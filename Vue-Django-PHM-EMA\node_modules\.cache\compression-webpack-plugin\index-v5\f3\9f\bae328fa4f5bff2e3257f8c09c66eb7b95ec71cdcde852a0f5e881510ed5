
22ced346b830d7e603268b80e895aac18ff47e9d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"deafeb1d881060d60ca47defaf903e18\"}","integrity":"sha512-sR7el3P58xBVQJxjXbLH5OOhKREXGynXsMbFnTtSHvKvw56NYgHWs2XyASw4cz3MlKuaTUJnQghG13bG/ssipw==","time":1754200994170,"size":378940}