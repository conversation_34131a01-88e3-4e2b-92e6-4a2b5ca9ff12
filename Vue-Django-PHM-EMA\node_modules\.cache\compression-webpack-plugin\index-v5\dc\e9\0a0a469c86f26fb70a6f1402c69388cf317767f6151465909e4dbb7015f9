
967b6c3b6152e5858165be8f2d94e61e495be67f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"94284de82159798c694869f337bedddb\"}","integrity":"sha512-NUzuWW+njDs0LMbvtrV6LyY1g1bTOyaQ5meWfAHp15Iy0uVuT1mxXwu+XrjZ9gRXpyvOIHNwum+41h7e9CIPMw==","time":1754202879708,"size":23538}