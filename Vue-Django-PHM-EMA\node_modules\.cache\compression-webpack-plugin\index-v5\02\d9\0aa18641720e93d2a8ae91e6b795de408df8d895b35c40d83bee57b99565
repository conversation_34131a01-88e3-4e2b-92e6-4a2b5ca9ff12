
c0f5fa6521547a368444ffb0fa44fb1347bf06ea	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"253d73b5431cf5df79e5ede3f9a9add9\"}","integrity":"sha512-+yV5xixzaGOhGLgO/kN6grVUVepX3iOvrc1x3x5noP8XiXmoKOPdxIRGDBcKGsCql/QG1+KuaT41eQt6JiLYQQ==","time":1754204881931,"size":23539}