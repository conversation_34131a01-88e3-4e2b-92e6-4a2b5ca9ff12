
824af97bb678430797965c44ab22af4003ba0e0d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"d6c1accd9a802da4bf8be8eeb47dec5b\"}","integrity":"sha512-UMg2fSnOgdP87utWc2d4lLW2JvPrW+eEnrzvSuQ0ONpbQu+BAlmemwWLvvUhJudSdMfffd0ETTFu7O9aujK+Aw==","time":1754205498625,"size":23503}