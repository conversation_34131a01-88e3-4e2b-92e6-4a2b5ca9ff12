// ===== 现代化主题样式 =====
@import './variables.scss';

// ===== 全局现代化样式 =====
.modern-theme {
  // 全局背景渐变
  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);
  min-height: 100vh;
  color: $textPrimary;
  
  // 科技感粒子背景
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(255, 107, 53, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }
}

// ===== 现代化卡片样式 =====
.modern-card {
  background: $bgCard;
  backdrop-filter: blur(10px);
  border: 1px solid $borderPrimary;
  border-radius: 12px;
  box-shadow: $shadowPrimary;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &:hover {
    border-color: $borderHover;
    box-shadow: $shadowHover;
    transform: translateY(-2px);
  }
  
  .card-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid $borderSecondary;
    
    .card-title {
      color: $textPrimary;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        width: 4px;
        height: 18px;
        background: linear-gradient(135deg, $techBlue, $techBlueLight);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  
  .card-content {
    padding: 24px;
  }
}

// ===== 现代化按钮样式 =====
.modern-button {
  background: linear-gradient(135deg, $techBlue, $techBlueDark);
  border: none;
  border-radius: 8px;
  color: $textPrimary;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
    
    &::before {
      left: 100%;
    }
  }
  
  &.danger {
    background: linear-gradient(135deg, $dangerColor, #e84393);
  }
  
  &.success {
    background: linear-gradient(135deg, $successColor, #00b894);
  }
  
  &.warning {
    background: linear-gradient(135deg, $warningColor, #e17055);
  }
}

// ===== 现代化表单样式 =====
.modern-form {
  .form-item {
    margin-bottom: 24px;
    
    .form-label {
      color: $textSecondary;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
    }
    
    .form-input {
      width: 100%;
      padding: 12px 16px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid $borderSecondary;
      border-radius: 8px;
      color: $textPrimary;
      font-size: 14px;
      transition: all 0.3s ease;
      
      &:focus {
        outline: none;
        border-color: $techBlue;
        box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
        background: rgba(255, 255, 255, 0.08);
      }
      
      &::placeholder {
        color: $textMuted;
      }
    }
  }
}

// ===== 状态指示器 =====
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
    animation: pulse 2s infinite;
  }
  
  &.online {
    background: rgba(0, 200, 150, 0.1);
    color: $successColor;
    border: 1px solid rgba(0, 200, 150, 0.3);
    
    &::before {
      background: $successColor;
    }
  }
  
  &.offline {
    background: rgba(255, 71, 87, 0.1);
    color: $dangerColor;
    border: 1px solid rgba(255, 71, 87, 0.3);
    
    &::before {
      background: $dangerColor;
    }
  }
  
  &.warning {
    background: rgba(255, 107, 53, 0.1);
    color: $warningColor;
    border: 1px solid rgba(255, 107, 53, 0.3);
    
    &::before {
      background: $warningColor;
    }
  }
}

// ===== 数据指标卡片 =====
.metric-card {
  background: $bgCard;
  backdrop-filter: blur(10px);
  border: 1px solid $borderPrimary;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  height: 160px; // 统一卡片高度
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &:hover {
    border-color: $borderHover;
    transform: translateY(-2px);
  }

  .metric-header {
    display: flex;
    justify-content: flex-start; // 只显示标题，左对齐
    align-items: center;
    margin-bottom: 16px;

    .metric-title {
      color: $textPrimary; // 改为主要文本色，更突出
      font-size: 16px; // 增大字体
      font-weight: 600; // 增加字重
      margin: 0;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); // 添加文字阴影增强可读性
    }
  }

  .metric-value {
    display: flex;
    align-items: baseline;
    margin-bottom: 16px;
    flex: 1;
    align-items: center; // 垂直居中

    .value {
      font-size: 32px;
      font-weight: 700;
      color: $textPrimary;
      line-height: 1;
    }

    .unit {
      font-size: 16px;
      color: $textMuted;
      margin-left: 4px;
    }
  }

  .metric-chart {
    height: 60px;
    // 图表容器样式
  }
}

// ===== 动画效果 =====
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// ===== 页面过渡动画 =====
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// ===== 响应式设计 =====
@media (max-width: 768px) {
  .modern-card {
    margin: 8px;
    border-radius: 8px;
    
    .card-content {
      padding: 16px;
    }
  }
  
  .metric-card {
    padding: 16px;
    
    .metric-value .value {
      font-size: 24px;
    }
  }
}
