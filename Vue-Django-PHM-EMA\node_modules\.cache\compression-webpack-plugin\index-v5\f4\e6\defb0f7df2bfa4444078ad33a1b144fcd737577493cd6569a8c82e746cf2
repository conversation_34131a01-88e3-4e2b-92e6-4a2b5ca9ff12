
0114c90150e49d347feb791b58ae658731fe81e3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F5.js\",\"contentHash\":\"189b041b6e513f91bbe78f846da24425\"}","integrity":"sha512-ncpkkgAma9SN5xI7ozF2NH1Q4TWAUMzwKziOJn/7n7L6TfOsA92+XngFf7fyKf2PlOKn0QMXOGo/yjqrbDoHbw==","time":1754206072122,"size":110598}