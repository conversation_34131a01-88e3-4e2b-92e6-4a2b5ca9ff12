
4cd621e69cc823a730bb37f0edfb7618efb479a2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.7f343c41ac77da53c5bd.hot-update.js\",\"contentHash\":\"dd26ece9846b0f03f8387fb3f3844d4c\"}","integrity":"sha512-MXKeAk9v5V/sxJS5fJg/eY+gwSRLkHNGb06h3+2KcukjY+HzYRjBz+eh9+5YL4vW2anU1vR+oD/FZli2BTftmA==","time":1754202867243,"size":84499}