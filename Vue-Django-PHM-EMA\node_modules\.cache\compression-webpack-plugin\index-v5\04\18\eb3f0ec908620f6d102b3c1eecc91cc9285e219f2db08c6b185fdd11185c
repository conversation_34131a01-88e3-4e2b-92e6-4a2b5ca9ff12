
be84c72b8dafec7b8e90ae3bf5ebe462b8e1132b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"f62a4a97c1d202f3823acf707075749e\"}","integrity":"sha512-BRlQS7KlvztI5J6Ybo+dsjTMQpRhB8dNuXr3vM8blYXFgQzelKQ9c1mJ4Q1f/Kvg+lkPtlXAgQMjd85LUL0Wtw==","time":1754204347206,"size":26703}