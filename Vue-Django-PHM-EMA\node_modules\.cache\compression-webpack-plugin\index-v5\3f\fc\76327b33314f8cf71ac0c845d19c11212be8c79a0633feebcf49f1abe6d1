
ff305107edfea827c97f86be9d9b882ff5f44794	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"3a63c34be1c3cfb04defef5011e3fc77\"}","integrity":"sha512-OkpkXfeTs1qSFD1z7B0CSikvf1SHtoMX5Z6bNkkyhzWlO6bTuvQtcXvqD7vAQYba3VgiuG00T3Mal8jqJ+AZuQ==","time":1754206071754,"size":26750}