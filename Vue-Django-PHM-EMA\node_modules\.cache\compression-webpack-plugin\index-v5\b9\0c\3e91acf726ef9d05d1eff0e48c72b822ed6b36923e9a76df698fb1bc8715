
f6e7a477334d2f725711f48221fc098c28824f08	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"7c8c345339707a5697fa73e82c3a34e1\"}","integrity":"sha512-tZZrmJE9sW5wdCtlxPxx/Z5vsa6DPkh+3AlYQf7IplrxTXpL4XQAyTwLUcQzpJ0cxHtvx4JnG6eFjGfIB4Cq8w==","time":1754204006599,"size":26682}