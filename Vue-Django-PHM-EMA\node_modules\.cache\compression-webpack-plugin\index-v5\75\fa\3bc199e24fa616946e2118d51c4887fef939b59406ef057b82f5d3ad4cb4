
a85681a6cf61ceaed48f81ee7d4b7869c2c6ba9c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"5f02ed2bab1cb256060002ece634ee82\"}","integrity":"sha512-tpPQHb/fqr1kS42ruCcigH2zAdbNgnn5fQ2XzH59Zj/kkNFkX2FsnNKrxmVuatqaM3bJSSepeBSAvMn18WtaYQ==","time":1754202922687,"size":107645}