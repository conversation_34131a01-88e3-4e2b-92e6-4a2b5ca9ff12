
27c03063f38e4695767029b4eb0ae41b325b2766	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"5f02ed2bab1cb256060002ece634ee82\"}","integrity":"sha512-mv55zKwjeKpzXOaFBiUEW1J1CjepHVu7ZTBxXapNnbK7m07N1IyWTsex9iXvr+U9ZVHsLvq0KIR/yiipjQscvw==","time":1754202922251,"size":138901}