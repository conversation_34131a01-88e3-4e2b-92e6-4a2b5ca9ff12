
1119ffaed6842dac35edccccb5799dbc8ae6288f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"26a4fabd9b7f92d8a3cb433244a53329\"}","integrity":"sha512-dNGD+NI9q2OGkzg8KaeLStr0XRCTEAXcMlVwHx+sSfwZ5VAf56sJOvq9TYmJrwSbeDyfGN4FfeN36hH3OhA5Zw==","time":1754204483186,"size":23493}