
888ca4051b1379eab320ba0f395e8403dd50bb75	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"64df4a6733c99a7ed833c970197a1fe4\"}","integrity":"sha512-5xTze2jDh6x3v6ubqm7gm2nqGFP9+erWMdYddd4adasnC69w9cXVEvI8Uebrfkeunf5LxcUoA9B7c3T9zzYmkg==","time":1754201860639,"size":26719}