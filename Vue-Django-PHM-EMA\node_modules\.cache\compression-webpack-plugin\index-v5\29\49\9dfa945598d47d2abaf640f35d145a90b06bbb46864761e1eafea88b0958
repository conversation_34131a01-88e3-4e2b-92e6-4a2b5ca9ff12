
768c5c74999cf555df28112f4b75f5d3c27e7fbb	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.5e5c89c825d67e86668f.hot-update.js\",\"contentHash\":\"c0c1fcde1654af0f5635e9e5b9f53811\"}","integrity":"sha512-XuI8CvMHFgBz0ZYOLkk/5Bq0YpQtk7hY1X9VA18/bvIKePAGfxbHk+JyuiqlvvD4Gq5IZgadniJXpFSmRWkwdQ==","time":1754202942643,"size":19282}