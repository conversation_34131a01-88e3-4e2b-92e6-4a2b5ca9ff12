{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\life\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\life\\index.vue", "mtime": 1754206070076}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycKaW1wb3J0IHsgZ2V0SGVhbHRoUHJlZGljdGlvbiwgZ2VuZXJhdGVIZWFsdGhSZXBvcnQgfSBmcm9tICdAL2FwaS9oZWFsdGgnCmltcG9ydCB7IHNhdmVBcyB9IGZyb20gJ2ZpbGUtc2F2ZXInCgpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGhlYWx0aFRyZW5kQ2hhcnQ6IG51bGwsCiAgICAgIGhlYWx0aEdhdWdlQ2hhcnQ6IG51bGwsCiAgICAgIGNvbXBvbmVudHNIZWFsdGhDaGFydDogbnVsbCwKICAgICAgcnVsVmFsdWU6ICcwJywKICAgICAgcnVsUGVyY2VudGFnZTogMCwKICAgICAgcnVsQ29sb3I6ICcjNDA5RUZGJywKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHJlcG9ydEdlbmVyYXRpbmc6IGZhbHNlLAogICAgICBoZWFsdGhEYXRhOiB7CiAgICAgICAgdHJlbmQ6IFtdLAogICAgICAgIGN1cnJlbnQ6IDAsCiAgICAgICAgY29tcG9uZW50czogW10KICAgICAgfQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgdGhpcy5pbml0Q2hhcnRzKCkKCiAgICAgIC8vIOajgOafpeaYr+WQpuacieS7juaVhemanOiviuaWremhtemdouS8oOadpeeahOaVsOaNrgogICAgICBpZiAodGhpcy4kc3RvcmUuZ2V0dGVyc1snZGlhZ25vc2lzL2hhc1VucHJvY2Vzc2VkUmVzdWx0J10pIHsKICAgICAgICB0aGlzLmhhbmRsZURpYWdub3Npc1Jlc3VsdCgpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5LuO5pWF6Zqc6K+K5pat6aG16Z2i5Lyg5p2l5pWw5o2u77yM5YiZ5pi+56S65Yid5aeL54q25oCBCiAgICAgICAgdGhpcy5yZXNldERhdGEoKQogICAgICB9CiAgICB9KQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnRzKQogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnJlc2l6ZUNoYXJ0cykKICAgIHRoaXMuaGVhbHRoVHJlbmRDaGFydCAmJiB0aGlzLmhlYWx0aFRyZW5kQ2hhcnQuZGlzcG9zZSgpCiAgICB0aGlzLmhlYWx0aEdhdWdlQ2hhcnQgJiYgdGhpcy5oZWFsdGhHYXVnZUNoYXJ0LmRpc3Bvc2UoKQogICAgdGhpcy5jb21wb25lbnRzSGVhbHRoQ2hhcnQgJiYgdGhpcy5jb21wb25lbnRzSGVhbHRoQ2hhcnQuZGlzcG9zZSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0Q2hhcnRzKCkgewogICAgICBjb25zb2xlLmxvZygn5Yid5aeL5YyW5Zu+6KGoLi4uJykKICAgICAgdGhpcy5oZWFsdGhUcmVuZENoYXJ0ID0gZWNoYXJ0cy5pbml0KGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdoZWFsdGhUcmVuZENoYXJ0JykpCiAgICAgIHRoaXMuaGVhbHRoR2F1Z2VDaGFydCA9IGVjaGFydHMuaW5pdChkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnaGVhbHRoR2F1Z2VDaGFydCcpKQogICAgICB0aGlzLmNvbXBvbmVudHNIZWFsdGhDaGFydCA9IGVjaGFydHMuaW5pdChkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY29tcG9uZW50c0hlYWx0aENoYXJ0JykpCgogICAgICAvLyDorr7nva7ku6rooajnm5jlrrnlmajmoLflvI/vvIznoa7kv53ku6rooajnm5jlsYXkuK3mmL7npLoKICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2hlYWx0aEdhdWdlQ2hhcnQnKS5wYXJlbnROb2RlLnN0eWxlLmRpc3BsYXkgPSAnZmxleCcKICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2hlYWx0aEdhdWdlQ2hhcnQnKS5wYXJlbnROb2RlLnN0eWxlLmp1c3RpZnlDb250ZW50ID0gJ2NlbnRlcicKICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2hlYWx0aEdhdWdlQ2hhcnQnKS5wYXJlbnROb2RlLnN0eWxlLmFsaWduSXRlbXMgPSAnY2VudGVyJwoKICAgICAgdGhpcy51cGRhdGVDaGFydHMoKQogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVDaGFydHMpCiAgICB9LAogICAgZmV0Y2hEYXRhKGlzUmVzZXQgPSBmYWxzZSkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vojrflj5blgaXlurfor4TkvLDmlbDmja4uLi4gcmVzZXQ6JywgaXNSZXNldCkKCiAgICAgIC8vIOaehOmAoOivt+axguWPguaVsAogICAgICBjb25zdCBwYXJhbXMgPSB7fQoKICAgICAgLy8g5aaC5p6c5piv6YeN572u6K+35rGCCiAgICAgIGlmIChpc1Jlc2V0KSB7CiAgICAgICAgcGFyYW1zLnJlc2V0ID0gJ3RydWUnCiAgICAgIH0gZWxzZSBpZiAodGhpcy4kc3RvcmUuZ2V0dGVyc1snZGlhZ25vc2lzL2hhc1VucHJvY2Vzc2VkUmVzdWx0J10pIHsKICAgICAgICBjb25zdCBkaWFnbm9zaXNSZXN1bHQgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzWydkaWFnbm9zaXMvY3VycmVudERpYWdub3Npc1Jlc3VsdCddCiAgICAgICAgaWYgKGRpYWdub3Npc1Jlc3VsdCAmJiBkaWFnbm9zaXNSZXN1bHQuc3VjY2VzcykgewogICAgICAgICAgY29uc3QgZmF1bHRfbW9kZSA9IGRpYWdub3Npc1Jlc3VsdC5kaWFnbm9zaXNfZGV0YWlscy5jb25jbHVzaW9uLnByZWRpY3RlZF9mYXVsdF9tb2RlCiAgICAgICAgICBwYXJhbXMuZmF1bHRfbW9kZSA9IGZhdWx0X21vZGUKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOiwg+eUqEFQSeiOt+WPluWBpeW6t+ivhOS8sOWSjOWvv+WRvemihOa1i+aVsOaNrgogICAgICBnZXRIZWFsdGhQcmVkaWN0aW9uKHBhcmFtcykKICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZygnQVBJ6L+U5Zue5pWw5o2uOicsIHJlc3BvbnNlKQogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YQoKICAgICAgICAgICAgLy8g5pu05paw5YGl5bq35bqm6LaL5Yq/5pWw5o2uCiAgICAgICAgICAgIHRoaXMuaGVhbHRoRGF0YS50cmVuZCA9IGRhdGEuaGVhbHRoX3RyZW5kIHx8IFtdCgogICAgICAgICAgICAvLyDmm7TmlrDlvZPliY3lgaXlurfluqYKICAgICAgICAgICAgdGhpcy5oZWFsdGhEYXRhLmN1cnJlbnQgPSBkYXRhLmN1cnJlbnRfaGVhbHRoIHx8IDAKCiAgICAgICAgICAgIC8vIOabtOaWsOmDqOS7tuWBpeW6t+eKtuaAgeaVsOaNrgogICAgICAgICAgICB0aGlzLmhlYWx0aERhdGEuY29tcG9uZW50cyA9IGRhdGEuY29tcG9uZW50X2hlYWx0aCB8fCBbXQoKICAgICAgICAgICAgLy8g5pu05pawUlVM6aKE5rWL57uT5p6c77yM5bCG5bCP5pWw6IiN5YWl5Li65pW05pWwCiAgICAgICAgICAgIHRoaXMucnVsVmFsdWUgPSBkYXRhLnJ1bF9ob3VycyA/IE1hdGgucm91bmQocGFyc2VGbG9hdChkYXRhLnJ1bF9ob3VycykpIDogJzAnCiAgICAgICAgICAgIHRoaXMucnVsUGVyY2VudGFnZSA9IGRhdGEucnVsX3BlcmNlbnRhZ2UgPyBNYXRoLnJvdW5kKHBhcnNlRmxvYXQoZGF0YS5ydWxfcGVyY2VudGFnZSkpIDogMAoKICAgICAgICAgICAgLy8g5aaC5p6c5piv5LuO5pWF6Zqc6K+K5pat6aG16Z2i6Lez6L2s5p2l55qE77yM5qCH6K6w6K+K5pat57uT5p6c5bey5aSE55CGCiAgICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5nZXR0ZXJzWydkaWFnbm9zaXMvaGFzVW5wcm9jZXNzZWRSZXN1bHQnXSkgewogICAgICAgICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdkaWFnbm9zaXMvcHJvY2Vzc0RpYWdub3Npc1Jlc3VsdCcpCiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIHRoaXMudXBkYXRlQ2hhcnRzKCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSei/lOWbnumUmeivrzonLCByZXNwb25zZSkKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfojrflj5bmlbDmja7lpLHotKUnKQogICAgICAgICAgICAvLyDkuI3lho3osIPnlKhtb2NrRGF0Ye+8jOaUueS4uuaYvuekuuWIneWni+eKtuaAgQogICAgICAgICAgICB0aGlzLnJlc2V0RGF0YSgpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5YGl5bq36K+E5Lyw5pWw5o2u5aSx6LSlOicsIGVycm9yKQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5pWw5o2u5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJykKICAgICAgICAgIC8vIOaYvuekuuivpue7humUmeivr+S/oeaBrwogICAgICAgICAgY29uc29sZS5sb2coJ+mUmeivr+ivpuaDhTonLCB7CiAgICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsCiAgICAgICAgICAgIHN0YWNrOiBlcnJvci5zdGFjaywKICAgICAgICAgICAgcmVzcG9uc2U6IGVycm9yLnJlc3BvbnNlICYmIHsKICAgICAgICAgICAgICBzdGF0dXM6IGVycm9yLnJlc3BvbnNlLnN0YXR1cywKICAgICAgICAgICAgICBzdGF0dXNUZXh0OiBlcnJvci5yZXNwb25zZS5zdGF0dXNUZXh0LAogICAgICAgICAgICAgIGRhdGE6IGVycm9yLnJlc3BvbnNlLmRhdGEKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICAgIC8vIOS4jeWGjeiwg+eUqG1vY2tEYXRh77yM5pS55Li65pi+56S65Yid5aeL54q25oCBCiAgICAgICAgICB0aGlzLnJlc2V0RGF0YSgpCiAgICAgICAgfSkKICAgICAgICAuZmluYWxseSgoKSA9PiB7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICAgIH0pCiAgICB9LAogICAgLy8g5aSE55CG5LuO5pWF6Zqc6K+K5pat6aG16Z2i5Lyg5p2l55qE6K+K5pat57uT5p6cCiAgICBoYW5kbGVEaWFnbm9zaXNSZXN1bHQoKSB7CiAgICAgIGNvbnN0IGRpYWdub3Npc1Jlc3VsdCA9IHRoaXMuJHN0b3JlLmdldHRlcnNbJ2RpYWdub3Npcy9jdXJyZW50RGlhZ25vc2lzUmVzdWx0J10KICAgICAgY29uc29sZS5sb2coJ+WkhOeQhuaVhemanOiviuaWree7k+aenDonLCBkaWFnbm9zaXNSZXN1bHQpCgogICAgICBpZiAoZGlhZ25vc2lzUmVzdWx0ICYmIGRpYWdub3Npc1Jlc3VsdC5zdWNjZXNzKSB7CiAgICAgICAgLy8g6I635Y+W5pWF6Zqc5qih5byPCiAgICAgICAgY29uc3QgZmF1bHRfbW9kZSA9IGRpYWdub3Npc1Jlc3VsdC5kaWFnbm9zaXNfZGV0YWlscy5jb25jbHVzaW9uLnByZWRpY3RlZF9mYXVsdF9tb2RlCgogICAgICAgIC8vIOiOt+WPluWBpeW6t+ivhOS8sOe7k+aenAogICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkKCiAgICAgICAgLy8g5pi+56S66YCa55+lCiAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgIHRpdGxlOiAn6K+K5pat57uT5p6c5bqU55SoJywKICAgICAgICAgIG1lc3NhZ2U6IGDlt7LmoLnmja7or4rmlq3nu5PmnpwoJHt0aGlzLmdldEZhdWx0TW9kZU5hbWUoZmF1bHRfbW9kZSl9KeabtOaWsOWBpeW6t+ivhOS8sGAsCiAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICBkdXJhdGlvbjogNTAwMAogICAgICAgIH0pCiAgICAgIH0KICAgIH0sCiAgICAvLyDph43nva7mlbDmja7kuLrliJ3lp4vnirbmgIEKICAgIHJlc2V0RGF0YSgpIHsKICAgICAgdGhpcy5oZWFsdGhEYXRhLnRyZW5kID0gW10KICAgICAgdGhpcy5oZWFsdGhEYXRhLmN1cnJlbnQgPSAwCiAgICAgIHRoaXMuaGVhbHRoRGF0YS5jb21wb25lbnRzID0gWwogICAgICAgIHsgbmFtZTogJ+eUteacuicsIHZhbHVlOiAwIH0sCiAgICAgICAgeyBuYW1lOiAn5o6n5Yi25ZmoJywgdmFsdWU6IDAgfSwKICAgICAgICB7IG5hbWU6ICflh4/pgJ/lmagnLCB2YWx1ZTogMCB9LAogICAgICAgIHsgbmFtZTogJ+S8oOaEn+WZqCcsIHZhbHVlOiAwIH0KICAgICAgXQogICAgICB0aGlzLnJ1bFZhbHVlID0gJzAnIC8vIOW3sue7j+aYr+aVtOaVsAogICAgICB0aGlzLnJ1bFBlcmNlbnRhZ2UgPSAwCiAgICAgIHRoaXMudXBkYXRlQ2hhcnRzKCkKICAgIH0sCiAgICAvLyDojrflj5bmlYXpmpzmqKHlvI/lkI3np7AKICAgIGdldEZhdWx0TW9kZU5hbWUoZmF1bHRfbW9kZSkgewogICAgICAvLyDku47mlYXpmpzor4rmlq3nmoRmYXVsdE1vZGVNYXDojrflj5bmlYXpmpzlkI3np7AKICAgICAgY29uc3QgZmF1bHRNb2RlTWFwID0gewogICAgICAgICcwX25vcm1hbCc6ICfmraPluLjnirbmgIEnLAogICAgICAgICcxX2RlZ3JhZGF0aW9uX21hZ25ldCc6ICfmsLjno4HkvZPpgIDno4HpgIDljJYnLAogICAgICAgICcyX2RlZ3JhZGF0aW9uX2JydXNoX3dlYXInOiAn55S15Yi356Oo5o2f6YCA5YyWJywKICAgICAgICAnM19kZWdyYWRhdGlvbl9jb21tdXRhdG9yX294aWRhdGlvbic6ICfmjaLlkJHlmajmsKfljJbpgIDljJYnLAogICAgICAgICc0X2ZhdWx0X3N0YXRvcl9zaG9ydCc6ICflrprlrZDnu5Xnu4Tnn63ot6/mlYXpmpwnLAogICAgICAgICc1X2ZhdWx0X3JvdG9yX29wZW4nOiAn6L2s5a2Q57uV57uE5byA6Lev5pWF6ZqcJywKICAgICAgICAnNl9kZWdyYWRhdGlvbl9iZWFyaW5nX3dlYXInOiAn6L205om/56Oo5o2f6YCA5YyWJywKICAgICAgICAnN19mYXVsdF9iZWFyaW5nX3N0dWNrJzogJ+i9tOaJv+WNoeatu+aVhemanCcsCiAgICAgICAgJzhfZGVncmFkYXRpb25fZ2Vhcl93ZWFyJzogJ+m9v+i9ruejqOaNn+mAgOWMlicsCiAgICAgICAgJzlfZGVncmFkYXRpb25fc2Vuc29yX2RyaWZ0JzogJ+S8oOaEn+WZqOa8guenu+mAgOWMlicsCiAgICAgICAgJzEwX2ZhdWx0X3NlbnNvcl9sb3NzJzogJ+S8oOaEn+WZqOWkseaViOaVhemanCcsCiAgICAgICAgJzExX2ZhdWx0X21vc2ZldF9icmVha2Rvd24nOiAnTU9TRkVU5Ye756m/5pWF6ZqcJywKICAgICAgICAnMTJfZGVncmFkYXRpb25fZHJpdmVfZGlzdG9ydGlvbic6ICfpqbHliqjkv6Hlj7flpLHnnJ/pgIDljJYnLAogICAgICAgICcxM19mYXVsdF9tY3VfY3Jhc2gnOiAnTUNV5bSp5rqD5pWF6ZqcJwogICAgICB9CgogICAgICByZXR1cm4gZmF1bHRNb2RlTWFwW2ZhdWx0X21vZGVdIHx8ICfmnKrnn6XmlYXpmpwnCiAgICB9LAogICAgdXBkYXRlQ2hhcnRzKCkgewogICAgICAvLyDmm7TmlrDlgaXlurfluqbotovlir/lm77ooagKICAgICAgdGhpcy5oZWFsdGhUcmVuZENoYXJ0LnNldE9wdGlvbih7CiAgICAgICAgdGl0bGU6IHsKICAgICAgICAgIHRleHQ6ICcnCiAgICAgICAgfSwKICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycKICAgICAgICB9LAogICAgICAgIHhBeGlzOiB7CiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLAogICAgICAgICAgZGF0YTogdGhpcy5oZWFsdGhEYXRhLnRyZW5kLm1hcChpdGVtID0+IGl0ZW1bMF0pLAogICAgICAgICAgbmFtZTogJ+ajgOa1i+aXtumXtOeCuScsCiAgICAgICAgICBuYW1lTG9jYXRpb246ICdtaWRkbGUnLAogICAgICAgICAgbmFtZUdhcDogMzAsCiAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgaW50ZXJ2YWw6ICdhdXRvJywKICAgICAgICAgICAgcm90YXRlOiA0NSwKICAgICAgICAgICAgZm9ybWF0dGVyOiAodmFsdWUpID0+IHsKICAgICAgICAgICAgICAvLyDlj6rmmL7npLrmnIjku73lkozml6XmnJ/vvIzkuI3mmL7npLrlubTku70KICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodmFsdWUpCiAgICAgICAgICAgICAgaWYgKCFpc05hTihkYXRlKSkgewogICAgICAgICAgICAgICAgcmV0dXJuIGAke2RhdGUuZ2V0TW9udGgoKSArIDF95pyIJHtkYXRlLmdldERhdGUoKX3ml6VgCiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiB2YWx1ZQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICB5QXhpczogewogICAgICAgICAgdHlwZTogJ3ZhbHVlJywKICAgICAgICAgIG5hbWU6ICflgaXlurfluqYnLAogICAgICAgICAgbWluOiAwLAogICAgICAgICAgbWF4OiAxMDAKICAgICAgICB9LAogICAgICAgIGdyaWQ6IHsKICAgICAgICAgIGJvdHRvbTogJzE1JScgLy8g5Li65peL6L2s5ZCO55qE5qiq6L205qCH562+55WZ5Ye65pu05aSa56m66Ze0CiAgICAgICAgfSwKICAgICAgICBzZXJpZXM6IFt7CiAgICAgICAgICBuYW1lOiAn57O757uf5YGl5bq35bqmJywKICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgIGRhdGE6IHRoaXMuaGVhbHRoRGF0YS50cmVuZC5tYXAoaXRlbSA9PiBpdGVtWzFdKSwKICAgICAgICAgIG1hcmtMaW5lOiB7CiAgICAgICAgICAgIGRhdGE6IFt7CiAgICAgICAgICAgICAgeUF4aXM6IDYwLAogICAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGRjk4MDAnIH0sCiAgICAgICAgICAgICAgbGFiZWw6IHsgZm9ybWF0dGVyOiAn6K2m5oiS57q/JyB9CiAgICAgICAgICAgIH1dCiAgICAgICAgICB9LAogICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgIHdpZHRoOiAzLAogICAgICAgICAgICBjb2xvcjogJyM0MDlFRkYnCiAgICAgICAgICB9LAogICAgICAgICAgc21vb3RoOiB0cnVlCiAgICAgICAgfV0KICAgICAgfSkKCiAgICAgIC8vIOabtOaWsOWBpeW6t+W6puS7quihqOebmAogICAgICB0aGlzLmhlYWx0aEdhdWdlQ2hhcnQuc2V0T3B0aW9uKHsKICAgICAgICBzZXJpZXM6IFt7CiAgICAgICAgICB0eXBlOiAnZ2F1Z2UnLAogICAgICAgICAgZGF0YTogW3sgdmFsdWU6IHRoaXMuaGVhbHRoRGF0YS5jdXJyZW50LCBuYW1lOiAn5YGl5bq35bqmJyB9XSwKICAgICAgICAgIG1pbjogMCwKICAgICAgICAgIG1heDogMTAwLAogICAgICAgICAgYXhpc0xpbmU6IHsKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgd2lkdGg6IDMwLAogICAgICAgICAgICAgIGNvbG9yOiBbCiAgICAgICAgICAgICAgICBbMC4zLCAnI0ZGNTcyMiddLAogICAgICAgICAgICAgICAgWzAuNywgJyNGRjk4MDAnXSwKICAgICAgICAgICAgICAgIFsxLCAnIzRDQUY1MCddCiAgICAgICAgICAgICAgXQogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgcmFkaXVzOiAnODUlJywgLy8g57yp5bCP5Luq6KGo55uY5Y2K5b6E77yM5Li65Yi75bqm5qCH562+55WZ5Ye65pu05aSa56m66Ze0CgogICAgICAgICAgZGV0YWlsOiB7CiAgICAgICAgICAgIGZvcm1hdHRlcjogJ3t2YWx1ZX0lJywKICAgICAgICAgICAgZm9udFNpemU6IDI4LAogICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsCiAgICAgICAgICAgIG9mZnNldENlbnRlcjogWzAsICc3MCUnXSwgLy8g5bCG5pWw5YC85pS+5Zyo5LiL5pa5CiAgICAgICAgICAgIGNvbG9yOiAnIzMzMycsIC8vIOS9v+eUqOabtOa3seeahOminOiJsuaPkOmrmOWvueavlOW6pgogICAgICAgICAgICB0ZXh0U2hhZG93OiAnMCAwIDNweCByZ2JhKDI1NSwyNTUsMjU1LDAuNSknIC8vIOa3u+WKoOaWh+Wtl+mYtOW9seaPkOmrmOWPr+ivu+aApwogICAgICAgICAgfSwKICAgICAgICAgIHRpdGxlOiB7CiAgICAgICAgICAgIGZvbnRTaXplOiAxOCwKICAgICAgICAgICAgZm9udFdlaWdodDogJ25vcm1hbCcsCiAgICAgICAgICAgIG9mZnNldENlbnRlcjogWzAsICc1MCUnXSwgLy8g5bCG5qCH6aKY5pS+5Zyo5pWw5YC85LiK5pa5CiAgICAgICAgICAgIGNvbG9yOiAnIzMzMycKICAgICAgICAgIH0sCiAgICAgICAgICBwb2ludGVyOiB7CiAgICAgICAgICAgIHdpZHRoOiA2LCAvLyDlop7liqDmjIfpkojlrr3luqbmj5Dpq5jlj6/op4HmgKcKICAgICAgICAgICAgbGVuZ3RoOiAnNzUlJyAvLyDnqI3lvq7nvKnnn63mjIfpkojvvIzpgb/lhY3kuI7liLvluqbph43lj6AKICAgICAgICAgIH0sCiAgICAgICAgICBheGlzVGljazogewogICAgICAgICAgICBsZW5ndGg6IDYsIC8vIOWHj+Wwj+WIu+W6pue6v+mVv+W6pu+8jOmBv+WFjeS4juaMh+mSiOmHjeWPoAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICB3aWR0aDogMiwgLy8g5L+d5oyB5Yi75bqm57q/5a695bqmCiAgICAgICAgICAgICAgY29sb3I6ICcjNjY2JyAvLyDliqDmt7HliLvluqbnur/popzoibIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGlzdGFuY2U6IC04IC8vIOWQkeWGheWBj+enu+WIu+W6pue6v++8jOehruS/neS4jeS4juWIu+W6puaVsOWtl+mHjeWPoAogICAgICAgICAgfSwKICAgICAgICAgIHNwbGl0TGluZTogewogICAgICAgICAgICBsZW5ndGg6IDE4LCAvLyDlop7liqDliIblibLnur/plb/luqbvvIzorqnliLvluqbmoIfnrb7kuI7liIblibLnur/mnInmm7TmmI7mmL7nmoTliIbpmpQKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgd2lkdGg6IDMsIC8vIOWinuWKoOWIhuWJsue6v+WuveW6pgogICAgICAgICAgICAgIGNvbG9yOiAnIzY2NicgLy8g5Yqg5rex5YiG5Ymy57q/6aKc6ImyCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGRpc3RhbmNlOiAtMyAvLyDlkJHlhoXlgY/np7vliIblibLnur/vvIzov5znprvliLvluqbmlbDlrZcKICAgICAgICAgIH0sCiAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgZGlzdGFuY2U6IDMwLCAvLyDov5vkuIDmraXlop7liqDmoIfnrb7kuI7liLvluqbnur/nmoTot53nprsKICAgICAgICAgICAgZm9udFNpemU6IDEyLCAvLyDnqI3lvq7lh4/lsI/lrZfkvZPlpKflsI/ku6Xlh4/lsJHph43lj6AKICAgICAgICAgICAgY29sb3I6ICcjMzMzJywgLy8g5Yqg5rex5paH5a2X6aKc6Imy5o+Q6auY5a+55q+U5bqmCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsKICAgICAgICAgICAgICAvLyDlh4/lsJHliLvluqbmmL7npLrvvIzlj6rmmL7npLow44CBMjDjgIE0MOOAgTYw44CBODDjgIExMDDnmoTliLvluqblgLwKICAgICAgICAgICAgICBpZiAodmFsdWUgJSAyMCA9PT0gMCkgewogICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvRml4ZWQoMCkgKyAnJwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICByZXR1cm4gJycgLy8g5YW25LuW5Yi75bqm5LiN5pi+56S65pWw5a2XCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LAogICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCknLCAvLyDmt7vliqDljYrpgI/mmI7og4zmma/oibIKICAgICAgICAgICAgcGFkZGluZzogWzIsIDRdLCAvLyDmt7vliqDlhoXovrnot50KICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAzLCAvLyDlnIbop5LovrnmoYYKICAgICAgICAgICAgdGV4dFNoYWRvdzogJzAgMCAycHggI2ZmZicgLy8g5re75Yqg5paH5a2X6Zi05b2x5o+Q6auY5Y+v6K+75oCnCiAgICAgICAgICB9CiAgICAgICAgfV0KICAgICAgfSkKCiAgICAgIC8vIOabtOaWsOmDqOS7tuWBpeW6t+eKtuaAgeafseeKtuWbvgogICAgICB0aGlzLmNvbXBvbmVudHNIZWFsdGhDaGFydC5zZXRPcHRpb24oewogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgIHR5cGU6ICdzaGFkb3cnCiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICB4QXhpczogewogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAgIGRhdGE6IHRoaXMuaGVhbHRoRGF0YS5jb21wb25lbnRzLm1hcChpdGVtID0+IGl0ZW0ubmFtZSkKICAgICAgICB9LAogICAgICAgIHlBeGlzOiB7CiAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgbmFtZTogJ+WBpeW6t+W6picsCiAgICAgICAgICBtaW46IDAsCiAgICAgICAgICBtYXg6IDEwMAogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbewogICAgICAgICAgdHlwZTogJ2JhcicsCiAgICAgICAgICBkYXRhOiB0aGlzLmhlYWx0aERhdGEuY29tcG9uZW50cy5tYXAoaXRlbSA9PiAoewogICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6IHRoaXMuZ2V0SGVhbHRoQ29sb3IoaXRlbS52YWx1ZSkKICAgICAgICAgICAgfQogICAgICAgICAgfSkpLAogICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAgICAgcG9zaXRpb246ICd0b3AnLAogICAgICAgICAgICBmb3JtYXR0ZXI6ICd7Y30lJwogICAgICAgICAgfQogICAgICAgIH1dCiAgICAgIH0pCiAgICB9LAogICAgZ2V0SGVhbHRoQ29sb3IodmFsdWUpIHsKICAgICAgaWYgKHZhbHVlID49IDgwKSB7CiAgICAgICAgcmV0dXJuICcjNENBRjUwJwogICAgICB9IGVsc2UgaWYgKHZhbHVlID49IDYwKSB7CiAgICAgICAgcmV0dXJuICcjRkY5ODAwJwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnI0ZGNTcyMicKICAgICAgfQogICAgfSwKICAgIHJlc2l6ZUNoYXJ0cygpIHsKICAgICAgdGhpcy5oZWFsdGhUcmVuZENoYXJ0ICYmIHRoaXMuaGVhbHRoVHJlbmRDaGFydC5yZXNpemUoKQogICAgICB0aGlzLmhlYWx0aEdhdWdlQ2hhcnQgJiYgdGhpcy5oZWFsdGhHYXVnZUNoYXJ0LnJlc2l6ZSgpCiAgICAgIHRoaXMuY29tcG9uZW50c0hlYWx0aENoYXJ0ICYmIHRoaXMuY29tcG9uZW50c0hlYWx0aENoYXJ0LnJlc2l6ZSgpCiAgICB9LAogICAgZm9ybWF0KHBlcmNlbnRhZ2UpIHsKICAgICAgcmV0dXJuIHBlcmNlbnRhZ2UgKyAnJScKICAgIH0sCiAgICBnZW5lcmF0ZVJlcG9ydCgpIHsKICAgICAgdGhpcy5yZXBvcnRHZW5lcmF0aW5nID0gdHJ1ZQogICAgICBjb25zb2xlLmxvZygn5byA5aeL55Sf5oiQ5YGl5bq36K+E5Lyw5oql5ZGKLi4uJykKCiAgICAgIC8vIOiwg+eUqEFQSeeUn+aIkOWBpeW6t+ivhOS8sOaKpeWRigogICAgICBnZW5lcmF0ZUhlYWx0aFJlcG9ydCh7CiAgICAgICAgaGVhbHRoX2RhdGE6IHsKICAgICAgICAgIGN1cnJlbnRfaGVhbHRoOiB0aGlzLmhlYWx0aERhdGEuY3VycmVudCwKICAgICAgICAgIGNvbXBvbmVudF9oZWFsdGg6IHRoaXMuaGVhbHRoRGF0YS5jb21wb25lbnRzLAogICAgICAgICAgcnVsX2hvdXJzOiB0aGlzLnJ1bFZhbHVlLAogICAgICAgICAgcnVsX3BlcmNlbnRhZ2U6IHRoaXMucnVsUGVyY2VudGFnZQogICAgICAgIH0KICAgICAgfSkKICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZygn5oql5ZGK55Sf5oiQ5oiQ5YqfLCDlk43lupTnsbvlnos6JywgcmVzcG9uc2UuaGVhZGVyc1snY29udGVudC10eXBlJ10pCiAgICAgICAgICAvLyDkvb/nlKhmaWxlLXNhdmVy5bqT5bCG5LqM6L+b5Yi25pWw5o2u5L+d5a2Y5Li6RXhjZWzmlofku7YKICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbcmVzcG9uc2UuZGF0YV0sIHsKICAgICAgICAgICAgdHlwZTogJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JwogICAgICAgICAgfSkKCiAgICAgICAgICAvLyDnlJ/miJDmlofku7blkI3vvJrlgaXlurfor4TkvLDmiqXlkYpf5bm05pyI5pel5pe25YiG56eSLnhsc3gKICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCkKICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gYOWBpeW6t+ivhOS8sOaKpeWRil8ke25vdy5nZXRGdWxsWWVhcigpfSR7U3RyaW5nKG5vdy5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKX0ke1N0cmluZyhub3cuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpfSR7U3RyaW5nKG5vdy5nZXRIb3VycygpKS5wYWRTdGFydCgyLCAnMCcpfSR7U3RyaW5nKG5vdy5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJyl9JHtTdHJpbmcobm93LmdldFNlY29uZHMoKSkucGFkU3RhcnQoMiwgJzAnKX0ueGxzeGAKCiAgICAgICAgICBzYXZlQXMoYmxvYiwgZmlsZU5hbWUpCgogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflgaXlurfor4TkvLDmiqXlkYrnlJ/miJDmiJDlip8nKQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKGVycm9yID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+eUn+aIkOWBpeW6t+ivhOS8sOaKpeWRiuWksei0pTonLCBlcnJvcikKICAgICAgICAgIC8vIOaYvuekuuivpue7humUmeivr+S/oeaBrwogICAgICAgICAgY29uc29sZS5sb2coJ+mUmeivr+ivpuaDhTonLCB7CiAgICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsCiAgICAgICAgICAgIHN0YWNrOiBlcnJvci5zdGFjaywKICAgICAgICAgICAgcmVzcG9uc2U6IGVycm9yLnJlc3BvbnNlICYmIHsKICAgICAgICAgICAgICBzdGF0dXM6IGVycm9yLnJlc3BvbnNlLnN0YXR1cywKICAgICAgICAgICAgICBzdGF0dXNUZXh0OiBlcnJvci5yZXNwb25zZS5zdGF0dXNUZXh0LAogICAgICAgICAgICAgIGRhdGE6IGVycm9yLnJlc3BvbnNlLmRhdGEKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+eUn+aIkOaKpeWRiuWksei0pe+8jOivt+eojeWQjumHjeivlScpCiAgICAgICAgfSkKICAgICAgICAuZmluYWxseSgoKSA9PiB7CiAgICAgICAgICB0aGlzLnJlcG9ydEdlbmVyYXRpbmcgPSBmYWxzZQogICAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/life", "sourcesContent": ["<template>\n  <div class=\"life-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-data-analysis\"></i>\n        寿命预测与健康评估\n        <span class=\"page-subtitle\">Life Prediction & Health Assessment</span>\n      </h2>\n    </div>\n\n    <!-- 健康度展示区域 -->\n    <el-row :gutter=\"20\" class=\"dashboard-row\">\n      <el-col :span=\"16\">\n        <el-card class=\"health-trend-card\">\n          <div slot=\"header\">\n            <span>系统健康度趋势</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"healthTrendChart\" class=\"chart\" />\n            <div v-if=\"!healthData.trend || healthData.trend.length === 0\" class=\"empty-content\">\n              <i class=\"el-icon-data-line\"></i>\n              <p>暂无健康度趋势数据</p>\n              <p class=\"tip\">请先进行故障诊断</p>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"8\">\n        <el-card class=\"gauge-card\">\n          <div slot=\"header\">\n            <span>当前健康度</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"healthGaugeChart\" class=\"chart\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" class=\"dashboard-row\">\n      <el-col :span=\"12\">\n        <!-- RUL预测展示 -->\n        <el-card class=\"rul-card\">\n          <div slot=\"header\">\n            <span>剩余使用寿命预测</span>\n          </div>\n          <div v-loading=\"loading\" class=\"rul-content\">\n            <h2 class=\"rul-value\">{{ rulValue }} 小时</h2>\n            <el-progress :percentage=\"rulPercentage\" :format=\"format\" :color=\"rulColor\"></el-progress>\n            <p class=\"rul-description\">预计剩余使用寿命，基于当前系统运行状态</p>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"12\">\n        <!-- 部件健康状态 -->\n        <el-card class=\"components-card\">\n          <div slot=\"header\">\n            <span>关键部件健康状态</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"componentsHealthChart\" class=\"chart\" />\n            <div v-if=\"!healthData.components || healthData.components.length === 0\" class=\"empty-content\">\n              <i class=\"el-icon-data-analysis\"></i>\n              <p>暂无部件健康状态数据</p>\n              <p class=\"tip\">请先进行故障诊断</p>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 报告生成按钮 -->\n    <div class=\"actions-container\">\n      <el-button type=\"primary\" icon=\"el-icon-document\" :loading=\"reportGenerating\" @click=\"generateReport\">\n        生成健康评估报告\n      </el-button>\n      <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"fetchData(true)\">\n        刷新数据\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport { getHealthPrediction, generateHealthReport } from '@/api/health'\nimport { saveAs } from 'file-saver'\n\nexport default {\n  data() {\n    return {\n      healthTrendChart: null,\n      healthGaugeChart: null,\n      componentsHealthChart: null,\n      rulValue: '0',\n      rulPercentage: 0,\n      rulColor: '#409EFF',\n      loading: false,\n      reportGenerating: false,\n      healthData: {\n        trend: [],\n        current: 0,\n        components: []\n      }\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initCharts()\n\n      // 检查是否有从故障诊断页面传来的数据\n      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        this.handleDiagnosisResult()\n      } else {\n        // 如果没有从故障诊断页面传来数据，则显示初始状态\n        this.resetData()\n      }\n    })\n    window.addEventListener('resize', this.resizeCharts)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.resizeCharts)\n    this.healthTrendChart && this.healthTrendChart.dispose()\n    this.healthGaugeChart && this.healthGaugeChart.dispose()\n    this.componentsHealthChart && this.componentsHealthChart.dispose()\n  },\n  methods: {\n    initCharts() {\n      console.log('初始化图表...')\n      this.healthTrendChart = echarts.init(document.getElementById('healthTrendChart'))\n      this.healthGaugeChart = echarts.init(document.getElementById('healthGaugeChart'))\n      this.componentsHealthChart = echarts.init(document.getElementById('componentsHealthChart'))\n\n      // 设置仪表盘容器样式，确保仪表盘居中显示\n      document.getElementById('healthGaugeChart').parentNode.style.display = 'flex'\n      document.getElementById('healthGaugeChart').parentNode.style.justifyContent = 'center'\n      document.getElementById('healthGaugeChart').parentNode.style.alignItems = 'center'\n\n      this.updateCharts()\n      window.addEventListener('resize', this.resizeCharts)\n    },\n    fetchData(isReset = false) {\n      this.loading = true\n      console.log('开始获取健康评估数据... reset:', isReset)\n\n      // 构造请求参数\n      const params = {}\n\n      // 如果是重置请求\n      if (isReset) {\n        params.reset = 'true'\n      } else if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        const diagnosisResult = this.$store.getters['diagnosis/currentDiagnosisResult']\n        if (diagnosisResult && diagnosisResult.success) {\n          const fault_mode = diagnosisResult.diagnosis_details.conclusion.predicted_fault_mode\n          params.fault_mode = fault_mode\n        }\n      }\n\n      // 调用API获取健康评估和寿命预测数据\n      getHealthPrediction(params)\n        .then(response => {\n          console.log('API返回数据:', response)\n          if (response.code === 200) {\n            const data = response.data\n\n            // 更新健康度趋势数据\n            this.healthData.trend = data.health_trend || []\n\n            // 更新当前健康度\n            this.healthData.current = data.current_health || 0\n\n            // 更新部件健康状态数据\n            this.healthData.components = data.component_health || []\n\n            // 更新RUL预测结果，将小数舍入为整数\n            this.rulValue = data.rul_hours ? Math.round(parseFloat(data.rul_hours)) : '0'\n            this.rulPercentage = data.rul_percentage ? Math.round(parseFloat(data.rul_percentage)) : 0\n\n            // 如果是从故障诊断页面跳转来的，标记诊断结果已处理\n            if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n              this.$store.dispatch('diagnosis/processDiagnosisResult')\n            }\n\n            this.updateCharts()\n          } else {\n            console.error('API返回错误:', response)\n            this.$message.error(response.message || '获取数据失败')\n            // 不再调用mockData，改为显示初始状态\n            this.resetData()\n          }\n        })\n        .catch(error => {\n          console.error('获取健康评估数据失败:', error)\n          this.$message.error('获取数据失败，请稍后重试')\n          // 显示详细错误信息\n          console.log('错误详情:', {\n            message: error.message,\n            stack: error.stack,\n            response: error.response && {\n              status: error.response.status,\n              statusText: error.response.statusText,\n              data: error.response.data\n            }\n          })\n          // 不再调用mockData，改为显示初始状态\n          this.resetData()\n        })\n        .finally(() => {\n          this.loading = false\n        })\n    },\n    // 处理从故障诊断页面传来的诊断结果\n    handleDiagnosisResult() {\n      const diagnosisResult = this.$store.getters['diagnosis/currentDiagnosisResult']\n      console.log('处理故障诊断结果:', diagnosisResult)\n\n      if (diagnosisResult && diagnosisResult.success) {\n        // 获取故障模式\n        const fault_mode = diagnosisResult.diagnosis_details.conclusion.predicted_fault_mode\n\n        // 获取健康评估结果\n        this.fetchData()\n\n        // 显示通知\n        this.$notify({\n          title: '诊断结果应用',\n          message: `已根据诊断结果(${this.getFaultModeName(fault_mode)})更新健康评估`,\n          type: 'info',\n          duration: 5000\n        })\n      }\n    },\n    // 重置数据为初始状态\n    resetData() {\n      this.healthData.trend = []\n      this.healthData.current = 0\n      this.healthData.components = [\n        { name: '电机', value: 0 },\n        { name: '控制器', value: 0 },\n        { name: '减速器', value: 0 },\n        { name: '传感器', value: 0 }\n      ]\n      this.rulValue = '0' // 已经是整数\n      this.rulPercentage = 0\n      this.updateCharts()\n    },\n    // 获取故障模式名称\n    getFaultModeName(fault_mode) {\n      // 从故障诊断的faultModeMap获取故障名称\n      const faultModeMap = {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n\n      return faultModeMap[fault_mode] || '未知故障'\n    },\n    updateCharts() {\n      // 更新健康度趋势图表\n      this.healthTrendChart.setOption({\n        title: {\n          text: ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: this.healthData.trend.map(item => item[0]),\n          name: '检测时间点',\n          nameLocation: 'middle',\n          nameGap: 30,\n          axisLabel: {\n            interval: 'auto',\n            rotate: 45,\n            formatter: (value) => {\n              // 只显示月份和日期，不显示年份\n              const date = new Date(value)\n              if (!isNaN(date)) {\n                return `${date.getMonth() + 1}月${date.getDate()}日`\n              }\n              return value\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '健康度',\n          min: 0,\n          max: 100\n        },\n        grid: {\n          bottom: '15%' // 为旋转后的横轴标签留出更多空间\n        },\n        series: [{\n          name: '系统健康度',\n          type: 'line',\n          data: this.healthData.trend.map(item => item[1]),\n          markLine: {\n            data: [{\n              yAxis: 60,\n              lineStyle: { color: '#FF9800' },\n              label: { formatter: '警戒线' }\n            }]\n          },\n          lineStyle: {\n            width: 3,\n            color: '#409EFF'\n          },\n          smooth: true\n        }]\n      })\n\n      // 更新健康度仪表盘\n      this.healthGaugeChart.setOption({\n        series: [{\n          type: 'gauge',\n          data: [{ value: this.healthData.current, name: '健康度' }],\n          min: 0,\n          max: 100,\n          axisLine: {\n            lineStyle: {\n              width: 30,\n              color: [\n                [0.3, '#FF5722'],\n                [0.7, '#FF9800'],\n                [1, '#4CAF50']\n              ]\n            }\n          },\n          radius: '85%', // 缩小仪表盘半径，为刻度标签留出更多空间\n\n          detail: {\n            formatter: '{value}%',\n            fontSize: 28,\n            fontWeight: 'bold',\n            offsetCenter: [0, '70%'], // 将数值放在下方\n            color: '#333', // 使用更深的颜色提高对比度\n            textShadow: '0 0 3px rgba(255,255,255,0.5)' // 添加文字阴影提高可读性\n          },\n          title: {\n            fontSize: 18,\n            fontWeight: 'normal',\n            offsetCenter: [0, '50%'], // 将标题放在数值上方\n            color: '#333'\n          },\n          pointer: {\n            width: 6, // 增加指针宽度提高可见性\n            length: '75%' // 稍微缩短指针，避免与刻度重叠\n          },\n          axisTick: {\n            length: 6, // 减小刻度线长度，避免与指针重叠\n            lineStyle: {\n              width: 2, // 保持刻度线宽度\n              color: '#666' // 加深刻度线颜色\n            },\n            distance: -8 // 向内偏移刻度线，确保不与刻度数字重叠\n          },\n          splitLine: {\n            length: 18, // 增加分割线长度，让刻度标签与分割线有更明显的分隔\n            lineStyle: {\n              width: 3, // 增加分割线宽度\n              color: '#666' // 加深分割线颜色\n            },\n            distance: -3 // 向内偏移分割线，远离刻度数字\n          },\n          axisLabel: {\n            distance: 30, // 进一步增加标签与刻度线的距离\n            fontSize: 12, // 稍微减小字体大小以减少重叠\n            color: '#333', // 加深文字颜色提高对比度\n            formatter: function(value) {\n              // 减少刻度显示，只显示0、20、40、60、80、100的刻度值\n              if (value % 20 === 0) {\n                return value.toFixed(0) + ''\n              } else {\n                return '' // 其他刻度不显示数字\n              }\n            },\n            backgroundColor: 'rgba(255, 255, 255, 0.8)', // 添加半透明背景色\n            padding: [2, 4], // 添加内边距\n            borderRadius: 3, // 圆角边框\n            textShadow: '0 0 2px #fff' // 添加文字阴影提高可读性\n          }\n        }]\n      })\n\n      // 更新部件健康状态柱状图\n      this.componentsHealthChart.setOption({\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        xAxis: {\n          type: 'category',\n          data: this.healthData.components.map(item => item.name)\n        },\n        yAxis: {\n          type: 'value',\n          name: '健康度',\n          min: 0,\n          max: 100\n        },\n        series: [{\n          type: 'bar',\n          data: this.healthData.components.map(item => ({\n            value: item.value,\n            itemStyle: {\n              color: this.getHealthColor(item.value)\n            }\n          })),\n          label: {\n            show: true,\n            position: 'top',\n            formatter: '{c}%'\n          }\n        }]\n      })\n    },\n    getHealthColor(value) {\n      if (value >= 80) {\n        return '#4CAF50'\n      } else if (value >= 60) {\n        return '#FF9800'\n      } else {\n        return '#FF5722'\n      }\n    },\n    resizeCharts() {\n      this.healthTrendChart && this.healthTrendChart.resize()\n      this.healthGaugeChart && this.healthGaugeChart.resize()\n      this.componentsHealthChart && this.componentsHealthChart.resize()\n    },\n    format(percentage) {\n      return percentage + '%'\n    },\n    generateReport() {\n      this.reportGenerating = true\n      console.log('开始生成健康评估报告...')\n\n      // 调用API生成健康评估报告\n      generateHealthReport({\n        health_data: {\n          current_health: this.healthData.current,\n          component_health: this.healthData.components,\n          rul_hours: this.rulValue,\n          rul_percentage: this.rulPercentage\n        }\n      })\n        .then(response => {\n          console.log('报告生成成功, 响应类型:', response.headers['content-type'])\n          // 使用file-saver库将二进制数据保存为Excel文件\n          const blob = new Blob([response.data], {\n            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n          })\n\n          // 生成文件名：健康评估报告_年月日时分秒.xlsx\n          const now = new Date()\n          const fileName = `健康评估报告_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}.xlsx`\n\n          saveAs(blob, fileName)\n\n          this.$message.success('健康评估报告生成成功')\n        })\n        .catch(error => {\n          console.error('生成健康评估报告失败:', error)\n          // 显示详细错误信息\n          console.log('错误详情:', {\n            message: error.message,\n            stack: error.stack,\n            response: error.response && {\n              status: error.response.status,\n              statusText: error.response.statusText,\n              data: error.response.data\n            }\n          })\n          this.$message.error('生成报告失败，请稍后重试')\n        })\n        .finally(() => {\n          this.reportGenerating = false\n        })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.life-container {\n  margin: 30px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);\n  padding: 24px;\n\n  /* 页面标题样式 */\n  .page-header {\n    margin-bottom: 32px;\n    text-align: center;\n\n    .page-title {\n      font-size: 2em;\n      font-weight: 700;\n      margin: 0;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n\n      i {\n        font-size: 1.2em;\n        color: #00d4ff;\n      }\n\n      .page-subtitle {\n        font-size: 0.4em;\n        color: #b8c5d1;\n        font-weight: 400;\n        margin-top: 8px;\n        letter-spacing: 1px;\n        display: block;\n      }\n    }\n  }\n}\n\n.dashboard-row {\n  margin-bottom: 20px;\n}\n\n.health-trend-card,\n.gauge-card,\n.rul-card,\n.components-card {\n  height: 350px;\n\n  .chart-container {\n    height: 300px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n\n    .chart {\n      width: 100%;\n      height: 100%;\n    }\n\n    .empty-content {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      background-color: rgba(255, 255, 255, 0.8);\n      z-index: 1;\n      text-align: center;\n      color: #909399;\n\n      i {\n        font-size: 48px;\n        margin-bottom: 16px;\n        color: #c0c4cc;\n      }\n\n      p {\n        margin: 5px 0;\n        font-size: 16px;\n      }\n\n      .tip {\n        font-size: 14px;\n        color: #a0a4a9;\n      }\n    }\n  }\n}\n\n.rul-content {\n  padding: 20px;\n  text-align: center;\n\n  .rul-value {\n    font-size: 32px;\n    margin-bottom: 20px;\n    color: #409EFF;\n  }\n\n  .rul-description {\n    margin-top: 20px;\n    color: #606266;\n    font-size: 14px;\n  }\n}\n\n.actions-container {\n  text-align: center;\n  margin-top: 20px;\n\n  .el-button {\n    margin: 0 10px;\n  }\n}\n</style>\n"]}]}