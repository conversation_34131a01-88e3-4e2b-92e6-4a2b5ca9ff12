
a70b67fadb5e5a08984a81acdf81cc36f3088881	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"6381e782109f7a53c6ff911b40c40beb\"}","integrity":"sha512-9Z1fSx/vFEBMXJN2ypsXYRDFHWPMYOBsvgdUJmwYZjOzayFPz4e+yAOVy2Wb0k7M/YvxQ5gNUycfCaJ2E47Hig==","time":1754202942251,"size":141758}