
e35a974c6e5daef34621266a13e142cc7aeb8e49	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"a9704f1f2a02ab9dbfd1e7324acbf42c\"}","integrity":"sha512-xD6wIoX98jXaFOq6bByp16H3hZL69kv20tGGZvA4ZXr/vX97E74yPhkq51NnwDZrwCE+BDFDptACS3DK0+OA/Q==","time":1754200969837,"size":26820}