
f6ad352df0d3b17d0858445463d38d0617d20133	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"46c2bb75dcba68da97abc920af939ec9\"}","integrity":"sha512-O8oHdGLoO/vmArynEZGK2IJv1S3+Q1tya3f/CSzAoJdddsJ19GFXIQRP2qQp6RYhk2wXC9h3laHaZO9YguqdiA==","time":1754202908696,"size":107331}