
a955ff644d5271338bff3659be3d53066d9c948d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"75e1613c0417da75a09d3da509806572\"}","integrity":"sha512-vZzfO+TRBTlNF+c0UtZiexuxN23diT1pu3yWtVp1DiQUhsmTRam0Y8vv+x/MG185pADtxdYeyS00dy5QT2OpGw==","time":1754205174013,"size":26693}