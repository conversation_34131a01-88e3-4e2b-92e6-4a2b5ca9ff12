
62986ccc59bd94521b311cde78a4b4217cff2fa4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.511ee9144741c1ccfa1a.hot-update.js\",\"contentHash\":\"930c72236f0817c4f0469cb87db64d12\"}","integrity":"sha512-JYYPr9f+8kOWXULqNDZjEMR958FnkVGNZLmUgcGK5w5Di8Q6NP+jNxHd1us1jfi4SaCqbVjcyVEL2p+bFDVkJQ==","time":1754202922250,"size":35877}