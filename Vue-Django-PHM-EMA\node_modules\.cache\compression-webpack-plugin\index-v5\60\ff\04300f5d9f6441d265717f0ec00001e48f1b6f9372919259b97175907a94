
a4572bd0d9d048eb0cd57fee2894893f7d912097	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"2a94c40b521071165ded313d4c66a536\"}","integrity":"sha512-i1gn6jJxxq/1AZosPHTaGNQf+5N+Cj2YVan+ZSk3yeNH+pJLR2m7QRsSrU6bWwnoZyTNjCXspEKj2zh9S8WgZA==","time":1754201015448,"size":274772}