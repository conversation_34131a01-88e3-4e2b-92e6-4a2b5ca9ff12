
c5b21baae6bde8b8e9050fa3093ac556eb2c6019	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F2.js\",\"contentHash\":\"25793b0efd71e064bc34a336a2bfe3fb\"}","integrity":"sha512-oJcNvwkRvjkuOg7bdT7Stkyg0fBf4ipJgH40dTWPJaEni2innzFJb4DYtjCfqe86oWohBrAsj1HEzZYyuK+bwg==","time":1754206025949,"size":178134}