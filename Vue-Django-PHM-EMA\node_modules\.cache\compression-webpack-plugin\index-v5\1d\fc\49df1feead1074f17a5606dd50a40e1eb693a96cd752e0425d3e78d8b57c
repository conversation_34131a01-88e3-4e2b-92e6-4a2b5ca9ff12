
aed4af4ad644f9ada2a0e6f79fb3f6fa5f425641	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F2.js\",\"contentHash\":\"25793b0efd71e064bc34a336a2bfe3fb\"}","integrity":"sha512-uexUogVQVvEHVbqyhP/x/PaJ8qG+BWe25Mxj/CtaQRekozl0xSiDTb4EGkDfcWU5GfUQfnFTKfa4UCYu+Xz8Ew==","time":1754206026471,"size":141611}