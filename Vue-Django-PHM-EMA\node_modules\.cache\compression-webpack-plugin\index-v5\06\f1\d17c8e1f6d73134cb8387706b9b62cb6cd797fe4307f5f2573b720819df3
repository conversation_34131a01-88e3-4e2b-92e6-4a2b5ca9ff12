
d7644d946e152454721ef31cf0f7b1537887bc15	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"7116fd8b2e33e79ca7d632290abe14f0\"}","integrity":"sha512-NOSKj1nCAMljdzmftU8xAY2SOiN2peFxIOUbwayF7b232eNH1S2HgqnlNx7kBvqEuoP7xTQbe9w53JKb9Ob4SA==","time":1754204776712,"size":26652}