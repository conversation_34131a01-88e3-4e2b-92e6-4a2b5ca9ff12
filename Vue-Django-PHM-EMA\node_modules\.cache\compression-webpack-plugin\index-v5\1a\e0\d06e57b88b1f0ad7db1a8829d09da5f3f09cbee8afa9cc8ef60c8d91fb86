
fbc81fa05d620b1718b88457e95d6187d14a76e2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"3d1b44697d44614e17a341415beac9eb\"}","integrity":"sha512-Z27LhwYURJHlUy0GigJWLbRZzS2SHrDCJPzOC31rRrK39dlV+riRUPdoureKzHiwbLVXzSkM3JJJmpLiSoKNGQ==","time":1754203859527,"size":114712}