
7e09dd72732007be0bbc3b461071b15e6230ba37	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"c312165665110f7943935999e597c7fd\"}","integrity":"sha512-uHZpzxxetzx6WGDSCPGm6dZ0uHil+Pvehbeg3Yh6k5gX5NZB+7RbVt0bQI2Dv47lyq8wXvt+KuO6ZkK4kRaFLw==","time":1754204299283,"size":119217}