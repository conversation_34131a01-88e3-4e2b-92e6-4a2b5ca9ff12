
5039d11c22ec900165d47328261e8a3980a7bd13	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"9a2085a4f96cafda1704d88e23e0e13e\"}","integrity":"sha512-ixDwf2U+pz2us6lgndfQdSehPq0L7PutTL2iwJN9GKuHGyCt0l04ubmWYWNXYsz1Xzuqnqi5GIa1lRasjfXCSw==","time":1754205363510,"size":26680}