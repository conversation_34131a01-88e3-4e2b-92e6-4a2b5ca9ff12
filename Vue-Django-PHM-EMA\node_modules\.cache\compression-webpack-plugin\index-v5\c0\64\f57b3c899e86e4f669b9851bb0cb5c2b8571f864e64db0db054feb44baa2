
ea11f42ed525d5c97c5d845531f22880897ea87f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"5f67efe00e80e9d37b88e609b19a36ca\"}","integrity":"sha512-XM6emSAzTMFJRk0MlAL8bDGIs1Zzc1bygAxrSCSstK4pO/ziCQwcKr8WCLoIQ5M4AbWyRozriFBh67X8PkQY6A==","time":1754204281539,"size":23460}