
68fd0db83855ca65e860d9575b307a284c7c34b3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"13a9dcb9bd449894df05af2c952895c3\"}","integrity":"sha512-0ix5Qq6uQ4QxqBahadT8UP0f9qzne/XhWAFNT8LeJBvJrbAwp72fYTJlhcY8YaBcKLM+vh86N3ltDMII4DuuDw==","time":1754204153628,"size":152624}