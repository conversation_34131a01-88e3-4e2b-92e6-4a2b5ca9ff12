
966cf78f49b85009884ae3448e8da7a8f01cd2e1	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"14d7e9bda618516a78a77b87114379fb\"}","integrity":"sha512-i6aV2nxzIcvgz5PG/VH0MUObMcNz4kddqFn0/HTNPgYvcMqoVJT9XdYLnU9qluqJ/7kKWk6UDwLWVRErvuYvlA==","time":1754204941107,"size":112221}