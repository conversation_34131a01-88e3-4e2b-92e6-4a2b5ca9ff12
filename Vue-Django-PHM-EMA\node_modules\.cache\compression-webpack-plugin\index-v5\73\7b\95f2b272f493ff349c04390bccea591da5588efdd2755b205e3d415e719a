
f4ade34950fbc1922f5f1b519a26fe4193d93021	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"94e1ff144eb840680b33950da2f610b3\"}","integrity":"sha512-1WVjsZTHn+6CvnW6XbhcKi1nmB4FFERaZuJd5EM8VCGprVMlp6nOnEwMJhU0/I+swA2ZMFMeb8891RjlEKn7rA==","time":1754201072655,"size":382623}