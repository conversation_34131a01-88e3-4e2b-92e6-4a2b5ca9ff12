
59828aca9c5a8403bede08b47ed32cc34dbec526	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.1e1279005abe8bb80e8b.hot-update.js\",\"contentHash\":\"5f2897d997d40781fd91b3e31310abec\"}","integrity":"sha512-HFU2wT5Oe/5dR85dyZwdpo/a+uECY3vKsvH/Yzm415mtqx/aqxgkmsGgD5jThqwV/f8l2aSWlc0/oby1pfYuRw==","time":1754203679042,"size":26112}