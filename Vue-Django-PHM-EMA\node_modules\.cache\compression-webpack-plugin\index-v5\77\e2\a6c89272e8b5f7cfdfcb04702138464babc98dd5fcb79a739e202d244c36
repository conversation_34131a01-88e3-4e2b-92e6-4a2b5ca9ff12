
d3239311f16dba74d8dee7e2fede528f9d01c7ee	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.17be21ec1c9f242bc38e.hot-update.js\",\"contentHash\":\"7739fa95d37cc2a790c74179fd2e71f5\"}","integrity":"sha512-ie5R8dZRPE1M29w2iEOq5UIjcoMBwOaKoDQpqXinmcs98Cv4Z+knpZ8wrJDZL8NF0M/pGPYYgJP9sgjMUiKTgQ==","time":1754204505143,"size":23667}