
19cd117567d9449feb0ea73cd211c2da900c58cc	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"b4818c9310a634dd1c31a7f53d3aeda8\"}","integrity":"sha512-eohW/Xak+WPT37E2MZ8NARGjM7DwPQ9EtgPgsZoZ9Pf3I/bmf37OaJc6S2MtaELqyxjoEZNtCscfMv1TmQFe7A==","time":1754203186534,"size":111062}