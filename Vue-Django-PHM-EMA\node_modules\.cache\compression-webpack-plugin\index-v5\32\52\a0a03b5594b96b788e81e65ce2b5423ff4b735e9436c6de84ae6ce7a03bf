
c411e1d94ed63fc7194a222e2b13cdc2340acede	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.8cd52fc88e0b30b72cee.hot-update.js\",\"contentHash\":\"9e98ccf5ff8f40fbf2796bb43a9dc56c\"}","integrity":"sha512-i77vQK5TvXKVSpTv+5Qx5Oc7sAwxntEjJbMoag/E10Lkql93qmWJSrO/cogzbQEb3dNpfX9ZwKcytXaygknAuw==","time":1754203186514,"size":86495}