
507febfd8e614e8804573c5228612fcf93705a03	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"0.69db7f9c81fbd9e43607.hot-update.js\",\"contentHash\":\"be6b2a12bc1a433e7ea45b53445dd1ab\"}","integrity":"sha512-Oyr2SxLHEfu76eOiVAFWf0IhVKsgDvI5z5EfRP4T5U9fUEW3vB9LlhCScqraJ6k7Qzl39L93zvAKPcG6t5Kt1g==","time":1754200969875,"size":137657}