
467ff55795e26aa63f52027767e61dc5b72fccb3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"4ff6f1e52d7470551c0bcdedf4f95120\"}","integrity":"sha512-bm5lg/xxNLAOtY7k0y1Q9lH+up8MTVAOvZJwi5UOu82DdsCFtKR+KXjb1B5kz4UyBRpChmc45btsPx1hKo7VNA==","time":1754204793136,"size":871657}