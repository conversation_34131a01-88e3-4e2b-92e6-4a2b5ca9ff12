
fa27f2b53f7d576d6e26b690641ea106c2a60865	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"44d47681801519e2826d16b86dd51771\"}","integrity":"sha512-ILNQOnMnYHaQ3LK3J4P9i4c/CQxzE/7qyAcT9LTejlE8urtD+1pFQfhL7PgKqN431dCJ90zatoEvie+qPr1BCQ==","time":1754206056150,"size":26701}