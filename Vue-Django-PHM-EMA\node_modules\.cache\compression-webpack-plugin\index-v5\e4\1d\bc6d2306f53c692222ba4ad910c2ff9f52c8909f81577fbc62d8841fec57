
d53aa2476b173ac6a6466e859bbc9063ecf1166c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.66ee804ab54fdd5df723.hot-update.js\",\"contentHash\":\"f8c6b9a81594593525df992e4c226dc9\"}","integrity":"sha512-qA6X3R4XnUdiHoal9zZel2Cj5waaSIm2b3luBnfbHXPkqc1j4AY0d1qvpNvcP0sc9p1P73fenG4WvRCe56OmPQ==","time":1754204281143,"size":29680}