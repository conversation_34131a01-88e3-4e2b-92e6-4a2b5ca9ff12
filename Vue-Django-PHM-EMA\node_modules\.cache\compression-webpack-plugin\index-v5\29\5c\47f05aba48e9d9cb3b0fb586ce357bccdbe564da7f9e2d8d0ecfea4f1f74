
5a358b555f34df0bd3510010363e0ae714de8e9b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"5bbe5785f5dd5d52c3a601f32ed5b92a\"}","integrity":"sha512-UttcdKEQW8dO+FUZ/fV0RuNRUjrheo4qyk2K+Xo4my/ZdepFuI8R2SDRdVrglBXvdmehoTsmhGr7w+aTvhbI4Q==","time":1754203679418,"size":23500}