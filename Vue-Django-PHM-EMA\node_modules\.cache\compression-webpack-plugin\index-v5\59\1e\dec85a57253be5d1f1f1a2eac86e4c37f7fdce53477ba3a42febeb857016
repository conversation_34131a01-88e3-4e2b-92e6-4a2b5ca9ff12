
5c794c07ee3e3cf113dd80c6027b90244178dd9c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"20671f3fe18ff8b31a9c5a189c39aab2\"}","integrity":"sha512-+9Hf+G5t1y7ztDfwpsF+sAIDHWOvmMi1Ocj87VlsbFRXYuLw4ASoMct6V5i+wxm6K6fcO5nbkMWW1Xv4/UI1Mw==","time":1754202155428,"size":26729}