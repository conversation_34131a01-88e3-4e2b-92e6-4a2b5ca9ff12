
6acfcae115adbd9a88598a34f33f4152be8f7af9	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"657f0f95f4d8f000bea335ae8417ed69\"}","integrity":"sha512-Jd+GlVlpOKFRfTF+/alHdwIbziTmX9rVMXgiJLJAmK+P9X22ngQT/z2sjs3KLNP2vo34er4Qk0jO64TVshpv7A==","time":1754204839208,"size":26725}