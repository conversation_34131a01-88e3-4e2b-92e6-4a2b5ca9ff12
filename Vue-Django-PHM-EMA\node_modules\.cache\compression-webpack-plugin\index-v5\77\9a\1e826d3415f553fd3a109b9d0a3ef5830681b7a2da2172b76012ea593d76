
a09668fc4a79886dc11ef3b7c61065bbac5beb95	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.222a81539ade9afdf417.hot-update.js\",\"contentHash\":\"d87db1256a0d261a0ce07a9ae59a86c6\"}","integrity":"sha512-PlOytv0mOVInziZjAHEu6FECYnS7AUXccD06kHQrBxUGzq/cQrGzAMgHZDBoEDWkXj7cWL3OL0LaiZ4D8/gArg==","time":1754203231249,"size":25376}