
9c236f35a2e4391006523d26adfedfebe2ad05e3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"487188cfb4c33e2f0d732d2cc2a23aef\"}","integrity":"sha512-fo5byG35sAPb1NoDVqmF3nrQj10smsIiutykhDHYjfZGLrgSd0akzP20jYPiEzdLh715EaGhAL10ow4yYJ0jIQ==","time":1754202098192,"size":26708}