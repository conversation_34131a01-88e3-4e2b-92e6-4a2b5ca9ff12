
cf89b0f809fb3d87fb39400ab11066b29afc93e9	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"e449b9dd78c33a3c983e06ec764d6244\"}","integrity":"sha512-cWzzATI8M8gIUmjnzAxlJrFe6X/CtiBfpXzwS7g3gkY40R7i2etRe2sHXvDM47Gxavc3amq2cXCfBFkgq57BeQ==","time":1754206041786,"size":26627}