
a72458668b4ceec75bb0fd6673b063ccda12dfca	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"38cbb814b2ca9c678f5ac8f5580c82ef\"}","integrity":"sha512-fq+O3xlY3R6+FbdXRWyVQo6CMQWiKnJNR6+4ZqNcLFTpFnQ4D4jvgK8+Fg1/zRoY8jiKIlIpyixeiYXHM5T2Yw==","time":1754205132800,"size":26771}