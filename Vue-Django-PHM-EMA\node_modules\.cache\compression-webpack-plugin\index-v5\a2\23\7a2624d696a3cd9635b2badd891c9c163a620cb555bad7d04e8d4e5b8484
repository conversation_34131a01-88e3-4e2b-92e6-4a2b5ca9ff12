
61c84fca600514629cf4f28764a33be3c5fe37ad	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"e86021c2571041208dacd7a58e770063\"}","integrity":"sha512-yvNJv66v7OkHr9ODuaSpUzQovNT1N3K6jN/ltTvXeylMU07hA0hdN6ZVXiwAP0imXRSrEMcrvD/LPeTI4l7kLg==","time":1754202908656,"size":23476}