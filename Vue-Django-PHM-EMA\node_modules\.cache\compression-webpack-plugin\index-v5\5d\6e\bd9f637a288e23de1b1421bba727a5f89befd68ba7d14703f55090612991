
2d7f387e14f54d2a4b9cc91977091ebc835e2500	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"a28c2fedc0ad77de5cb8388389c4a86d\"}","integrity":"sha512-zPS6rbtkTVM7n/1xdLqPcExWha4/8qkHfrAJew/G0E0SErwFHN/ySOmIw0ISKIwyJhTAw4L9ID0yvsLdv8I8Ig==","time":1754204815579,"size":89804}