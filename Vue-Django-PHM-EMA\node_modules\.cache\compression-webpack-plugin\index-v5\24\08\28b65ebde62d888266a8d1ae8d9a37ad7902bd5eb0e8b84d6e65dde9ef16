
0679570ef39d8d1ec4d8caac2f7670823796fc17	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.d89fd1a85815b06d45a4.hot-update.js\",\"contentHash\":\"1e15378a4227397d10cd117b5526fd41\"}","integrity":"sha512-BtWfoQWGpEVnfQZPFjwiM1exEol59nu2ORJAeyFoe1KROb6XoCqyu7lwcEeHu9uvCF/YaYl5zSg4AbpPwsR/ug==","time":1754204941488,"size":13684}