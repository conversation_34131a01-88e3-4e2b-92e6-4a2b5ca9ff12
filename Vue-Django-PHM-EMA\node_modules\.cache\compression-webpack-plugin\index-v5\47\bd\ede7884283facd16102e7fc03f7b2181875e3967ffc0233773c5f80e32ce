
d7790936168b09c05ad5b876487945a463345f73	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F2.js\",\"contentHash\":\"c66d54b33b9da3aff5d42dfc423a2d70\"}","integrity":"sha512-OcSwxT1nxLvStI2A69YqxtX+K5Mop9EmwU8ec45JKQnlEX2wVw6ct6y7xkZv3HO1Tyk15fELGAQg6FZNhJCIXg==","time":1754206042241,"size":145749}