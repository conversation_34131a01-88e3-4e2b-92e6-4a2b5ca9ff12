
f97a143576fc86d4d7788fec80f2ccde875b284f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.33a44fb16b233230fc33.hot-update.js\",\"contentHash\":\"a3ce034d9ea28e399b3c20fbc0409a88\"}","integrity":"sha512-m4c3Q+b55JlpcmhzQH42aDorc2NlNvIZK93fyp5hUGhdYpNN1iN2CrV3Jn7ZCnZ7xnIzXbDhayGdIUaYFrqeYg==","time":1754204483186,"size":30641}