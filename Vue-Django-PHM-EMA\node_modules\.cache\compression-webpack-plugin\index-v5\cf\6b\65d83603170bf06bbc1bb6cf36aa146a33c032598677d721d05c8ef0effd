
04295241c16a7c2fe0a28a3776329522e6b319f0	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.93640c3fec31b90d7f6b.hot-update.js\",\"contentHash\":\"492446e71ebe11803dadd4d2f494076b\"}","integrity":"sha512-+IAlhKtRGTneUI9PQEouBgCybAjsxFkMl53jZGev7K4pmui162kCIskiR08pOpSG7eVwVNKkOCIf1oLo5kqHaw==","time":1754204907015,"size":11867}