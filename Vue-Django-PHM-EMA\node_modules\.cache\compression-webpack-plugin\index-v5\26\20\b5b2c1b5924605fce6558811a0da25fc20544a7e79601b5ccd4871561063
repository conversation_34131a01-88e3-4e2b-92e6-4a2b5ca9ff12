
853a1650b8978cb20fa2bb05aca605f2600f1a54	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.4a687960aee179477df9.hot-update.js\",\"contentHash\":\"4d798031aaeb24cfc9aa7401dbd48f63\"}","integrity":"sha512-lf4OzcIH8uzocrMfHANz0hgHu5EDvHESe4LFsFSMm9IdrpcFzpP12AMw/PXZiW6+51Ba3bzFyj9tX+gN2sQBHw==","time":1754203462873,"size":25378}