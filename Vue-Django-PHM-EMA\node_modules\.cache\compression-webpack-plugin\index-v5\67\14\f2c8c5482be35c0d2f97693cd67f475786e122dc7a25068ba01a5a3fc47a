
39838e24f65ad7f01cfe05bb44c180397442ee6f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"13a9dcb9bd449894df05af2c952895c3\"}","integrity":"sha512-fGU37LmHRJ5gvj7SqQ+81u3BqVPpD0q9c3x94I8WRRAc3xSw26rs/6S8fKIjlF3w/mVXYB2OnJCVflZgIdqxew==","time":1754204154040,"size":116020}