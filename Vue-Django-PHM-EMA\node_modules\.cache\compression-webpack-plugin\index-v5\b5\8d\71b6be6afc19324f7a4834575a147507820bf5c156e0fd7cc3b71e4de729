
d553ff1eaa5ff51d514871b64d9713900720d849	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"9a2085a4f96cafda1704d88e23e0e13e\"}","integrity":"sha512-j5wpVtR5v8+p7nLUrvoovJIsD7b8fgglaDeWuqO98X282HsAVZexSPORiKmuj4O5H7pk6lOeDngV2gnBs6lk+w==","time":1754205363906,"size":23521}