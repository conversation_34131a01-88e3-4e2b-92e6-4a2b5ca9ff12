
00acd6e8940175e71937dd133b42b47c75fad393	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"c312165665110f7943935999e597c7fd\"}","integrity":"sha512-okX7U2I3tc8x9Ng6DdDisCUghx9NB5XIitzt4Cd9Gja1ieXGGfRdA2QMF6scMRRlLYq3CDCy3P66EoMs7dM4jQ==","time":1754204298914,"size":157171}