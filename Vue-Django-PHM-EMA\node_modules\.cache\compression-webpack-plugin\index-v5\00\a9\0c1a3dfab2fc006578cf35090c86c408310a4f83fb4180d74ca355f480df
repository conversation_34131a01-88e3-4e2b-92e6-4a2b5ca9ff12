
8f8f3685a5461b71f5fd34102fb126c29755a9c2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"cc9ba05034c4887331f1ee74d644b9f9\"}","integrity":"sha512-0+MkHpy4PWbA+cQNlngX3kyKgCGK9jF2QEbMmiaVxqj7DTqwlZpiBdaT7EFlySWG0zCYgptTgjL1eirGowIq4Q==","time":1754201792515,"size":1849893}