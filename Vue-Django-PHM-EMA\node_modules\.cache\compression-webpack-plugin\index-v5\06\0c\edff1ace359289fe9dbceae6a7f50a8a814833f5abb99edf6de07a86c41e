
e532f06ac095ea8b05bfc6db4da4c8a66e9c58f5	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"cc9ba05034c4887331f1ee74d644b9f9\"}","integrity":"sha512-EqR+tKi0TXugGDyJxmdGcwJaUi2YmLuFc0H70oaN9xl67aYPThx+UO+iJqT/50l6XU1dFuspZS5ebTeSvEGA1A==","time":1754201795350,"size":872308}