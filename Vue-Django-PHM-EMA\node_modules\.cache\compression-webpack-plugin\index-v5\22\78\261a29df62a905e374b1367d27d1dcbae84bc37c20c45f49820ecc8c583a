
b54ed42e6c0a99fc50b6316f16ff00f601897210	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"db74acfcb0b58ddbe97548e0c350ad2a\"}","integrity":"sha512-Ndcxz0x5p614qenYvNBkTyzFLeejqPWf0MmUTqItJqwDjC2el+kbAFJ3pThIEYXANdjOtC5Br4okhZ0EBUS70Q==","time":1754203845135,"size":114492}