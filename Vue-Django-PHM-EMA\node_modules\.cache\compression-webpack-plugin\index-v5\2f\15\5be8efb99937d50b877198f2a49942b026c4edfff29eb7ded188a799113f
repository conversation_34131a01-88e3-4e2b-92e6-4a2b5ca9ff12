
56a8eedee1143c1ce440660a018c393e24f4fe2c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.0eb693995b4c7679cf2f.hot-update.js\",\"contentHash\":\"e0b3faec88f89d2bee7fa687ec015aa5\"}","integrity":"sha512-rAA3QLJdABKhHgbsf95izd0eMXbPOogQGGhpCiFq6vY4XwlqITtGsgh/vmB3/KpsSqhApcjidr21lEFwR5e+bg==","time":1754204194487,"size":28896}