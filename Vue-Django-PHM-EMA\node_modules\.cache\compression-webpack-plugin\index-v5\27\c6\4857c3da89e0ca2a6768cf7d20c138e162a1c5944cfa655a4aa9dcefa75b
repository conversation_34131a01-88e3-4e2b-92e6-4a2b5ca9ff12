
d48569a58c8e49e90acd7b9c5032dcd8c680b869	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"d31f918496bf4999def6e8260eead391\"}","integrity":"sha512-RQbfTFW4GH+KV0/UwvEkYqd9Z/SBXtYu+gDZ8rblbMoMpumPFwN2lxO7j80XlMPC3+0issuh8kkDBCfZ++kICQ==","time":1754201792377,"size":26756}