
f56d66b0205329c8b54aee41d8da1187f73c4cb8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"0b32dbfd989a877f0a0f0b35b971f765\"}","integrity":"sha512-CL4+poJ+7g2EoHhW1jyJUXeO4u9uR9yyaYU+D/DxeuhdSVFae0LNQgF4m7K9eiALy4TpWqBVOmn2yO2FtzHVxg==","time":1754203231659,"size":113145}