
92c5e2df0e733418b36bcf4ce7cfb2c039442e4d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.f8959224dcf62210d31d.hot-update.js\",\"contentHash\":\"a3ce034d9ea28e399b3c20fbc0409a88\"}","integrity":"sha512-FpGmQuhZexzi3R2wNcP6+VZwOYFvNjqE7a2sRf9R8ShjmjwHLKoCdWTTrEHVxl92zTiA12oHgwESsU0QOCyI6Q==","time":1754203844725,"size":36070}