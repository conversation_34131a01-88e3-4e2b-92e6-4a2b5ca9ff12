
92c2ec5358095abc785d247662ab4f6c3ed85ca5	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"a18c0adeeb8379b869e3973b65e4a7d7\"}","integrity":"sha512-FaZqoBsFUsNhXccgMY1gaAZfRbZZZXwAiQMAMaOxNF6xHi4iylXOzBPMq7Rxg9wL2gwpvp+/YihN9+0kovP8xw==","time":1754204815577,"size":23442}