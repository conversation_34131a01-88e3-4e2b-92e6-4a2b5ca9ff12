
100a74d7e1c7cf099a6e72f5f53a175e8f7af22d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"cd92c67424a658372156f588b6012a8f\"}","integrity":"sha512-v59fw37X8cIeMo6X2ZW83p8o4voYUaN8jvLcsYJkz3RL2n1zBC3RuboRq7qig9SGivV2zhsbBezSPnNf+e3xdA==","time":1754201059422,"size":381831}