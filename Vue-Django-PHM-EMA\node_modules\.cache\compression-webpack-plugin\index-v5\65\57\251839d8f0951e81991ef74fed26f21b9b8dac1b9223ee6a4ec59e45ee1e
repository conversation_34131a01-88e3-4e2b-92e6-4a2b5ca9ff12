
8858db1e54d525eca2f7618e9050e9f258663f93	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F5.js\",\"contentHash\":\"219ae20ff1ff3ce503c1b588abca04f2\"}","integrity":"sha512-fUa7joKY/UA7KqGisSGeKvN7jJppvi3mS5hiXi2j/+iq47IPkunstdjmDDb1bGow+JTC6ny6Rn8ExiPTdltCsw==","time":1754206056150,"size":130632}