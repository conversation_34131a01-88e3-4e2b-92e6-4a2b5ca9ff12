
b0bb421e92e0bfb44c37b791c424e747526470a0	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"9b7b92c19218a2e1ca992cecb3f68183\"}","integrity":"sha512-ohl74yVkiOEoKqGeKXunosTGtCpklUCBJdzsFI+yauueKpThfLrNWl6YS2UTIo64vZwocJ3ah1Wu+YHhLNReCg==","time":1754204907016,"size":90504}