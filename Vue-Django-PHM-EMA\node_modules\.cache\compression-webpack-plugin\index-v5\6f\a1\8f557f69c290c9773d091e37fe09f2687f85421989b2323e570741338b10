
99efa8d5616ac044c791ce5e136d2247193da444	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"be8ad2ad69d5a9e66669b06348f65dc2\"}","integrity":"sha512-VvvYIEkL94W0adcNjnXu/E2TTICqXUoIir7zSzAookI3Bd+3euClRLV/EF8kRshWaU1BCPhxOuZ4ZDZgQA1rhw==","time":1754205117402,"size":23538}