
7288671478978c4750a19d89d5a15099b1597452	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.d89fd1a85815b06d45a4.hot-update.js\",\"contentHash\":\"1e15378a4227397d10cd117b5526fd41\"}","integrity":"sha512-ECX58UUAW834FMMW7391o1E0RmUIPbw86WLVojn0B1V4Er0VcpmC2CRe6OKY0x5kA+L/gg0xyE9WRprW3sClgQ==","time":1754204941106,"size":15107}