
41c67bc17a5a1e8d9edb3e03ab9cf3297cd4e5df	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.75d6c297b947e982c763.hot-update.js\",\"contentHash\":\"fb69c8156b36b92530da50d0873d3435\"}","integrity":"sha512-FejqkFDCdEJ7Xf6GOJUH/0laja/+ul0A/tUNEqqFQ9l2ZVR8zsokhBEq62dddMXF50XIpfMDKnTPnSoM1VOcpg==","time":1754203663878,"size":109382}