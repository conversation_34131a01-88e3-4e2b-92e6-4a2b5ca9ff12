
5181b1ed66aa7b05a0fb9692352b9020587f4da3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.222a81539ade9afdf417.hot-update.js\",\"contentHash\":\"d87db1256a0d261a0ce07a9ae59a86c6\"}","integrity":"sha512-fuTbkX8DtiQQCemgmNTQwdFoZQDp9QK2apZMnSLRfbwlzR7V6C7ZcB4zABrvIxdDa1t6RXo1w7whnWmH2PtAYw==","time":1754203231602,"size":22496}