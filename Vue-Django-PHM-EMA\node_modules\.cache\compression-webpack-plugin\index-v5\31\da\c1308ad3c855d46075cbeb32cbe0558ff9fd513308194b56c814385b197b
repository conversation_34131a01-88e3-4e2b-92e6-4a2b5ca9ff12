
ae5e9bb7890c1b85d0538eb0489dd8f6e141e296	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"f62a4a97c1d202f3823acf707075749e\"}","integrity":"sha512-lFVZR/LiM5lx1F5jHOYSInkE5vJiXkuayhQRAEzLS/aKV6uDREpTlFKxjJ6L2byR8EsT5Y5cOiRhUiWF7bA0nA==","time":1754204347584,"size":23526}