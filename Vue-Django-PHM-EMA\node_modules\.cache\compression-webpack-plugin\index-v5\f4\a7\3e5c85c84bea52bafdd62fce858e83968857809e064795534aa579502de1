
d666e5ae6e1c746a738d3f4f09f992bfd555da05	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.fc5f0e5f77a486e5798f.hot-update.js\",\"contentHash\":\"5dc302068ce1c30ecd27e7686dea3fb0\"}","integrity":"sha512-qBF2bXV5rueelhT3oabGq85wdwbTCXFlFal6w3IZnUB9QTotCuVeE3vhtO1+xBMirBF1cx5JWRupeYkvzdRRQQ==","time":1754204764504,"size":15514}