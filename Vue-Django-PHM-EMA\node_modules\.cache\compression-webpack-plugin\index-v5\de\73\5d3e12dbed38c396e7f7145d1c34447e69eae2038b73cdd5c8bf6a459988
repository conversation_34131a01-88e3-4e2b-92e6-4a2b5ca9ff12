
30513dd0bd22d78aa38b5e2e316541c672a7e7f4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"532a4c009a1383035729163d3f9edd79\"}","integrity":"sha512-y2CA0ZTQZeA+D4yAm3mG7PST4FEwd8w0ABPdsaNwkTAuXSCNmO3qSP1M3F72rcfLvQUAoadg9F30CzEKTONa4g==","time":1754205132800,"size":103452}