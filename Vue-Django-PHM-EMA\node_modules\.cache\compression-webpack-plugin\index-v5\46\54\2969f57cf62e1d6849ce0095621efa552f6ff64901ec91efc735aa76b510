
25376aa52cfdc02132e13ca4b4e46a52d5a8173a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"47992a0ab9c5c9ed39f8cfd342e5d9aa\"}","integrity":"sha512-SlNQGzW0rKZI4MWvzGZN3ExM3Gsd2SYl56hjp0XoPO1StPGZA0B/04vRCVwAUfl578pQ7Gy1/N122aOkYlRQFg==","time":1754201111687,"size":26772}