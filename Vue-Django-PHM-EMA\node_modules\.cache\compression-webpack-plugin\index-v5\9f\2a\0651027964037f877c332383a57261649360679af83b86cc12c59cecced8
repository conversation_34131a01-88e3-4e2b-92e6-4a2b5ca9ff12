
5cb109c503dcd6fceb5d5a7b43e5a42fe17f0fa7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"9904141c7c3418d716fb669720a391a6\"}","integrity":"sha512-/0iU5a+JkaLzWWLT9GnMiT2YhlMF/f1HcKEMsD+IFhnwsxWkXnKoYjr8oW3esB6bkqjT/puROo/R/4ANJ/HJ0Q==","time":1754203463257,"size":23545}