
9dbff7ee4fb42e6247d21a17355ad0d247627e31	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"ed08beddaffb182194ecde60cf4f7324\"}","integrity":"sha512-q+sc03oG43CUYaqgjgtvZgu9tLtiT4RH4UpFhYKB9UawQWxerczjiPxXOv/YC21ZfiRE5nokWRlTTKeXtIVW+A==","time":1754205154713,"size":23475}