
5dec2fe52198377a87e7e0622a6efba18ebbe813	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"app.1d3b79c5bfce5223ddad.hot-update.js\",\"contentHash\":\"6ea3e4becf0a61ac4deaf7674cd45bb2\"}","integrity":"sha512-TGEw0qZsA4Q5hdcFt+durkXMs49nsTnOhM62nM/GEsqW9dxOZybTnDnRChFrQUWbu2n8bRI0/MeI4qN5RLawDg==","time":1754201792795,"size":44837}