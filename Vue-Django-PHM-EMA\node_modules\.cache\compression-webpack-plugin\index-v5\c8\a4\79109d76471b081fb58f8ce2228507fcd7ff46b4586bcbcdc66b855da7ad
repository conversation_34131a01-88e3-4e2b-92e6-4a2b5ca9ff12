
9322453c96d7867b081e4fae54abea0e3d5fe8ca	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"399a1cfff4b6148e0b5df02c01c3b73c\"}","integrity":"sha512-/cqxol4KUUi5JyvOvs331funeMbiAio2W8xYZKG0ypfksdtQrAK969AdX0J2LJBap1q9NeR4dx0c/X1ZcvvsVw==","time":1754203231250,"size":26701}