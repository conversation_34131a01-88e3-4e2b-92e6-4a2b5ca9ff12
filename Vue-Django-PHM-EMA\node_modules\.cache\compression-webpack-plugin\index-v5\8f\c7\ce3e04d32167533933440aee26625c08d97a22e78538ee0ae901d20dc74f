
b5e8e80c06c1814dde91c2c8ab199c35a7c006b9	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.93640c3fec31b90d7f6b.hot-update.js\",\"contentHash\":\"492446e71ebe11803dadd4d2f494076b\"}","integrity":"sha512-ZBzIB8d5S290GbHrv938pQ+r94Zu7JJQqdipMlxxZKyuOq5pwxyUFHnpjDI57SGt3mU2HzFJ8oBnA2lqeMzhSQ==","time":1754204906606,"size":13007}