
5b79e1385a12a79dfb6890cf4f750094cca3fd88	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"2fc2dff4e9e1154f71533777e8c33dd2\"}","integrity":"sha512-cpCSA9E2SgfiAYJPR31qvTf7D1/YMEc7Ul/QXmmXNdvkWMuc63n0xzcbI34lBBjaPUA+rorm+sQG8DbX/t3jvQ==","time":1754202866941,"size":138689}