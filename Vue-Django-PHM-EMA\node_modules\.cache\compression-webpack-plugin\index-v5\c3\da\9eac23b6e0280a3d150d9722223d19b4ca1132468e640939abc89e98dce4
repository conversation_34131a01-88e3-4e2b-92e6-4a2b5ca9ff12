
863704a8139b1342e4929874a89d8e82b731324a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"a31699552196e250c0daf5ebe8d19c02\"}","integrity":"sha512-k3AC2n1VR48dtNqg09NXeKKJMCkfNZBBckS55IAQDZU/eu5GdFs/tcugR4I/lmFdalO3h5wOqQi99uigtkjUlQ==","time":1754204860475,"size":23557}