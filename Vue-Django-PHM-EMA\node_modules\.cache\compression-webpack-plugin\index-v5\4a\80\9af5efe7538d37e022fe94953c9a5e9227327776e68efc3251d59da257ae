
66cf285a9afd2430f42fb5829cff8a10090d4b49	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"d6c1accd9a802da4bf8be8eeb47dec5b\"}","integrity":"sha512-01WmF/cz438lmuof8GI/Km7ger+9zrm26TnmVnFmyUmgI0tFn2EbrNpiF2PeowdAhM3YA5XIstokyzBmn3XfaA==","time":1754205498261,"size":26726}