
a057260f7845b8832f3287e52958894e863c7b27	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"b911408b9141600d4b99cf2ae6430ae7\"}","integrity":"sha512-SCI43rzH1X0BfX7n68NltRYxa0fhxnEUv6Lkd1MyiXRiQWyhW6I/hO3gnW3yj0gFOVE7VsEB0pxoH0ol6IAHLA==","time":1754204906606,"size":26702}