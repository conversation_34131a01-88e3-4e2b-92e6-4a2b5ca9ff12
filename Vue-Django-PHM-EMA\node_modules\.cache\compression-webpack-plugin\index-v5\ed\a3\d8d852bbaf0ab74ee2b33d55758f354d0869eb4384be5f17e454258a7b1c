
a20d0e0e43976d1b2715cd67f32a6cf0a4fcf400	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"72097c49e4f2e4e1d387d7f382e29eef\"}","integrity":"sha512-J20ugfEJr7aulkCqFM4hAQOESzYG9UTm2YueZLPtjFrOL1FFuy+6fbEuq5Nw4Bhc0wqRJDjawD9BFLsrfPK2uA==","time":1754204790187,"size":26653}