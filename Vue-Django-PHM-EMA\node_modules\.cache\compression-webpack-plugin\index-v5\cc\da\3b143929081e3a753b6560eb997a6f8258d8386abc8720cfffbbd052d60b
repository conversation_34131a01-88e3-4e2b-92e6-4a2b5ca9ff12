
bdde445405246feace4c57f6f405d80b95a57512	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"3875d722d82274ef68bec0c09a22e772\"}","integrity":"sha512-eDK9FUHHBEpA9rdNup+IodMx3kObjtXpnkRb8q7fDIBzJjLspSXv1BpkpRreDBxEOe8Mq6fX94r8un8CudIoRQ==","time":1754205259874,"size":23536}