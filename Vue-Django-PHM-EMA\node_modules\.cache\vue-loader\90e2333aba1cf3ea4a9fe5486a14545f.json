{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\injection\\index.vue?vue&type=style&index=0&id=f2c5c520&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\injection\\index.vue", "mtime": 1754205496055}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1634626957199}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1634627893377}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1634627525156}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1634627658274}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkQA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/injection", "sourcesContent": ["<template>\n  <div id=\"injection-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-cpu\"></i>\n        虚拟模型仿真\n        <span class=\"page-subtitle\">Virtual Model Simulation</span>\n      </h2>\n    </div>\n\n    <div class=\"model\">\n      <h2>虚拟仿真模型</h2>\n      <el-select v-model=\"model\" clearable placeholder=\"请选择模型\" @change=\"selectModel\">\n        <el-option\n          v-for=\"item in options1\"\n          :key=\"item.value\"\n          :label=\"item.label\"\n          :value=\"item.value\"\n        >\n        </el-option>\n      </el-select>\n      <h3 style=\"text-align:center\">模型显示<span style=\"color:red; font-size:18px; font-family:KaiTi\">(点击查看大图)</span></h3>\n      <div class=\"modelImg\">\n        <el-card style=\"height:300px;width:100%\">\n          <viewer :images=\"images\">\n            <img v-for=\"src in images\" :key=\"src\" :src=\"src\">\n          </viewer>\n        </el-card>\n      </div>\n    </div>\n    <div class=\"faultTree\">\n      <h2>虚拟模型仿真<span style=\"color:red; font-size:18px; font-family:KaiTi\">(双击故障树节点选择对应故障类型)</span></h2>\n      <!-- <faultree> </faultree> -->\n      <div class=\"faultText\">\n        <el-row>\n          <el-col :span=\"15\" style=\"width:60%\">\n            <el-input v-model=\"faultLine\" placeholder=\"待选择\">\n              <template slot=\"prepend\">仿真类型：</template>\n            </el-input>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-select v-model=\"signalFre\" style=\"width:65%\" clearable placeholder=\"请选择所输入的正弦信号频率\" @change=\"selectSignalFre\">\n              <el-option\n                v-for=\"item in options2\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              >\n              </el-option>\n            </el-select>\n            <el-button type=\"plain\" @click=\"startInject\">确认</el-button>\n          </el-col>\n        </el-row>\n      </div>\n      <div id=\"tree\">\n        <faultree :name=\"treeData1\" style=\"width:100%\" @func=\"getFault\"></faultree>\n        <!-- name自起名，treeData1父组件数据 -->\n      </div>\n    </div>\n\n  </div>\n</template>\n\n<script>\n\nimport { getToken } from '@/utils/auth'\nimport faultree from './components/faultree.vue'\n\nexport default {\n  name: '虚拟模型仿真', // eslint-disable-line vue/name-property-casing\n  components: {\n    faultree\n  },\n  data() {\n    return {\n      model: '',\n      faultLine: '',\n      faultType: null,\n      signalFre: '',\n      faultIndexDict: '',\n      images: [],\n      root: false,\n      visible: false,\n      imageUrl: '',\n      selected: '',\n      options1: [\n        { value: '选项1', label: 'EMA-AMESim模型' },\n        { value: '选项2', label: 'EMA-Simulink模型' }\n      ],\n      options2: [\n        { value: '选项1', label: '0.3Hz' },\n        { value: '选项2', label: '0.6Hz' },\n        { value: '选项3', label: '0.9Hz' },\n        { value: '选项4', label: '1.2Hz' },\n        { value: '选项5', label: '1.5Hz' },\n        { value: '选项6', label: '1.8Hz' },\n        { value: '选项7', label: '2.1Hz' },\n        { value: '选项8', label: '2.4Hz' },\n        { value: '选项9', label: '2.7Hz' },\n        { value: '选项10', label: '3.0Hz' },\n        { value: '选项11', label: '3.3Hz' },\n        { value: '选项12', label: '3.6Hz' },\n        { value: '选项13', label: '3.9Hz' },\n        { value: '选项14', label: '4.2Hz' },\n        { value: '选项15', label: '4.5Hz' },\n        { value: '选项16', label: '4.8Hz' },\n        { value: '选项17', label: '5.1Hz' },\n        { value: '选项18', label: '5.4Hz' },\n        { value: '选项19', label: '5.7Hz' },\n        { value: '选项20', label: '6.0Hz' }\n      ],\n      treeData1: [\n        { title: 'EMA故障树' }\n      ]\n    }\n  },\n  mounted() {\n    this.getErrorName2Index()\n  },\n\n  methods: {\n    nowTime() {\n      /* console.log(new Date()) // Sat Aug 07 2021 15:14:05 GMT+0800 (中国标准时间)\n      new Date().getTime() 时间戳 总毫秒数 */\n      var nowtime = this.$moment(new Date().getTime()).format('YYYY_MM_DD_HH_mm_ss')\n      return nowtime\n    },\n    getErrorName2Index() {\n      this.$axios.get('./errorDict.json').then(res => {\n        this.faultIndexDict = res.data.errorName2IndexDict\n        console.log('字典', res.data.errorName2IndexDict)\n      })\n    },\n\n    // 选择模型图片\n    selectModel(id) {\n      let selectedName = {}\n      selectedName = this.options1.find((item) => {\n        return item.value === id\n      })\n      this.selected = selectedName.label\n      console.log(this.selected)\n      this.$axios({\n        method: 'GET',\n        url: '/phm/getModelp/' +\n        '?type=' + this.selected,\n        responseType: 'blob',\n        headers: { Authorization: 'Bearer' + getToken() }\n      }).then(res => {\n        const blob = new Blob([res.data], {\n          type: res.headers['content-type']\n        })\n        this.imageUrl = window.URL.createObjectURL(blob)\n        this.images.push(this.imageUrl)\n        console.log(this.images.length)\n        if (this.images.length > 1) {\n          this.images.shift()\n        }\n        console.log(this.images)\n      })\n    },\n\n    // 选择信号频率\n    selectSignalFre(id) {\n      this.getErrorName2Index()\n      let selectedName = {}\n      selectedName = this.options2.find((item) => {\n        return item.value === id\n      })\n      this.signalFre = selectedName.label\n    },\n\n    // 将选中的文字转换为故障序号\n    getFault(data, root) {\n      print(data)\n      this.faultLine = data\n      this.root = root\n      this.faultName = this.faultLine.split('-')[this.faultLine.split('-').length - 1]\n      this.faultType = this.faultIndexDict[this.faultName]\n    },\n\n    // 发送注入信息\n    startInject() {\n      if (!this.faultLine) {\n        this.$notify({\n          title: '提示',\n          message: '尚未选择注入故障类型！',\n          duration: 3500,\n          type: 'error'\n        })\n      } else if (!this.signalFre) {\n        this.$notify({\n          title: '提示',\n          message: '尚未选择输入正弦信号频率！',\n          duration: 3500,\n          type: 'error'\n        })\n      } else if (!this.root) {\n        this.$notify({\n          title: '提示',\n          message: '当前选择不是根节点！',\n          duration: 3500,\n          type: 'error'\n        })\n      } else if (!this.faultType) {\n        /* console.log('2424234', this.faultName, this.faultType, this.faultIndexDict)\n        console.log('45243', this.faultIndexDict[this.faultName]) */\n        this.$notify({\n          title: '提示',\n          message: '该种故障正在研究中，请选择其他故障',\n          duration: 3500,\n          type: 'warning'\n        })\n        this.faultLine = ''\n        this.signalFre = ''\n      } else {\n        this.$axios({\n          method: 'GET',\n          url: '/phm/getFaultInject/' +\n          '?faultType=' + this.faultType + '&signalFre=' +\n          this.signalFre + '&faultLine=' + this.faultLine\n        }).then(res => {\n          console.log(res.data)\n          if (res.data.code === 200) {\n            const h = this.$createElement\n            this.$notify({\n              title: '成功',\n              message: h('p', null, [\n                h('span', null, '已启动 '),\n                h('span', { style: 'color: red' }, this.faultName),\n                h('span', null, ' 仿真'), <br/>,\n                h('span', null, '正弦输入信号为 '),\n                h('span', { style: 'color: red' }, this.signalFre.split('Hz')[0]),\n                h('span', null, ' Hz')\n              ]),\n              duration: 4500,\n              type: 'success'\n            })\n            this.faultLine = ''\n            this.signalFre = ''\n            this.root = false\n          } else {\n            this.$notify({\n              title: '提示',\n              message: '虚拟模型仿真启动失败！',\n              duration: 3000,\n              type: 'error'\n            })\n          }\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n#injection-container {\n  padding: 24px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);\n}\n\n/* 页面标题样式 */\n.page-header {\n  margin-bottom: 32px;\n  text-align: center;\n\n  .page-title {\n    font-size: 2em;\n    font-weight: 700;\n    margin: 0;\n    background: linear-gradient(135deg, $techBlue, $techBlueLight);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12px;\n\n    i {\n      font-size: 1.2em;\n      color: $techBlue;\n    }\n\n    .page-subtitle {\n      font-size: 0.4em;\n      color: $textSecondary;\n      font-weight: 400;\n      margin-top: 8px;\n      letter-spacing: 1px;\n      display: block;\n    }\n  }\n}\n\n.model {\n    padding: 24px;\n    margin-bottom: 32px;\n    background: $bgCard;\n    backdrop-filter: blur(10px);\n    border: 1px solid $borderPrimary;\n    border-radius: 12px;\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\n    &:hover {\n      border-color: $borderHover;\n      box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3);\n      transform: translateY(-2px);\n    }\n\n    h2 {\n      color: $textPrimary;\n      font-weight: 600;\n      margin-bottom: 20px;\n      display: flex;\n      align-items: center;\n      font-size: 1.4em;\n\n      &::before {\n        content: '';\n        width: 4px;\n        height: 18px;\n        background: linear-gradient(135deg, $techBlue, $techBlueLight);\n        border-radius: 2px;\n        margin-right: 12px;\n      }\n    }\n\n    h3 {\n      color: $textPrimary;\n      font-weight: 500;\n      margin-bottom: 16px;\n    }\n\n    .modelImg {\n      width: 100%;\n      margin: 0 auto;\n      margin-top: 20px;\n    }\n  }\n\n.faultTree {\n  height: 940px;\n  padding: 24px;\n  background: $bgCard;\n  backdrop-filter: blur(10px);\n  border: 1px solid $borderPrimary;\n  border-radius: 12px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\n  &:hover {\n    border-color: $borderHover;\n    box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3);\n    transform: translateY(-2px);\n  }\n\n  h2 {\n    color: $textPrimary;\n    font-weight: 600;\n    margin-bottom: 20px;\n    display: flex;\n    align-items: center;\n    font-size: 1.4em;\n\n    &::before {\n      content: '';\n      width: 4px;\n      height: 18px;\n      background: linear-gradient(135deg, $techBlue, $techBlueLight);\n      border-radius: 2px;\n      margin-right: 12px;\n    }\n  }\n\n  .faultText {\n    width: 100%;\n    margin-bottom: 20px;\n  }\n}\n\n// Element UI 组件深色主题样式\n:deep(.el-select) {\n  .el-input__inner {\n    background-color: rgba(47, 51, 73, 0.8) !important;\n    border-color: rgba(0, 212, 255, 0.2) !important;\n    color: #ffffff !important;\n\n    &:hover {\n      border-color: rgba(0, 212, 255, 0.4) !important;\n    }\n\n    &:focus {\n      border-color: #00d4ff !important;\n    }\n  }\n}\n\n:deep(.el-input) {\n  .el-input__inner {\n    background-color: rgba(47, 51, 73, 0.8) !important;\n    border-color: rgba(0, 212, 255, 0.2) !important;\n    color: #ffffff !important;\n\n    &:hover {\n      border-color: rgba(0, 212, 255, 0.4) !important;\n    }\n\n    &:focus {\n      border-color: #00d4ff !important;\n    }\n  }\n\n  .el-input-group__prepend {\n    background-color: rgba(26, 29, 41, 0.8) !important;\n    border-color: rgba(0, 212, 255, 0.2) !important;\n    color: #b8c5d1 !important;\n  }\n}\n\n:deep(.el-button) {\n  &.el-button--default {\n    background-color: rgba(47, 51, 73, 0.8) !important;\n    border-color: rgba(0, 212, 255, 0.2) !important;\n    color: #ffffff !important;\n\n    &:hover {\n      background-color: rgba(0, 212, 255, 0.1) !important;\n      border-color: rgba(0, 212, 255, 0.4) !important;\n    }\n  }\n}\n\n:deep(.el-card) {\n  background-color: rgba(47, 51, 73, 0.8) !important;\n  border-color: rgba(0, 212, 255, 0.2) !important;\n\n  .el-card__body {\n    background-color: transparent !important;\n  }\n}\n\n</style>\n\n"]}]}