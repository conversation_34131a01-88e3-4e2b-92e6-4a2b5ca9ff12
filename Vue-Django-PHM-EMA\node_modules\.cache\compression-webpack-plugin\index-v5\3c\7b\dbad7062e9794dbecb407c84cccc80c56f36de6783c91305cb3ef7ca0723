
7e7a254e88090ae5bde8f2a14a36ba79d8ca52ec	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"2b4c308c2a645faeb1ecc80366602c67\"}","integrity":"sha512-5HByOFl+Solou+xRZHYRNHC1BnCshziAtHJLmYXLxQgAa+bkLVswqxMvKMdAGW/UF2fk/1hV4DpIMUdOARIDqA==","time":1754203845100,"size":23495}