
59d4aaaf3554f4be15cafe97bebc627d8a5fd02c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.febcc0f830ad9d2b2134.hot-update.js\",\"contentHash\":\"682be3154843f04f1456581f279aaa0e\"}","integrity":"sha512-Qju2r1fJ4OHRJ6GBJIY5x57yJQv9jrTurfwkw3AFCTouU02UyGM/V7iK8zAIeISIyZWfeh7Sm3WJuRr1nZ8kNA==","time":1754204860113,"size":29810}