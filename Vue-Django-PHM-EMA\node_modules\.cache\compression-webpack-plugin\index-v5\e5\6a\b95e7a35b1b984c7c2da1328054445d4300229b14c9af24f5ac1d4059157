
4d816d2b31bf77ec41af9ff44a3af04d0f533c64	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"ea9e8cf1f3904a26e9790353741dc0c1\"}","integrity":"sha512-MEJD5iBEQFAn6pbqmFlnpWxQutsSC7cLeeNR1Pmb+okVFFLutLR6f4LIqPeYDglJBoi/eWxRYyRze7vPMEdV/Q==","time":1754204504772,"size":150896}