
4a8b2a046fcd7503cc423ee109279dbeb4016a9f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"9de1e1dc8c39ea8911e8af7eefe313ef\"}","integrity":"sha512-IsPh3VLqhNsIGDJY89B83NVkKP+ilj2qiMFUoLAM2N7xGgCKcxlwdKdcCH6muVVlUhzbvPK+tA7kVwx3qFvF8Q==","time":1754200994518,"size":23551}