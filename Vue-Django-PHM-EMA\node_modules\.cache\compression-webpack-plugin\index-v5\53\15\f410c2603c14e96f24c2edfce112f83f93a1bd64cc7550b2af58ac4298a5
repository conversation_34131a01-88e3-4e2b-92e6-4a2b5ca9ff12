
fe2884e3aae7eadd47d25dbbfbc3ba8f5881b730	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"9904141c7c3418d716fb669720a391a6\"}","integrity":"sha512-OsVvAIAv5cFPptfVEAaEHY14goVUNzif0JwOv28P1j14/dSKUW3knhRyLhW/4iuQxWl7QtoBXeTwBFgsX8qiBw==","time":1754203462874,"size":26729}