
f2b392c802696b1cbb6fe97d7b5db5170688072a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"0.b294548ef659d88d5289.hot-update.js\",\"contentHash\":\"fa12527c1444323a02fc904bc4a8043d\"}","integrity":"sha512-bLIVbvHES5rWx9LXZXUGGFYQd5TXxMp3plSre8G68ah9kGK3eYDHvQjgDTwNNvsrRcnsV1lWdmgnGtSMH82a6g==","time":1754201072582,"size":33512}