
3b5d27d4a9b7b5801fc2d21df7fcb73fae3f6a84	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"2e3125c6dccdffc8a4ffe05b075a98e1\"}","integrity":"sha512-hw1/bGpsZQA8EKsCiqr2NrhsME/TA0gP3IjLuhjNrn9U7ywfkz5NGXJscYbHKmSrmrln2wdXwDers2NbxoNIFQ==","time":1754205174361,"size":87661}