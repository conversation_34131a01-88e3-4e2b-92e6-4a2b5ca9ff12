
32979d8ffc9c8d2e6d0ea95d9998dc1864d807aa	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.b56796794c846a8a0494.hot-update.js\",\"contentHash\":\"b3afb279e70bcb3e91c7baa6bcf4368b\"}","integrity":"sha512-FxxTj8KQB0b7E4VDhTY7wwDb6lszuFbBzUvs1qHvY5+VAsTW86tRBoHyCwwTOivlX+5XWeH/tGVMcdr4tks8vg==","time":1754204173552,"size":24859}