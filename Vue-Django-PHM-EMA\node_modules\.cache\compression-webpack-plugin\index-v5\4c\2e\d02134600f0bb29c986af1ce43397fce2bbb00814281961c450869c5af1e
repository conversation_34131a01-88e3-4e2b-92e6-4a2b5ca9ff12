
198c7ccdefb753a267e118287e89becdd347e11c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"aa32f56e69d7962d90f6020149864c75\"}","integrity":"sha512-ww7XuMnC/G5EMb03q8gndDtaeEPrHB9NGh+Cp1S5yGCWes5WfYu86OQP+/xoNP1SLCdzW8u0YF9rNVM4kvEGUw==","time":1754202854158,"size":1849955}