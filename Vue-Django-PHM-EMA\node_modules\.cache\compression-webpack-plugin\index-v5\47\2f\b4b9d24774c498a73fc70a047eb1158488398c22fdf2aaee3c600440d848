
ab2bda3c3e93b14ed1bb31acaf9f6ad231497d06	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"app.ddda577373b6380a0c2a.hot-update.js\",\"contentHash\":\"ad3b9b26b7717e50cb5763544d6ff8a2\"}","integrity":"sha512-mgMFic8ft/KxP/mEwRqFT0YBxCAYmrlnGrGK6uOH49rFPnFNX/YLND4zWn1wGyueBZcd5ddojkuRQR5KbNA+XQ==","time":1754202853907,"size":14056}