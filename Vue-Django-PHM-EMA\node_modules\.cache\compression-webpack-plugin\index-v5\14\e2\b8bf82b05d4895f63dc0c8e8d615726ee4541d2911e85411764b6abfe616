
c9a72924250fd819348df35cedea436768f1ee5f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"47992a0ab9c5c9ed39f8cfd342e5d9aa\"}","integrity":"sha512-4c9roTC7SCmafu1wpaf9Iz0wLusR9WeaHpkfkPkGbAG9NpZNxpx7/0HAh0JL8ioIuiS+hgIIShr8bN+1LH+sOw==","time":1754201112048,"size":23525}