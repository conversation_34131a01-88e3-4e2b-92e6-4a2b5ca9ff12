
2f3aa73aea673353c0d39e23c995c5f1b50326f3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"d5195005ff30aa1ce5602d7e9cec234b\"}","integrity":"sha512-l1yh6wURxe8KI1Y4l7Y366c1DYj87eHkdAh0PwnI4/OaTWNZFQ2XlyD+EzY0jyuyEWl9vMqGKLWjWo28VpSFAw==","time":1754203664264,"size":23560}