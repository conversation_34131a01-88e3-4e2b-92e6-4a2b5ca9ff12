
b48768935ca65f13b0cb7cd87aab82ac8b89b264	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"c3a4c27b75fec77055faa860da40962d\"}","integrity":"sha512-xqEbp3UPydEVD8TntXETwGdsr+5af7rno9KFODNHnGPE8aFJ0LXcJRAaYsWWG5hXFLiRZoWVD9JZu026Rw7S3w==","time":1754202942643,"size":23461}