
9f38c71e59f9ab85c3b3af48fdaf4bd98100292c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"2564fd6d5b24fb6e4e1f28b3be7ce9ce\"}","integrity":"sha512-OdKn42GHiBsk20TQlU9DAoEmMDZxPpYyj2TTx9a11BaJkRqfn8EVMy9x29tS8yCrInCsbZzPq4Naupyi2f8cOQ==","time":1754203211601,"size":146119}