
3904967798c961328d6c7da3fa012fa12dba5c1b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"5c20c28c94768c574dcfff6aa039efdf\"}","integrity":"sha512-TxF61S7usKT8fyJONOw/XMpQZlrncZM/gT4u37JzOXzSFTTvkkU+fzxZenY90Kloj9+Mmecdmby6wWM14j54mw==","time":1754204881560,"size":107171}