
6f1de49f95a25c7ba56446624c6f1aa7631a3b88	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"62db4d5cfa694e8fae924a92235b53d7\"}","integrity":"sha512-QUiEvv9zqmTyTQQb4iPnjvALNdO4JBeJ48LeUOCD4oPSKNuNW5UaNHJzNhVKmwCCGXfKlTs4BnQ0E6OI83OULA==","time":1754201059745,"size":23495}