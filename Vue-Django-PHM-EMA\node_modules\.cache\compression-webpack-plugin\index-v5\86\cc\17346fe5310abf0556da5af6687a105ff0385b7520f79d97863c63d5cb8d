
91b60cee3bb502e75c61967b90fe1ce88bc2af78	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.0eb693995b4c7679cf2f.hot-update.js\",\"contentHash\":\"e0b3faec88f89d2bee7fa687ec015aa5\"}","integrity":"sha512-UjpoDx81lKYI+COW0Pbq95X+VZUH87LM3EYDbGCS4uKTjkxGvxi8oQ0ENuh9ZYTGjI+Z205onk/Z3Z6C+On13w==","time":1754204194837,"size":25114}