
072b816bd6630b55fbbc33f38d1def9918167e77	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.ebaf2a8558ab0c9bb389.hot-update.js\",\"contentHash\":\"0a9adcb709ef051a115611de5f12fe84\"}","integrity":"sha512-3pOkmAxaeP/xpCWwrGEGXKklf4cdhgh2TmxIbWku0CzGAFIAqMuhWWKmfWa14uujZveKQBl9/XNzcfye85eWVg==","time":1754204777093,"size":23359}