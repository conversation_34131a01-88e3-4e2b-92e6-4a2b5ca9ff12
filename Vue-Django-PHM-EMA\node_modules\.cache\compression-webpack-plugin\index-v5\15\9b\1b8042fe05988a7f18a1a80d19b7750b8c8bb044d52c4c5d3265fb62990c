
72e8540239bb4aa874c1d2d772035d54aea87907	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.e5d64167039ce224d5c8.hot-update.js\",\"contentHash\":\"c994e3a97a3fe353e524bbe220249674\"}","integrity":"sha512-XSw6v8EgxhxGe6a4FGNyDu0cmRuIQt6MqmU/HYAWgTVGHtaC7zzAgECzlm4DtdRwvJAOmLOBJrT3eLt8OsGuBA==","time":1754204006598,"size":27598}