
a82f009d921da26b08a9456740a38c95b4fddbbf	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.ae33f4ab22605e1f005f.hot-update.js\",\"contentHash\":\"5476bc7b1886e8b7a9f94ed9a16833eb\"}","integrity":"sha512-2Wnq9S4Y3w3QdZTyUdZQdNZKUr487l421HwYWlyy0dnwFsJGgbBHJavfAX6MGwebHfWylZqm0haxYHDZZqnMbg==","time":1754204839207,"size":29756}