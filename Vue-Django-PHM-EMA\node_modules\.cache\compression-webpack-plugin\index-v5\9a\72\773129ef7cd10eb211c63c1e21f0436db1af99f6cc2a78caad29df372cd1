
7115555d28e0236195c093b77e586cd2ee910edb	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"3d1b44697d44614e17a341415beac9eb\"}","integrity":"sha512-caJtqfl1Hsi+l4N1fkwAg8YE52ad9gaAcJKTYscsSP/Twtc4dNMgs837cX40KG+n+5gsNOazTEUHfB4xNncZ1w==","time":1754203859109,"size":150630}