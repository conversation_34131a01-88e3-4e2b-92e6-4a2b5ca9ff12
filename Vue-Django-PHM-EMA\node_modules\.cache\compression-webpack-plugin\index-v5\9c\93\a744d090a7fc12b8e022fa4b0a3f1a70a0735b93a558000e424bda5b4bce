
7d0cfe749f401e6351d05d41ae8e0fcd9eb7cabe	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.7c50275df76515c960f9.hot-update.js\",\"contentHash\":\"d3186e704e952a70c1ba2be9b7e5628c\"}","integrity":"sha512-u2lT9t6Q63xyTI+oVxFR3nTIwqn39CoXKQyixfmCE4F4FOWwBDqBti51hjkJRtK529m2d0UCjVab9errBHJ5xw==","time":1754205116989,"size":45083}