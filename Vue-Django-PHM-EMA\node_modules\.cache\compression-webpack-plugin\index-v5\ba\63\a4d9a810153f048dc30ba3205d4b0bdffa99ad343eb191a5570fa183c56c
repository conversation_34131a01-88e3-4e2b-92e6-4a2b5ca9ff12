
6752481753ef489d2156f96a06e68ac7d831d418	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F3.js\",\"contentHash\":\"c492e26227386eb03e98f40cd30a87a6\"}","integrity":"sha512-lGqTG6e5smQDnqLs0iZuUYb0XK6KagGbbXXfIfBtAWfGwaq69Yts/EaijFpVHVdAE8xyLwT6Dv6hSMJw1lWY3A==","time":1754202098619,"size":110106}