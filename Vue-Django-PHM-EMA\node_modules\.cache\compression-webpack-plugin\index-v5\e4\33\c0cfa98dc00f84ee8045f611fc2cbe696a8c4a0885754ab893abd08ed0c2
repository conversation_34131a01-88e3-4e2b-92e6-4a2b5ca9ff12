
0ac3513c775c0d85c1592898b7cfce9d518b0f6f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"0.8490cb052c3543afa27f.hot-update.js\",\"contentHash\":\"515f2c2b82d8885859ff52558d58df71\"}","integrity":"sha512-MKpJ+mFujiRVnzAAl2zOAwe9PIXIqwX7nichjOMFj0TElCW1SKNe2uBHBSGXwGC05DnnTZeZVy8aqy/Dz7oSCA==","time":1754200994083,"size":121265}