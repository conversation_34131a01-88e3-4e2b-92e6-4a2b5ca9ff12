
13d5b299bec1f9925020a85f8e1b7be101cc2d0b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"b4818c9310a634dd1c31a7f53d3aeda8\"}","integrity":"sha512-h5kD+k5G4raHMF/kFjHejDmtSGEwcI09MFFHL5nDQoP3wdLyPvxOp/2UN6HqVsyHuKFK0kbL1hpfBaTC8UId0Q==","time":1754203186095,"size":144468}