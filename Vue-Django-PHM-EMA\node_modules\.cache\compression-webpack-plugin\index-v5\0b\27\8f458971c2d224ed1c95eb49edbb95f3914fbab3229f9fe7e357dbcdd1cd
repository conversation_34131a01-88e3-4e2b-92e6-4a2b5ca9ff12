
d180d9698e0c172144cfd0fb1539166a2b612836	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"0.8490cb052c3543afa27f.hot-update.js\",\"contentHash\":\"515f2c2b82d8885859ff52558d58df71\"}","integrity":"sha512-hrJNf359pvAeyU+aF1HLFRtE24MqOgAemAiwZ1fKwZ3KNB85SMRpfyJuYLP20NtPowGyQgx8LRa9OrW3wovhkA==","time":1754200994519,"size":103715}