
40a5bbb9be5c2a368e63259632b9cfd3d4317b64	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"3.19f0983600a11a516428.hot-update.js\",\"contentHash\":\"39fb2b0776bf1d68c0a64b9f836e9697\"}","integrity":"sha512-Kkf2SSaEz4yHIaJCSWprEi+o+93Z2c5XRpmD5b1qh1FI17bAToucHOwQFYcLg3DkAnolNaz+rXnbiZshJIrfSg==","time":1754202098192,"size":42579}