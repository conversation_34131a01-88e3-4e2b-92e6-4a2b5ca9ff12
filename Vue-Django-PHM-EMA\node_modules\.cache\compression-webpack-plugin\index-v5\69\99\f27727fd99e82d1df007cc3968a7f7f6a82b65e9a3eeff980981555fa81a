
37a77d00c5da724a2e845fdcb1b6ba415369aeef	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.4a687960aee179477df9.hot-update.js\",\"contentHash\":\"4d798031aaeb24cfc9aa7401dbd48f63\"}","integrity":"sha512-1srMCXuvSJS+iDlk1uOYKGyaw086Mt68zQriBvc19RYWJ4fHp0Nq/qKPw35Ic1Z3zGGpYgVW93Z4dsHZlmIZkQ==","time":1754203463256,"size":22489}