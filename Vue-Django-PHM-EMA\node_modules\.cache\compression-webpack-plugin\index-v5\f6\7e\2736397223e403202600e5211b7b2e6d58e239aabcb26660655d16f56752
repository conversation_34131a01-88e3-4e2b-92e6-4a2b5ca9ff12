
64907dd5c53cfd1b0d403c59f05bcefb6e0c6dcc	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"399a1cfff4b6148e0b5df02c01c3b73c\"}","integrity":"sha512-6BDTCM81EQUiec6K4EWOnouVD0/KzEcdyPVmnT8Xz7+3IwGUK48a7AD+U62DJe3LhhXUfZElQDObL26fwq93cg==","time":1754203231602,"size":23496}