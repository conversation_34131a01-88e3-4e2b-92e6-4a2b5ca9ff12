
88bcb8c72519c9a6ad9b9108c112fd7b48837dad	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"3.19f0983600a11a516428.hot-update.js\",\"contentHash\":\"39fb2b0776bf1d68c0a64b9f836e9697\"}","integrity":"sha512-D8toHSweYRQu23t/hY9tdLN5mOI1kMltEtOvV0hJhl9uXz8S9T0MhH6Ua9464+Kal/QghXyCGgMSgId2OZl4Zg==","time":1754202098574,"size":36992}