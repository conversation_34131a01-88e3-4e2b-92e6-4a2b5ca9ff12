
9c129900cc0fd171aa6f721362a24184f3afb854	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"72097c49e4f2e4e1d387d7f382e29eef\"}","integrity":"sha512-Ao3GQO2WQVLOLpFAO8WSNLlTRmu2vi3QnLucoOm/oHqyeNyUlztU0lpPomaoiTeZx5NWNZuYXCpSkVBw0qzyqA==","time":1754204790615,"size":23538}