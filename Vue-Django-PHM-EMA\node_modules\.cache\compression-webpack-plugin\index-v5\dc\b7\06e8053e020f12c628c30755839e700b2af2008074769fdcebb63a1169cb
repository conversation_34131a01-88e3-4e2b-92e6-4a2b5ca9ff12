
e7a4f6e929cefb207b7ed93288fc7db77f40b44a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"99dce5629236ee82996e8cb890dee6cc\"}","integrity":"sha512-BI4x4zHRVtY8hZT/8dLG0pi6nKkEzjp/ysfKJFepMHewjHA/8uol+7OuB2La28y5r750f7ukMkwMhZLDbkAo7g==","time":1754202961168,"size":144421}