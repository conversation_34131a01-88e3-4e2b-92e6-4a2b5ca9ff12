
e3abd1ebc45eceac8b3a86302fb3701e8f804241	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"fea51bbd00dd219411610bfa850d28f0\"}","integrity":"sha512-wR9gg253jF0ro0f/kHGwZTS6VSq9ADKnQGnjbeaWQRFqCjjAwxKud3wCX0kX7VVtHPW6BHTG1ODREHd4WV5YKQ==","time":1754202854644,"size":23561}