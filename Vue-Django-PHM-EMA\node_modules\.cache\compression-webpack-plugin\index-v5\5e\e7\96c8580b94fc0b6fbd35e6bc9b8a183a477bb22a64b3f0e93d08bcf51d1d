
20a29d775d15b79294ab0b4425224695bb1f6326	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"1935e5808a73ed6cc8783d8258e1ebc6\"}","integrity":"sha512-aEivXwjjNjthT1fbg7nVXAUN/a5bs/mSou7a23PFXPewwnY0ZGlDHIiQolz5UcFObMZ0WkF3Yg+Jqd79Aq7c9w==","time":1754205498262,"size":116049}