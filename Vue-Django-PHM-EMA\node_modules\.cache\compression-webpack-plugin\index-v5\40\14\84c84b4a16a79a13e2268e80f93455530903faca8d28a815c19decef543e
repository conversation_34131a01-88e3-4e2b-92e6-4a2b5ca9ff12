
a19fa45b316b831e14e37e90655ddfc922abe4c3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"fdc2f53b786fa036cddd730600c06fb0\"}","integrity":"sha512-hag60zvWL8FsLAFA3ptUnQs6lugtOMbEdc4eQ6b41HNTikgBM4q3zSP1yCoYCoEmPHpVEJ5S8CK+2f3QbWeBEg==","time":1754201112493,"size":278167}