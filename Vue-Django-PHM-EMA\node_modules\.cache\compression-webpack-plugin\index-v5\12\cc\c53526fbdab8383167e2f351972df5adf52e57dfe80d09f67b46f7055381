
024a4de5fc37ed2cc842bc01a988b57631f416f9	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"82d1d7de942b0e455810fc6f09694a72\"}","integrity":"sha512-JDV6nGTT4fZlwmAyTQ2Xt0VSqTxLDbmn81ld1sdJulVzDlM3q7y+Mq0A9AMs4rZw/F6bG0vIcdh1pDFUDaKx4w==","time":1754205424912,"size":87720}