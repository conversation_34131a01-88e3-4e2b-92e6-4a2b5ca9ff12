
bf8d7b105b0968cd1cb02c2c59037f012ecf4d43	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"a28c2fedc0ad77de5cb8388389c4a86d\"}","integrity":"sha512-tnlY9Qk7nn5uzTNpZQjYU2zazaMBlTpVbck48hzeJzGD+BQOhfH/8HfpPkXbdWDNB+5ewly5yRVPFnT1RqiwQQ==","time":1754204815194,"size":107310}