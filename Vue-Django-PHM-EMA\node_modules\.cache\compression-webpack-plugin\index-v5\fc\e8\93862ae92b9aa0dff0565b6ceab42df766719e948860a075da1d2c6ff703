
bd379601fc15c8742bc0cac88546b8ba76d5c1cd	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"dfc4510295654083b87f6c8ee010ecc7\"}","integrity":"sha512-DKSfK6IG5ilWoA1DRiUzoNjaqcEMlApvd/aAixYSAS7CuWPHNFDsU+v37RybRn3xj4PQwtYe8z2EiXZpXfsczA==","time":1754202961168,"size":26719}