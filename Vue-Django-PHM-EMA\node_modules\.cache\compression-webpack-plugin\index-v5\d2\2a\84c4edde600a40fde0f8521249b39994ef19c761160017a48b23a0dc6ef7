
70263da41e7cfe8332560682229cdb066b05b24a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"bc9055fe9fd68411bcaa841fbbd58079\"}","integrity":"sha512-W+O7eZduk17X9i7N5MIpOZ4sIGTtOTc3QZJ0dB/ZJ6+RfF38sT7dyNrVAhFqcdXKtCi950Iq9iUwHEp7YP3cyw==","time":1754203859108,"size":26704}