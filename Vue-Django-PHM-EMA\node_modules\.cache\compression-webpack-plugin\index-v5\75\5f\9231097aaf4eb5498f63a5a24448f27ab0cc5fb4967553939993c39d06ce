
95c7d8682f1d9bdd7bba6bf60a364fdd5b0eea2c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.7f343c41ac77da53c5bd.hot-update.js\",\"contentHash\":\"dd26ece9846b0f03f8387fb3f3844d4c\"}","integrity":"sha512-b+GsdQfyUcWFi0DMj/hysoOMlm5yBxh2v/PjtNdzfXkbi9DD3RmVjmdKyNQhdei/IZHHaL160pZ98vOzZPTBHA==","time":1754202866853,"size":105914}