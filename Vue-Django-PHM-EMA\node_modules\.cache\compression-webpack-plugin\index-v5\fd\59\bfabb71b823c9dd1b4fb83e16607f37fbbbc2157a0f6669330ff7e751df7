
b38dea840ca5d43da0d32133db5193985ee24b0c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"7116fd8b2e33e79ca7d632290abe14f0\"}","integrity":"sha512-DKdIRYTvM7iLPqMyJvCRjU5LLpTBV22Fqt6pP+yUezeRG410CJtizFXi6cnwkEALl5kFyCU+Jwb0ggEjt+JO8Q==","time":1754204777093,"size":23493}