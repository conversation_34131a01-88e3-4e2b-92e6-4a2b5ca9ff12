
51c9713dd5f15ee248b4f1c5279c4bd9cb38b023	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"54320d001500745eda5f4be2e16d8d87\"}","integrity":"sha512-274N7WbAgwP7x8tKUmxCin0KNeWs3dNo4x7JOEoA01qdiPo5QK9z8LPqbkhOP7BpKRtTa/Ncsk6JKKJS9BJMKg==","time":1754204298849,"size":26781}