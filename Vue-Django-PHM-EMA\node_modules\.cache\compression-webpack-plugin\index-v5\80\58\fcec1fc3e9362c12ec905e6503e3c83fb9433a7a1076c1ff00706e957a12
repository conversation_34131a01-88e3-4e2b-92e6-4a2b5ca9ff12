
778f2b33f9fb9a1c078cecbfcb9415e96a4dd209	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"bd26c53b6bc43dd6fc60b8c031da0b00\"}","integrity":"sha512-wJcNgrY3wHTa6pjD663EPGHMZDQ8FZ7+d6z+gS8H7h+kSwd205oiwKTQi/7LUNKjHitn4e+pFfWSSmpf+3728Q==","time":1754204281583,"size":117680}