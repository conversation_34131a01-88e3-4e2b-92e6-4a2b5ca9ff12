
274d037c4a89adb5df8564c0c7c03f285f8e3fed	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"62db4d5cfa694e8fae924a92235b53d7\"}","integrity":"sha512-o2ivvYO2Z4ifqN0VhsNueo2fIqXeDx8CR5sY4CFQ1PFZIUaspendVpSojMmMNet38oTfm+lgzkXOm2j2zQScmQ==","time":1754201059343,"size":26765}