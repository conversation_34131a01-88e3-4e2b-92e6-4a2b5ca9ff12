
c68d84e14e3279e70bf844221db2df969aca3156	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"bd26c53b6bc43dd6fc60b8c031da0b00\"}","integrity":"sha512-jg5wYrVW0smzTDDA7GKHaGAxzCtKtYUZxpl6Cp1frHE4xBj+jBiBP7TkzMGCxqfURpemfyhBaUXaL1yuiqfuug==","time":1754204281218,"size":155361}