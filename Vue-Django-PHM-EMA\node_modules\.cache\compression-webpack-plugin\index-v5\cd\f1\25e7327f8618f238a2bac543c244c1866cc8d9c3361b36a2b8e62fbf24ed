
3011cff0fbeb891d9793948ab30d3b372cf0a858	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.f8959224dcf62210d31d.hot-update.js\",\"contentHash\":\"a3ce034d9ea28e399b3c20fbc0409a88\"}","integrity":"sha512-m4c3Q+b55JlpcmhzQH42aDorc2NlNvIZK93fyp5hUGhdYpNN1iN2CrV3Jn7ZCnZ7xnIzXbDhayGdIUaYFrqeYg==","time":1754203845100,"size":30641}