
d58d9759a1eda4d9538203277f610246f75ca54f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"a310d8c5e7f4318e9732726ea8e68f60\"}","integrity":"sha512-M5s6/jsdRZpx9bHpBvJEH1hL8fLk/08StHT5zLboun7NtmzbZZQLkbDC97QOxkElID4tdEHOIgR5GwEs50h2yg==","time":1754204407656,"size":26674}