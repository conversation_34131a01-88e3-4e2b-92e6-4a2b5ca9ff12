
5898bea31e8cf19418523353d5373441740e6d26	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"ef35d471c884bd66ef652db3f2ca1483\"}","integrity":"sha512-NGUl4Vy5/cz6p5RMe63Nenp/iz4CXsvQjIvsbs8EfjYobQajX9gjduYiejgN2s1RuU4kztwegXjjmQTh1iXP0g==","time":1754201072936,"size":23593}