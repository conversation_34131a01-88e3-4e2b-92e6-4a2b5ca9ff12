
1c2b47ab3cc7c06121749adfb63fb32bb2974509	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"0371224a184c03bfa74b7ec2c341f393\"}","integrity":"sha512-vPDdM3mwxngjw4iRGkdaTQyVNv3mdjzxdhDhloCT2ht1siJrCmVhqBXkxMGPMXBg7zatK4x6+dftjsv/gBGphQ==","time":1754206026372,"size":23564}