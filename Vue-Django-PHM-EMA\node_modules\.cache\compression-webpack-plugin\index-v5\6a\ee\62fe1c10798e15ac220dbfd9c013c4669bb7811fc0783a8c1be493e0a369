
6a841990538e6cb34f0844758a92bd180b8a88bd	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"6d2a8d17407ca5fa0c37fe299a2f6cfc\"}","integrity":"sha512-JDLMtGuLx8GyTa7+ZgDwWZIvD/RzNBXOT/GYDMIw7CJujgTdwefsVW2nFY+/KrQDksdfIi+M+UVHuf29Mna7IQ==","time":1754204776713,"size":97465}