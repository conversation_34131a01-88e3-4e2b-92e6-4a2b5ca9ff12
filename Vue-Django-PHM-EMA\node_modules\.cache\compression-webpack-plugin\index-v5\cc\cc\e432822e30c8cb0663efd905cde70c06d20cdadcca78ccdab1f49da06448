
b9106c370b62242cae526dfdb82e36573262c11e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"99dce5629236ee82996e8cb890dee6cc\"}","integrity":"sha512-aeMNqHqqZPUdzzRKXTh6EltbEU/mmbxE2+89p6rpSEl+5KhCOW9y3FPIiEqzy3jrvWHwQy+Lj01vfClwk8FlYw==","time":1754202961700,"size":110819}