
512fdfe7d4b039a353d70c07ef1c0611e1df901f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"3a63c34be1c3cfb04defef5011e3fc77\"}","integrity":"sha512-gDG11jZ0DJGp4sB7zxUv1PDDicvxJWNvHOFZRhICB+PlsQCxMjXuolwYUZ5nPIEV1VuOJblwrsnD00K/plNH/w==","time":1754206072113,"size":23511}