
7b947d95aa71dfce38a0709583264379dc9cace4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"ea9e8cf1f3904a26e9790353741dc0c1\"}","integrity":"sha512-jN1tdUcesklO1rSFHDic/DnzrW9F2N7CtxIV6BKQwH1AzELoCcePlfFzkWRhHGuv2vS98TjpCEAAjx9Sj7ZXmA==","time":1754204505207,"size":114802}