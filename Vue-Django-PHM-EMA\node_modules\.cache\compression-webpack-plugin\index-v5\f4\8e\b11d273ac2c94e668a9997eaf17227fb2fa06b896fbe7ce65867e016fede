
d7599ec627276efff01805795facdfbfcd53d2b3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"40ef43f4f0418b871e79e2364aa70f6d\"}","integrity":"sha512-mex8zYhyvtdbbepoPp9tAhQ20WWTD0CGU4j647/mUSnIEGRujE42KCnFlmRksDgad1lS2hxyCvD0Tok/ntl9Lg==","time":1754200969955,"size":378989}