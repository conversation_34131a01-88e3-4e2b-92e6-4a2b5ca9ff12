
a5e7d298b1c90eec99f45575083f0ae10b937c24	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.fc5f0e5f77a486e5798f.hot-update.js\",\"contentHash\":\"5dc302068ce1c30ecd27e7686dea3fb0\"}","integrity":"sha512-l+hE/PybNiF5EZVwcPxW8emiJe0XG5pxaNDK9l2bJj8E+3voIE460lRwHj60VeR61p50tj1mTN6sL31z/SvGYg==","time":1754204764920,"size":13749}