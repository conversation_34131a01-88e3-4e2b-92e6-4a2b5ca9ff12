{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\life\\index.vue?vue&type=style&index=0&id=13ddbe19&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\life\\index.vue", "mtime": 1754206054570}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1634626957199}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1634627893377}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1634627525156}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1634627658274}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmxpZmUtY29udGFpbmVyIHsKICBwYWRkaW5nOiAyMHB4Owp9CgouZGFzaGJvYXJkLXJvdyB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmhlYWx0aC10cmVuZC1jYXJkLAouZ2F1Z2UtY2FyZCwKLnJ1bC1jYXJkLAouY29tcG9uZW50cy1jYXJkIHsKICBoZWlnaHQ6IDM1MHB4OwoKICAuY2hhcnQtY29udGFpbmVyIHsKICAgIGhlaWdodDogMzAwcHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgcG9zaXRpb246IHJlbGF0aXZlOwoKICAgIC5jaGFydCB7CiAgICAgIHdpZHRoOiAxMDAlOwogICAgICBoZWlnaHQ6IDEwMCU7CiAgICB9CgogICAgLmVtcHR5LWNvbnRlbnQgewogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgIHRvcDogMDsKICAgICAgbGVmdDogMDsKICAgICAgcmlnaHQ6IDA7CiAgICAgIGJvdHRvbTogMDsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTsKICAgICAgei1pbmRleDogMTsKICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICBjb2xvcjogIzkwOTM5OTsKCiAgICAgIGkgewogICAgICAgIGZvbnQtc2l6ZTogNDhweDsKICAgICAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgICAgIGNvbG9yOiAjYzBjNGNjOwogICAgICB9CgogICAgICBwIHsKICAgICAgICBtYXJnaW46IDVweCAwOwogICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgfQoKICAgICAgLnRpcCB7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGNvbG9yOiAjYTBhNGE5OwogICAgICB9CiAgICB9CiAgfQp9CgoucnVsLWNvbnRlbnQgewogIHBhZGRpbmc6IDIwcHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwoKICAucnVsLXZhbHVlIHsKICAgIGZvbnQtc2l6ZTogMzJweDsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICBjb2xvcjogIzQwOUVGRjsKICB9CgogIC5ydWwtZGVzY3JpcHRpb24gewogICAgbWFyZ2luLXRvcDogMjBweDsKICAgIGNvbG9yOiAjNjA2MjY2OwogICAgZm9udC1zaXplOiAxNHB4OwogIH0KfQoKLmFjdGlvbnMtY29udGFpbmVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgbWFyZ2luLXRvcDogMjBweDsKCiAgLmVsLWJ1dHRvbiB7CiAgICBtYXJnaW46IDAgMTBweDsKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqfA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/life", "sourcesContent": ["<template>\n  <div class=\"life-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-data-analysis\"></i>\n        寿命预测与健康评估\n        <span class=\"page-subtitle\">Life Prediction & Health Assessment</span>\n      </h2>\n    </div>\n\n    <!-- 健康度展示区域 -->\n    <el-row :gutter=\"20\" class=\"dashboard-row\">\n      <el-col :span=\"16\">\n        <el-card class=\"health-trend-card\">\n          <div slot=\"header\">\n            <span>系统健康度趋势</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"healthTrendChart\" class=\"chart\" />\n            <div v-if=\"!healthData.trend || healthData.trend.length === 0\" class=\"empty-content\">\n              <i class=\"el-icon-data-line\"></i>\n              <p>暂无健康度趋势数据</p>\n              <p class=\"tip\">请先进行故障诊断</p>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"8\">\n        <el-card class=\"gauge-card\">\n          <div slot=\"header\">\n            <span>当前健康度</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"healthGaugeChart\" class=\"chart\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" class=\"dashboard-row\">\n      <el-col :span=\"12\">\n        <!-- RUL预测展示 -->\n        <el-card class=\"rul-card\">\n          <div slot=\"header\">\n            <span>剩余使用寿命预测</span>\n          </div>\n          <div v-loading=\"loading\" class=\"rul-content\">\n            <h2 class=\"rul-value\">{{ rulValue }} 小时</h2>\n            <el-progress :percentage=\"rulPercentage\" :format=\"format\" :color=\"rulColor\"></el-progress>\n            <p class=\"rul-description\">预计剩余使用寿命，基于当前系统运行状态</p>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"12\">\n        <!-- 部件健康状态 -->\n        <el-card class=\"components-card\">\n          <div slot=\"header\">\n            <span>关键部件健康状态</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"componentsHealthChart\" class=\"chart\" />\n            <div v-if=\"!healthData.components || healthData.components.length === 0\" class=\"empty-content\">\n              <i class=\"el-icon-data-analysis\"></i>\n              <p>暂无部件健康状态数据</p>\n              <p class=\"tip\">请先进行故障诊断</p>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 报告生成按钮 -->\n    <div class=\"actions-container\">\n      <el-button type=\"primary\" icon=\"el-icon-document\" :loading=\"reportGenerating\" @click=\"generateReport\">\n        生成健康评估报告\n      </el-button>\n      <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"fetchData(true)\">\n        刷新数据\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport { getHealthPrediction, generateHealthReport } from '@/api/health'\nimport { saveAs } from 'file-saver'\n\nexport default {\n  data() {\n    return {\n      healthTrendChart: null,\n      healthGaugeChart: null,\n      componentsHealthChart: null,\n      rulValue: '0',\n      rulPercentage: 0,\n      rulColor: '#409EFF',\n      loading: false,\n      reportGenerating: false,\n      healthData: {\n        trend: [],\n        current: 0,\n        components: []\n      }\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initCharts()\n\n      // 检查是否有从故障诊断页面传来的数据\n      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        this.handleDiagnosisResult()\n      } else {\n        // 如果没有从故障诊断页面传来数据，则显示初始状态\n        this.resetData()\n      }\n    })\n    window.addEventListener('resize', this.resizeCharts)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.resizeCharts)\n    this.healthTrendChart && this.healthTrendChart.dispose()\n    this.healthGaugeChart && this.healthGaugeChart.dispose()\n    this.componentsHealthChart && this.componentsHealthChart.dispose()\n  },\n  methods: {\n    initCharts() {\n      console.log('初始化图表...')\n      this.healthTrendChart = echarts.init(document.getElementById('healthTrendChart'))\n      this.healthGaugeChart = echarts.init(document.getElementById('healthGaugeChart'))\n      this.componentsHealthChart = echarts.init(document.getElementById('componentsHealthChart'))\n\n      // 设置仪表盘容器样式，确保仪表盘居中显示\n      document.getElementById('healthGaugeChart').parentNode.style.display = 'flex'\n      document.getElementById('healthGaugeChart').parentNode.style.justifyContent = 'center'\n      document.getElementById('healthGaugeChart').parentNode.style.alignItems = 'center'\n\n      this.updateCharts()\n      window.addEventListener('resize', this.resizeCharts)\n    },\n    fetchData(isReset = false) {\n      this.loading = true\n      console.log('开始获取健康评估数据... reset:', isReset)\n\n      // 构造请求参数\n      const params = {}\n\n      // 如果是重置请求\n      if (isReset) {\n        params.reset = 'true'\n      } else if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        const diagnosisResult = this.$store.getters['diagnosis/currentDiagnosisResult']\n        if (diagnosisResult && diagnosisResult.success) {\n          const fault_mode = diagnosisResult.diagnosis_details.conclusion.predicted_fault_mode\n          params.fault_mode = fault_mode\n        }\n      }\n\n      // 调用API获取健康评估和寿命预测数据\n      getHealthPrediction(params)\n        .then(response => {\n          console.log('API返回数据:', response)\n          if (response.code === 200) {\n            const data = response.data\n\n            // 更新健康度趋势数据\n            this.healthData.trend = data.health_trend || []\n\n            // 更新当前健康度\n            this.healthData.current = data.current_health || 0\n\n            // 更新部件健康状态数据\n            this.healthData.components = data.component_health || []\n\n            // 更新RUL预测结果，将小数舍入为整数\n            this.rulValue = data.rul_hours ? Math.round(parseFloat(data.rul_hours)) : '0'\n            this.rulPercentage = data.rul_percentage ? Math.round(parseFloat(data.rul_percentage)) : 0\n\n            // 如果是从故障诊断页面跳转来的，标记诊断结果已处理\n            if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n              this.$store.dispatch('diagnosis/processDiagnosisResult')\n            }\n\n            this.updateCharts()\n          } else {\n            console.error('API返回错误:', response)\n            this.$message.error(response.message || '获取数据失败')\n            // 不再调用mockData，改为显示初始状态\n            this.resetData()\n          }\n        })\n        .catch(error => {\n          console.error('获取健康评估数据失败:', error)\n          this.$message.error('获取数据失败，请稍后重试')\n          // 显示详细错误信息\n          console.log('错误详情:', {\n            message: error.message,\n            stack: error.stack,\n            response: error.response && {\n              status: error.response.status,\n              statusText: error.response.statusText,\n              data: error.response.data\n            }\n          })\n          // 不再调用mockData，改为显示初始状态\n          this.resetData()\n        })\n        .finally(() => {\n          this.loading = false\n        })\n    },\n    // 处理从故障诊断页面传来的诊断结果\n    handleDiagnosisResult() {\n      const diagnosisResult = this.$store.getters['diagnosis/currentDiagnosisResult']\n      console.log('处理故障诊断结果:', diagnosisResult)\n\n      if (diagnosisResult && diagnosisResult.success) {\n        // 获取故障模式\n        const fault_mode = diagnosisResult.diagnosis_details.conclusion.predicted_fault_mode\n\n        // 获取健康评估结果\n        this.fetchData()\n\n        // 显示通知\n        this.$notify({\n          title: '诊断结果应用',\n          message: `已根据诊断结果(${this.getFaultModeName(fault_mode)})更新健康评估`,\n          type: 'info',\n          duration: 5000\n        })\n      }\n    },\n    // 重置数据为初始状态\n    resetData() {\n      this.healthData.trend = []\n      this.healthData.current = 0\n      this.healthData.components = [\n        { name: '电机', value: 0 },\n        { name: '控制器', value: 0 },\n        { name: '减速器', value: 0 },\n        { name: '传感器', value: 0 }\n      ]\n      this.rulValue = '0' // 已经是整数\n      this.rulPercentage = 0\n      this.updateCharts()\n    },\n    // 获取故障模式名称\n    getFaultModeName(fault_mode) {\n      // 从故障诊断的faultModeMap获取故障名称\n      const faultModeMap = {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n\n      return faultModeMap[fault_mode] || '未知故障'\n    },\n    updateCharts() {\n      // 更新健康度趋势图表\n      this.healthTrendChart.setOption({\n        title: {\n          text: ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: this.healthData.trend.map(item => item[0]),\n          name: '检测时间点',\n          nameLocation: 'middle',\n          nameGap: 30,\n          axisLabel: {\n            interval: 'auto',\n            rotate: 45,\n            formatter: (value) => {\n              // 只显示月份和日期，不显示年份\n              const date = new Date(value)\n              if (!isNaN(date)) {\n                return `${date.getMonth() + 1}月${date.getDate()}日`\n              }\n              return value\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '健康度',\n          min: 0,\n          max: 100\n        },\n        grid: {\n          bottom: '15%' // 为旋转后的横轴标签留出更多空间\n        },\n        series: [{\n          name: '系统健康度',\n          type: 'line',\n          data: this.healthData.trend.map(item => item[1]),\n          markLine: {\n            data: [{\n              yAxis: 60,\n              lineStyle: { color: '#FF9800' },\n              label: { formatter: '警戒线' }\n            }]\n          },\n          lineStyle: {\n            width: 3,\n            color: '#409EFF'\n          },\n          smooth: true\n        }]\n      })\n\n      // 更新健康度仪表盘\n      this.healthGaugeChart.setOption({\n        series: [{\n          type: 'gauge',\n          data: [{ value: this.healthData.current, name: '健康度' }],\n          min: 0,\n          max: 100,\n          axisLine: {\n            lineStyle: {\n              width: 30,\n              color: [\n                [0.3, '#FF5722'],\n                [0.7, '#FF9800'],\n                [1, '#4CAF50']\n              ]\n            }\n          },\n          radius: '85%', // 缩小仪表盘半径，为刻度标签留出更多空间\n\n          detail: {\n            formatter: '{value}%',\n            fontSize: 28,\n            fontWeight: 'bold',\n            offsetCenter: [0, '70%'], // 将数值放在下方\n            color: '#333', // 使用更深的颜色提高对比度\n            textShadow: '0 0 3px rgba(255,255,255,0.5)' // 添加文字阴影提高可读性\n          },\n          title: {\n            fontSize: 18,\n            fontWeight: 'normal',\n            offsetCenter: [0, '50%'], // 将标题放在数值上方\n            color: '#333'\n          },\n          pointer: {\n            width: 6, // 增加指针宽度提高可见性\n            length: '75%' // 稍微缩短指针，避免与刻度重叠\n          },\n          axisTick: {\n            length: 6, // 减小刻度线长度，避免与指针重叠\n            lineStyle: {\n              width: 2, // 保持刻度线宽度\n              color: '#666' // 加深刻度线颜色\n            },\n            distance: -8 // 向内偏移刻度线，确保不与刻度数字重叠\n          },\n          splitLine: {\n            length: 18, // 增加分割线长度，让刻度标签与分割线有更明显的分隔\n            lineStyle: {\n              width: 3, // 增加分割线宽度\n              color: '#666' // 加深分割线颜色\n            },\n            distance: -3 // 向内偏移分割线，远离刻度数字\n          },\n          axisLabel: {\n            distance: 30, // 进一步增加标签与刻度线的距离\n            fontSize: 12, // 稍微减小字体大小以减少重叠\n            color: '#333', // 加深文字颜色提高对比度\n            formatter: function(value) {\n              // 减少刻度显示，只显示0、20、40、60、80、100的刻度值\n              if (value % 20 === 0) {\n                return value.toFixed(0) + ''\n              } else {\n                return '' // 其他刻度不显示数字\n              }\n            },\n            backgroundColor: 'rgba(255, 255, 255, 0.8)', // 添加半透明背景色\n            padding: [2, 4], // 添加内边距\n            borderRadius: 3, // 圆角边框\n            textShadow: '0 0 2px #fff' // 添加文字阴影提高可读性\n          }\n        }]\n      })\n\n      // 更新部件健康状态柱状图\n      this.componentsHealthChart.setOption({\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        xAxis: {\n          type: 'category',\n          data: this.healthData.components.map(item => item.name)\n        },\n        yAxis: {\n          type: 'value',\n          name: '健康度',\n          min: 0,\n          max: 100\n        },\n        series: [{\n          type: 'bar',\n          data: this.healthData.components.map(item => ({\n            value: item.value,\n            itemStyle: {\n              color: this.getHealthColor(item.value)\n            }\n          })),\n          label: {\n            show: true,\n            position: 'top',\n            formatter: '{c}%'\n          }\n        }]\n      })\n    },\n    getHealthColor(value) {\n      if (value >= 80) {\n        return '#4CAF50'\n      } else if (value >= 60) {\n        return '#FF9800'\n      } else {\n        return '#FF5722'\n      }\n    },\n    resizeCharts() {\n      this.healthTrendChart && this.healthTrendChart.resize()\n      this.healthGaugeChart && this.healthGaugeChart.resize()\n      this.componentsHealthChart && this.componentsHealthChart.resize()\n    },\n    format(percentage) {\n      return percentage + '%'\n    },\n    generateReport() {\n      this.reportGenerating = true\n      console.log('开始生成健康评估报告...')\n\n      // 调用API生成健康评估报告\n      generateHealthReport({\n        health_data: {\n          current_health: this.healthData.current,\n          component_health: this.healthData.components,\n          rul_hours: this.rulValue,\n          rul_percentage: this.rulPercentage\n        }\n      })\n        .then(response => {\n          console.log('报告生成成功, 响应类型:', response.headers['content-type'])\n          // 使用file-saver库将二进制数据保存为Excel文件\n          const blob = new Blob([response.data], {\n            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n          })\n\n          // 生成文件名：健康评估报告_年月日时分秒.xlsx\n          const now = new Date()\n          const fileName = `健康评估报告_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}.xlsx`\n\n          saveAs(blob, fileName)\n\n          this.$message.success('健康评估报告生成成功')\n        })\n        .catch(error => {\n          console.error('生成健康评估报告失败:', error)\n          // 显示详细错误信息\n          console.log('错误详情:', {\n            message: error.message,\n            stack: error.stack,\n            response: error.response && {\n              status: error.response.status,\n              statusText: error.response.statusText,\n              data: error.response.data\n            }\n          })\n          this.$message.error('生成报告失败，请稍后重试')\n        })\n        .finally(() => {\n          this.reportGenerating = false\n        })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.life-container {\n  padding: 20px;\n}\n\n.dashboard-row {\n  margin-bottom: 20px;\n}\n\n.health-trend-card,\n.gauge-card,\n.rul-card,\n.components-card {\n  height: 350px;\n\n  .chart-container {\n    height: 300px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n\n    .chart {\n      width: 100%;\n      height: 100%;\n    }\n\n    .empty-content {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      background-color: rgba(255, 255, 255, 0.8);\n      z-index: 1;\n      text-align: center;\n      color: #909399;\n\n      i {\n        font-size: 48px;\n        margin-bottom: 16px;\n        color: #c0c4cc;\n      }\n\n      p {\n        margin: 5px 0;\n        font-size: 16px;\n      }\n\n      .tip {\n        font-size: 14px;\n        color: #a0a4a9;\n      }\n    }\n  }\n}\n\n.rul-content {\n  padding: 20px;\n  text-align: center;\n\n  .rul-value {\n    font-size: 32px;\n    margin-bottom: 20px;\n    color: #409EFF;\n  }\n\n  .rul-description {\n    margin-top: 20px;\n    color: #606266;\n    font-size: 14px;\n  }\n}\n\n.actions-container {\n  text-align: center;\n  margin-top: 20px;\n\n  .el-button {\n    margin: 0 10px;\n  }\n}\n</style>\n"]}]}