
84a7d650ddb10fb9970c749886453f1b2b7893e0	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"cf4379985ca695c9e2d75e74a9cfb1db\"}","integrity":"sha512-1EUnphJGef8znMn5K41WiTAJJeo+nHmYuSfMGUdO2TH7yOXjIPBKNqO5tp/oZ1leNOverq1I2velb4rKq4I1MA==","time":1754201014974,"size":23524}