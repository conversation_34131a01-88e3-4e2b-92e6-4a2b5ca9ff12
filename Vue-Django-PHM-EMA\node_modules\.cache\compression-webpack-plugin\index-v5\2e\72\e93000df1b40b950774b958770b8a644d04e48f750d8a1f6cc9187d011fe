
c84ccc5cf3d6e117450adffdd85d3ba2d46899de	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"253d73b5431cf5df79e5ede3f9a9add9\"}","integrity":"sha512-jJTBEEQM/rxACrfBryzuf8quEbPK+ilufY6k0Xe2RQs0kJB7vE8IlxK2kGjQOcEUR5m6SKdrGfqXjrpJGoVk8A==","time":1754204881559,"size":26766}