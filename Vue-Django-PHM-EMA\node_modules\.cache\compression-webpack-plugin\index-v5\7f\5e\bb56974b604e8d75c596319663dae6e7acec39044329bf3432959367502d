
18eb6649f6dcb7e8d0355dc0ab888e846577d7af	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"2.ea3a0d5b384a67a5d06f.hot-update.js\",\"contentHash\":\"7959e4fb819b17c2f7aef3521769807a\"}","integrity":"sha512-tG2W35OrlOjOgwcYkIt4FUyGoQve4tLl9MNfbboQNZkCDe2f8MO3iCiOgl9jwqj3Yx5fCF4wZx7Itbybc2Ugxg==","time":1754206042143,"size":18985}