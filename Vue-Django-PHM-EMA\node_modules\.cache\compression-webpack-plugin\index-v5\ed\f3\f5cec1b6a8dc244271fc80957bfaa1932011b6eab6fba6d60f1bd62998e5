
775e295c18d5e8d61c9b876878cab7c3af8c1c34	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"3.2f7a05f5e0fd5d17e4b4.hot-update.js\",\"contentHash\":\"95b31f06684e266ccbec9eeae5ad7d8b\"}","integrity":"sha512-9nKC5GGnAXkdHFYabqL6mDjp0uQBmZZ4nL0TSwfjbMBD7qGz7SPTNpKQu1mgFl+yw5A30XTJDnhW01DsO+Qjbw==","time":1754201768687,"size":50772}