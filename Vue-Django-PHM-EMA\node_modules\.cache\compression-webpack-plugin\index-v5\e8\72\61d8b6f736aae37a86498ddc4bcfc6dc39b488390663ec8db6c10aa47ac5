
44d4a8d5b6e3a55e0d6eac0a1844ee177ab6a404	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"9179af5130ef201bd50543b657200f01\"}","integrity":"sha512-0OU6X8qsfgeXH9mGYGcizkgbde+UhUoF1r4Nyz6rc4o2ha1BnQw7CUxaLWL/XWDH57TnwoxfGyq5F1shDhPLpg==","time":1754203463286,"size":113206}