<template>
  <div class="diagnosis-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-s-tools"></i>
        设备故障诊断
        <span class="page-subtitle">Device Fault Diagnosis</span>
      </h2>
    </div>

    <!-- 新增FMEA诊断功能 -->
    <el-card class="box-card">
      <div class="diagnosis-card">
        <el-row class="Offline" style="background:#fff">
          <el-col :span="14">
            <el-form class="selectCard">
              <el-form-item label="诊断模型">
                <el-select v-model="selectedFmeaModel" style="width:80%" clearable placeholder="请选择模型">
                  <el-option
                    v-for="item in fmeaModelOptions"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name"
                  >
                    <span style="float: left">{{ item.name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">
                      {{ item.creation_time }} ({{ item.size_mb }}MB)
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="诊断数据">
                <el-select v-model="selectedFmeaData" style="width:80%" clearable placeholder="请选择数据文件">
                  <el-option
                    v-for="item in fmeaDataOptions"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
              <el-button type="primary" @click="getFmeaData(); getFmeaModel()">刷新数据</el-button>
              <el-button type="primary" style="margin-left:15px" @click="startFmeaDiagnosis">开始诊断</el-button>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 模型管理卡片 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix model-management-header">
        <span class="model-management-title">诊断算法模型管理</span>
        <el-button
          size="medium"
          type="success"
          icon="el-icon-upload"
          @click="showUploadModelDialog"
          class="upload-model-btn"
        >
          上传新模型
        </el-button>
      </div>
      <el-table
        :data="fmeaModelOptions"
        style="width: 100%"
        border
        stripe
        class="model-management-table"
        :resizable="false"
      >
        <el-table-column prop="name" label="诊断算法模型名称" :resizable="false" />
        <el-table-column prop="creation_time" label="创建时间" :resizable="false" />
        <el-table-column prop="size_mb" label="大小(MB)" :resizable="false" />
        <el-table-column label="操作" :resizable="false">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteModel(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新FMEA诊断报告 -->
    <el-dialog title="28SY型 FMEA 诊断报告" :visible.sync="fmeaDialogVisible" width="80%" custom-class="diagnosis-report-dialog">
      <div v-if="fmeaReport && fmeaReport.success" class="diagnosis-report-content">
        <!-- 基本信息区域 -->
        <div class="report-header">
          <div class="report-meta">
            <div class="meta-item">
              <span class="meta-label"><i class="el-icon-time"></i> 诊断时间：</span>
              <span class="meta-value">{{ currentDiagnosisTime }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label"><i class="el-icon-folder"></i> 数据文件：</span>
              <span class="meta-value">{{ fmeaReport.diagnosis_details.data_file }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label"><i class="el-icon-cpu"></i> 诊断模型：</span>
              <span class="meta-value">{{ fmeaReport.diagnosis_details.model_used }}</span>
            </div>
          </div>

          <!-- 诊断结果突出显示 -->
          <div class="diagnosis-result">
            <div class="result-title">诊断结果</div>
            <div class="result-value">
              <el-tag
                :type="getFaultTagType(fmeaReport.diagnosis_details.conclusion.predicted_fault_mode)"
                size="large"
                effect="dark"
                class="result-tag"
                style="max-width: 100%; white-space: normal; height: auto; line-height: 1.5; padding: 10px 15px; font-size: 18px;"
              >
                {{ getChineseFaultName(fmeaReport.diagnosis_details.conclusion.predicted_fault_mode) }}
              </el-tag>
              <div v-if="fmeaReport.diagnosis_details.conclusion.confidence_score" class="confidence-score">
                置信度: {{ (fmeaReport.diagnosis_details.conclusion.confidence_score * 100).toFixed(1) }}%
              </div>
            </div>
          </div>
        </div>

        <!-- 诊断步骤 -->
        <div class="diagnosis-steps-section">
          <div class="section-title">诊断流程</div>
          <el-steps :active="fmeaReport.diagnosis_details.steps.length" finish-status="success" class="diagnostic-steps">
            <el-step
              v-for="step in fmeaReport.diagnosis_details.steps"
              :key="step.step"
              :title="step.description"
              :description="`耗时: ${step.duration_ms} ms`"
            />
          </el-steps>
        </div>

        <!-- 操作按钮 -->
        <div class="report-actions">
          <el-button type="primary" icon="el-icon-download" @click="downloadReport">
            下载诊断报告
          </el-button>
          <el-button
            v-if="fmeaReport.diagnosis_details.conclusion.predicted_fault_mode !== '0_normal'"
            type="success"
            icon="el-icon-view"
            @click="viewInConsole"
          >
            在3D模型中查看
          </el-button>
          <el-button type="warning" icon="el-icon-data-analysis" @click="viewHealthAssessment">
            查看健康评估
          </el-button>
        </div>
      </div>
      <div v-else-if="fmeaReport">
        <el-alert
          :title="'诊断失败: ' + fmeaReport.message"
          type="error"
          show-icon
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fmeaDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 上传模型对话框 -->
    <el-dialog title="上传新模型" :visible.sync="uploadModelDialogVisible" width="40%">
      <el-form :model="uploadModelForm" label-width="100px">
        <el-form-item label="模型文件">
          <el-upload
            ref="upload"
            class="upload-demo"
            action="#"
            :on-change="handleModelFileChange"
            :auto-upload="false"
            :limit="1"
            :file-list="modelFileList"
          >
            <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
            <div slot="tip" class="el-upload__tip">只能上传.h5格式的模型文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="自定义名称">
          <el-input v-model="uploadModelForm.newName" placeholder="可选，留空使用原文件名"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadModelDialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="modelFileList.length === 0" @click="submitUploadModel">上 传</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// LineChart组件已不再使用，可以移除
// import LineChart from './components/LineChart.vue'

export default {
  name: '设备故障诊断', // eslint-disable-line vue/name-property-casing
  components: {
    // LineChart
  },
  data() {
    return {
      // FMEA诊断所需数据
      fmeaDataOptions: [],
      fmeaModelOptions: [],
      selectedFmeaData: '',
      selectedFmeaModel: '',
      fmeaDialogVisible: false,
      fmeaReport: null,

      // 模型上传相关
      uploadModelDialogVisible: false,
      uploadModelForm: {
        newName: ''
      },
      modelFileList: [],

      // 新增属性
      currentDiagnosisTime: '',

      // 故障类型映射表
      faultModeMap: {
        '0_normal': '正常状态',
        '1_degradation_magnet': '永磁体退磁退化',
        '2_degradation_brush_wear': '电刷磨损退化',
        '3_degradation_commutator_oxidation': '换向器氧化退化',
        '4_fault_stator_short': '定子绕组短路故障',
        '5_fault_rotor_open': '转子绕组开路故障',
        '6_degradation_bearing_wear': '轴承磨损退化',
        '7_fault_bearing_stuck': '轴承卡死故障',
        '8_degradation_gear_wear': '齿轮磨损退化',
        '9_degradation_sensor_drift': '传感器漂移退化',
        '10_fault_sensor_loss': '传感器失效故障',
        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',
        '12_degradation_drive_distortion': '驱动信号失真退化',
        '13_fault_mcu_crash': 'MCU崩溃故障'
      }
    }
  },
  mounted() {
    this.getFmeaData()
    this.getFmeaModel()
  },
  methods: {
    getFmeaData() {
      this.fmeaDataOptions = []
      this.$axios.get('/phm/getOfflineData_28sy/').then(res => {
        if (res.data.code === 200) {
          this.fmeaDataOptions = res.data.data
        }
      })
    },
    getFmeaModel() {
      this.fmeaModelOptions = []
      this.$axios.get('/phm/getOfflineModel_28sy/').then(res => {
        if (res.data.code === 200) {
          this.fmeaModelOptions = res.data.data
          // 如果只有一个模型，默认选中
          if (this.fmeaModelOptions.length === 1) {
            this.selectedFmeaModel = this.fmeaModelOptions[0].name
          }
        }
      })
    },
    startFmeaDiagnosis() {
      if (!this.selectedFmeaData) {
        this.$notify({
          title: '提示',
          message: '请选择诊断数据！',
          duration: 3500,
          type: 'error'
        })
        return
      }

      const wait = this.$notify({
        title: '提示',
        duration: 0,
        message: '正在进行FMEA诊断...',
        type: 'info'
      })
      this.fmeaReport = null

      // 构建请求URL，如果选择了模型则添加模型参数
      let url = `/phm/offlineDiagnosis_28sy/?name=${this.selectedFmeaData}`
      if (this.selectedFmeaModel) {
        url += `&model=${this.selectedFmeaModel}`
      }

      this.$axios.get(url)
        .then(res => {
          wait.close()
          this.fmeaReport = res.data
          this.fmeaDialogVisible = true
          if (res.data.success) {
            // 设置当前诊断时间
            this.currentDiagnosisTime = new Date().toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            })

            this.$notify({
              title: '成功',
              message: 'FMEA诊断完成',
              duration: 4500,
              type: 'success'
            })

            // 保存诊断结果到Vuex
            this.$store.dispatch('diagnosis/saveDiagnosisResult', res.data)

            // 自动保存诊断结果到历史数据库
            this.saveDiagnosisToHistory(res.data)
          } else {
            this.$notify({
              title: '失败',
              message: `诊断出错: ${res.data.message}`,
              duration: 5000,
              type: 'error'
            })
          }
        }).catch(err => {
          wait.close()
          this.$notify({
            title: '网络错误',
            message: `请求诊断接口失败: ${err}`,
            duration: 5000,
            type: 'error'
          })
        })
    },
    getProgressBarStatus(probability) {
      if (probability > 0.7) return 'success'
      if (probability > 0.3) return 'warning'
      return 'exception'
    },

    // 模型管理相关方法
    showUploadModelDialog() {
      this.uploadModelDialogVisible = true
      this.uploadModelForm.newName = ''
      this.modelFileList = []
    },

    handleModelFileChange(file, fileList) {
      // 限制只能选择一个文件，并且必须是.h5格式
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }

      if (file.raw && !file.raw.name.endsWith('.h5')) {
        this.$message.error('只能上传.h5格式的模型文件!')
        fileList.pop()
      }

      this.modelFileList = fileList
    },

    submitUploadModel() {
      if (this.modelFileList.length === 0) {
        this.$message.error('请先选择模型文件')
        return
      }

      // 获取文件对象
      const file = this.modelFileList[0].raw

      // 创建表单数据
      const formData = new FormData()
      formData.append('model_file', file)

      if (this.uploadModelForm.newName) {
        formData.append('new_name', this.uploadModelForm.newName)
      }

      // 显示上传中提示
      const loading = this.$loading({
        lock: true,
        text: '正在上传模型...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 直接使用axios发送请求
      this.$axios.post('/phm/uploadModel_28sy/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(response => {
        loading.close()
        this.$notify({
          title: '成功',
          message: '模型上传成功',
          type: 'success',
          duration: 3000
        })
        this.uploadModelDialogVisible = false
        this.getFmeaModel() // 刷新模型列表
      }).catch(error => {
        loading.close()
        this.$notify({
          title: '失败',
          message: `模型上传失败: ${error}`,
          type: 'error',
          duration: 5000
        })
      })
    },

    handleDeleteModel(row) {
      this.$confirm(`确认删除模型 "${row.name}"?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const formData = new FormData()
        formData.append('model_name', row.name)

        this.$axios.post('/phm/deleteModel_28sy/', formData)
          .then(res => {
            if (res.data.code === 200) {
              this.$notify({
                title: '成功',
                message: res.data.message,
                type: 'success',
                duration: 3000
              })
              // 如果删除的是当前选中的模型，清空选择
              if (this.selectedFmeaModel === row.name) {
                this.selectedFmeaModel = ''
              }
              this.getFmeaModel() // 刷新模型列表
            } else {
              this.$notify({
                title: '失败',
                message: res.data.data,
                type: 'error',
                duration: 5000
              })
            }
          })
          .catch(err => {
            this.$notify({
              title: '错误',
              message: `删除模型失败: ${err}`,
              type: 'error',
              duration: 5000
            })
          })
      }).catch(() => {
        // 取消删除操作
      })
    },

    // 新增方法
    getFaultTagType(faultMode) {
      // 根据故障模式名称判断标签类型
      if (faultMode === '0_normal') {
        return 'success' // 正常状态为绿色
      } else if (faultMode.includes('degradation')) {
        return 'warning' // 退化类型为黄色
      } else if (faultMode.includes('fault')) {
        return 'danger' // 故障类型为红色
      }
      return 'info' // 默认为蓝色
    },

    getChineseFaultName(faultMode) {
      return this.faultModeMap[faultMode] || '未知故障'
    },

    downloadReport() {
      if (!this.fmeaReport || !this.fmeaReport.success) {
        this.$message.error('没有可下载的诊断报告')
        return
      }

      // 创建报告内容
      const reportData = this.fmeaReport.diagnosis_details
      const conclusion = reportData.conclusion

      // 构建HTML内容
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>28SY型 FMEA 诊断报告</title>
          <style>
            body { 
              font-family: "Microsoft YaHei", Arial, sans-serif; 
              margin: 0; 
              padding: 30px;
              background-color: #f5f7fa;
              color: #303133;
            }
            .report-container {
              max-width: 900px;
              margin: 0 auto;
              background: white;
              border-radius: 8px;
              box-shadow: 0 4px 12px rgba(0,0,0,0.1);
              padding: 30px;
            }
            .header { 
              text-align: center; 
              margin-bottom: 30px;
              padding-bottom: 20px;
              border-bottom: 1px solid #EBEEF5;
            }
            .header h1 {
              color: #409EFF;
              margin-bottom: 10px;
            }
            .header p {
              color: #606266;
              font-size: 14px;
            }
            .section { 
              margin-bottom: 30px; 
            }
            .section h2 {
              color: #303133;
              font-size: 18px;
              margin-bottom: 15px;
              padding-bottom: 10px;
              border-bottom: 1px solid #EBEEF5;
            }
            .meta-info {
              display: flex;
              flex-wrap: wrap;
              margin-bottom: 20px;
            }
            .meta-item {
              flex: 1;
              min-width: 200px;
              padding: 10px 15px;
              background: #f9f9f9;
              border-radius: 4px;
              margin: 5px;
            }
            .meta-label {
              font-weight: bold;
              margin-bottom: 5px;
              color: #606266;
            }
            .meta-value {
              color: #303133;
            }
            .steps { 
              display: flex; 
              justify-content: space-between; 
              margin: 20px 0;
              flex-wrap: wrap;
            }
            .step { 
              text-align: center; 
              padding: 15px; 
              border-top: 3px solid #67C23A; 
              flex: 1;
              min-width: 150px; 
              background: #f9f9f9;
              border-radius: 4px;
              margin: 5px;
            }
            .step-title {
              font-weight: bold;
              margin-bottom: 8px;
            }
            .step-duration {
              font-size: 12px;
              color: #909399;
            }
            .result { 
              margin: 20px 0; 
              padding: 20px; 
              border-radius: 8px;
              background: #f9f9f9;
              text-align: center;
            }
            .result-label {
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .result-value {
              display: inline-block;
              padding: 10px 20px;
              border-radius: 4px;
              font-size: 18px;
              font-weight: bold;
            }
            .normal { 
              color: white; 
              background-color: #67C23A;
            }
            .degradation { 
              color: white; 
              background-color: #E6A23C;
            }
            .fault { 
              color: white; 
              background-color: #F56C6C;
            }
            .confidence {
              margin-top: 10px;
              font-size: 14px;
              color: #606266;
            }
            table { 
              width: 100%; 
              border-collapse: collapse; 
              margin-top: 10px;
            }
            th, td { 
              padding: 12px; 
              text-align: left; 
              border-bottom: 1px solid #EBEEF5;
            }
            th {
              background-color: #f5f7fa;
            }
          </style>
        </head>
        <body>
          <div class="report-container">
            <div class="header">
              <h1>28SY型 FMEA 诊断报告</h1>
              <p>诊断时间: ${this.currentDiagnosisTime}</p>
            </div>
            
            <div class="section">
              <h2>诊断信息</h2>
              <div class="meta-info">
                <div class="meta-item">
                  <div class="meta-label">数据文件</div>
                  <div class="meta-value">${reportData.data_file}</div>
                </div>
                <div class="meta-item">
                  <div class="meta-label">使用模型</div>
                  <div class="meta-value">${reportData.model_used}</div>
                </div>
              </div>
            </div>
            
            <div class="section">
              <h2>诊断流程</h2>
              <div class="steps">
                ${reportData.steps.map((step, index) => `
                  <div class="step">
                    <div class="step-title">${index + 1}. ${step.description}</div>
                    <div class="step-duration">耗时: ${step.duration_ms} ms</div>
                  </div>
                `).join('')}
              </div>
            </div>
            
            <div class="section">
              <h2>诊断结论</h2>
              <div class="result">
                <div class="result-label">诊断结果</div>
                <div class="result-value ${conclusion.predicted_fault_mode.includes('fault') ? 'fault' : conclusion.predicted_fault_mode.includes('degradation') ? 'degradation' : 'normal'}">
                  ${this.getChineseFaultName(conclusion.predicted_fault_mode)}
                </div>
                ${conclusion.confidence_score ? `
                <div class="confidence">
                  置信度: ${(conclusion.confidence_score * 100).toFixed(1)}%
                </div>
                ` : ''}
              </div>
            </div>
          </div>
        </body>
        </html>
      `

      // 创建Blob对象
      const blob = new Blob([htmlContent], { type: 'text/html' })

      // 创建下载链接
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `28SY诊断报告_${this.currentDiagnosisTime.replace(/[: ]/g, '_')}.html`

      // 触发下载
      document.body.appendChild(link)
      link.click()

      // 清理
      document.body.removeChild(link)
    },

    // 添加跳转到主控台页面的方法
    viewInConsole() {
      // 确保诊断结果已保存到Vuex
      if (this.fmeaReport && this.fmeaReport.success) {
        console.log('保存诊断结果到Vuex:', this.fmeaReport)
        this.$store.dispatch('diagnosis/saveDiagnosisResult', this.fmeaReport)
      }

      // 关闭当前对话框
      this.fmeaDialogVisible = false

      // 跳转到主控台页面
      this.$router.push('/')
    },

    // 添加保存诊断结果到历史数据的方法
    saveDiagnosisToHistory(diagnosisResult) {
      if (!diagnosisResult || !diagnosisResult.success) {
        return
      }

      const conclusion = diagnosisResult.diagnosis_details.conclusion
      const faultMode = conclusion.predicted_fault_mode

      // 构造保存诊断数据的请求
      this.$axios.post('/phm/saveDiagnosisData/', {
        timestamp: new Date().toISOString(),
        fault_mode: faultMode,
        data_file: diagnosisResult.diagnosis_details.data_file,
        model_used: diagnosisResult.diagnosis_details.model_used,
        status: faultMode === '0_normal' ? '0' : faultMode.split('_')[0], // 使用故障模式的编号作为状态码
        health_status: faultMode === '0_normal' ? '1' : '0' // 正常状态为1，其他为0
      }).then(response => {
        if (response.data.code === 200) {
          console.log('已保存诊断结果到历史数据库')
        } else {
          console.error('保存诊断结果失败:', response.data.msg)
        }
      }).catch(error => {
        console.error('保存诊断结果出错:', error)
      })
    },

    viewHealthAssessment() {
      // 确保诊断结果已保存到Vuex
      if (this.fmeaReport && this.fmeaReport.success) {
        console.log('保存诊断结果到Vuex:', this.fmeaReport)
        this.$store.dispatch('diagnosis/saveDiagnosisResult', this.fmeaReport)
      }

      // 关闭当前对话框
      this.fmeaDialogVisible = false

      // 跳转到寿命预测与健康评估页面
      this.$router.push('/life/index')
    }
  }
}
</script>

<style lang="scss" >
.diagnosis-container{
  margin: 30px;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);
  padding: 24px;

  /* 页面标题样式 */
  .page-header {
    margin-bottom: 32px;
    text-align: center;

    .page-title {
      font-size: 2em;
      font-weight: 700;
      margin: 0;
      background: linear-gradient(135deg, #00d4ff, #33ddff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;

      i {
        font-size: 1.2em;
        color: #00d4ff;
      }

      .page-subtitle {
        font-size: 0.4em;
        color: #b8c5d1;
        font-weight: 400;
        margin-top: 8px;
        letter-spacing: 1px;
        display: block;
      }
    }
  }

  /* 模型管理标题样式 */
  .model-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .model-management-title {
      font-size: 18px;
      font-weight: 600;
      color: #00d4ff !important;
      text-align: center;
      flex: 1;
      background: linear-gradient(135deg, #00d4ff, #33ddff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .upload-model-btn {
      padding: 10px 20px !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      border-radius: 6px !important;
      box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;
      transition: all 0.3s ease !important;

      &:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;
      }

      i {
        margin-right: 6px !important;
      }
    }
  }
}

/* 模型管理表格样式 */
.model-management-table {
  background: rgba(47, 51, 73, 0.8) !important;

  .el-table__header-wrapper {
    .el-table__header {
      th {
        background: rgba(47, 51, 73, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
        font-weight: normal;
      }
    }
  }

  .el-table__body-wrapper {
    background: rgba(47, 51, 73, 0.8) !important;

    .el-table__body {
      background: rgba(47, 51, 73, 0.8) !important;

      tr {
        background: rgba(47, 51, 73, 0.8) !important;

        &:hover {
          background: rgba(0, 212, 255, 0.1) !important;
        }

        &.el-table__row--striped {
          background: rgba(47, 51, 73, 0.6) !important;

          &:hover {
            background: rgba(0, 212, 255, 0.1) !important;
          }
        }
      }

      td {
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: #b8c5d1 !important;
        background: transparent !important;
      }
    }
  }

  .el-table__border-left-patch,
  .el-table__border-right-patch {
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: rgba(47, 51, 73, 0.8) !important;
  }

  .el-table--border {
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: rgba(47, 51, 73, 0.8) !important;
  }

  .el-table--border::after {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .el-table--border td,
  .el-table--border th {
    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .el-table--border th.gutter:last-of-type {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: rgba(47, 51, 73, 0.8) !important;
  }

  .el-table__empty-block {
    background: rgba(47, 51, 73, 0.8) !important;
  }

  /* 禁用列宽调整功能 */
  .el-table th.is-leaf {
    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .el-table .el-table__header-wrapper .el-table__header th .cell {
    cursor: default !important;
  }

  .el-table .el-table__header-wrapper .el-table__header th:hover .cell {
    cursor: default !important;
  }

  .el-table th.gutter {
    display: none !important;
  }

  /* 四列均匀分布 */
  .model-management-table .el-table__header-wrapper .el-table__header colgroup col {
    width: 25% !important;
  }

  .model-management-table .el-table__body-wrapper .el-table__body colgroup col {
    width: 25% !important;
  }

  .model-management-table .el-table__header th,
  .model-management-table .el-table__body td {
    width: 25% !important;
    min-width: 25% !important;
    max-width: 25% !important;
  }
}

.box-card {
  background: rgba(47, 51, 73, 0.8) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  margin-bottom: 24px;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 212, 255, 0.4) !important;
    box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3) !important;
    transform: translateY(-2px);
  }
}
.selectCard .el-form-item__label {
  font-size: 17px;
  font-weight: 600;
  color: #ffffff !important;
}

.dignosisReport .el-dialog.diaReport{
  border-radius: 15px;
  width: 80%;
}

.report .el-form-item__label {
  font-size: 21px;
  font-family: KaiTi;
  color: #ffffff !important;
}
.report .el-form-item__content {
  width: 82%;
  color: #b8c5d1 !important;
}

.probability-distribution {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px 20px;
}
.prob-item {
  display: flex;
  align-items: center;
}
.prob-label {
  width: 300px; /* 根据最长标签调整 */
  margin-right: 10px;
  text-align: right;
  font-size: 14px;
  color: #b8c5d1 !important;
}

/* 诊断报告弹窗样式优化 */
.diagnosis-report-dialog {
  background: rgba(47, 51, 73, 0.8) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.2) !important;
  border-radius: 16px !important;

  .el-dialog__body {
    padding: 20px 30px;
    background: transparent !important;
    color: #b8c5d1 !important;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px 16px 0 0;

    .el-dialog__title {
      color: #ffffff !important;
      font-weight: 600;
    }
  }
}

.diagnosis-report-content {
  font-family: "Microsoft YaHei", Arial, sans-serif;
  color: #b8c5d1 !important;

    .report-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-wrap: wrap;

    @media (max-width: 968px) {
      flex-direction: column;

      .diagnosis-result {
        margin-left: 0;
        margin-top: 15px;
        width: 100%;
        max-width: none;
      }
    }
  }

    .report-meta {
    flex: 2;
    max-width: 65%;

    .meta-item {
      margin-bottom: 12px;
      font-size: 14px;
      line-height: 1.5;
      display: flex;
      align-items: baseline;

      &:last-child {
        margin-bottom: 0;
      }

      .meta-label {
        color: #b8c5d1 !important;
        margin-right: 12px;
        font-weight: 500;
        min-width: 90px;

        i {
          margin-right: 5px;
          color: #00d4ff;
        }
      }

      .meta-value {
        color: #ffffff !important;
        word-break: break-word;
        flex: 1;
        font-family: Consolas, Monaco, monospace;
      }
    }

    @media (max-width: 968px) {
      max-width: 100%;
    }
  }

  .diagnosis-result {
    flex: 1;
    background: #f9f9f9;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    min-width: 250px;
    max-width: 400px;
    margin-left: 20px;

    .result-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 15px;
      text-align: center;
    }

    .result-value {
      display: flex;
      flex-direction: column;
      align-items: center;

      .result-tag {
        font-size: 18px;
        padding: 10px 20px;
        font-weight: bold;
        margin-bottom: 10px;
        width: 100%;
        text-align: center;
        white-space: normal;
        height: auto;
        line-height: 1.5;
      }

      .confidence-score {
        font-size: 14px;
        color: #606266;
        margin-top: 5px;
      }
    }
  }

  .diagnosis-steps-section {
    margin-bottom: 25px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #303133;
    }

    .diagnostic-steps {
      padding: 0 10px;
    }
  }

  .report-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;

    .el-button {
      padding: 10px 20px;
    }
  }
}
</style>
