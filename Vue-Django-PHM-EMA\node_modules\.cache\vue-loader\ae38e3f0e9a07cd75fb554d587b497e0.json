{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\home\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\home\\index.vue", "mtime": 1754201109728}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/home", "sourcesContent": ["<template>\n  <div class=\"home-container modern-theme\">\n    <div class=\"headtxt\">\n      <h3 class=\"main-title\">\n        <span class=\"title-icon\">⚡</span>\n        伺服系统 PHM 软件平台\n        <div class=\"title-subtitle\">Prognostics and Health Management System</div>\n      </h3>\n    </div>\n    <div id=\"model-container\" class=\"modern-card\">\n      <div class=\"ctl\">\n        <el-button-group>\n          <el-button class=\"modern-button\" @click=\"onWindowResize\">\n            <i class=\"el-icon-refresh\"></i>\n            刷新3D视图\n          </el-button>\n        </el-button-group>\n      </div>\n      <div v-show=\"infoShow\" id=\"infoBox\">\n        <ul style=\"font-size:20px; line-height:31px\">\n          <li>组件名称: {{ info.name }}</li>\n        </ul>\n      </div>\n      <!-- 现代化伺服系统信息面板 -->\n      <div id=\"systemInfoBox\" class=\"modern-card\" :class=\"{ 'collapsed': systemInfoCollapsed }\">\n        <div class=\"card-header\" @click=\"toggleSystemInfo\">\n          <h4 class=\"card-title\">\n            <i class=\"el-icon-cpu\"></i>\n            伺服系统信息\n          </h4>\n          <div class=\"header-controls\">\n            <div class=\"status-indicator online\">在线</div>\n            <button class=\"collapse-btn\" :class=\"{ 'collapsed': systemInfoCollapsed }\">\n              <i :class=\"systemInfoCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n            </button>\n          </div>\n        </div>\n        <div class=\"card-content\" v-show=\"!systemInfoCollapsed\">\n          <div class=\"info-grid\">\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🔧</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">型号</span>\n                <span class=\"info-value\">XXXX-EMA</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">⚡</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">额定功率</span>\n                <span class=\"info-value\">200W</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🔋</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">额定电压</span>\n                <span class=\"info-value\">24V DC</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🔄</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">额定转速</span>\n                <span class=\"info-value\">3000RPM</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🎛️</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">控制方式</span>\n                <span class=\"info-value\">闭环位置控制</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">📅</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">生产日期</span>\n                <span class=\"info-value\">2023-05-15</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🏷️</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">序列号</span>\n                <span class=\"info-value\">SN20230515001</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 故障提示浮窗组件 -->\n    <fault-notification\n      :visible.sync=\"faultNotificationVisible\"\n      :type=\"faultNotificationType\"\n      :part-name=\"faultNotificationPartName\"\n      :status-name=\"faultNotificationStatusName\"\n      :diagnosis-time=\"faultNotificationTime\"\n      @close=\"handleFaultNotificationClose\"\n      @view-details=\"handleViewDetails\"\n    />\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'\nimport { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'\nimport dat from 'three/examples/js/libs/dat.gui.min.js'\nimport ModelInteractionUtil, {\n  DEFAULT_COLOR,\n  PART_COLOR,\n  GREEN_PARTS\n} from '@/utils/ModelInteractionUtil'\nimport RocketFlameEffect from '@/utils/RocketFlameEffect'\nimport FaultNotification from '@/components/FaultNotification.vue'\nimport {\n  getPartNameByFaultType,\n  getFaultTypeName,\n  getPartChineseName,\n  getStatusType\n} from '@/utils/faultToPartMapping'\n\nexport default {\n  name: '主控台', // eslint-disable-line vue/name-property-casing\n  components: {\n    FaultNotification\n  },\n  data() {\n    return {\n      dataShow: {\n        Xgive: '',\n        Xget: '',\n        Fget: '',\n        healthStatus: ''\n      },\n      dataPermit: false,\n      btntxt: '开始接收数据',\n      // mesh: null,\n      module: null,\n      moduleAll: null,\n      folderName: './3D/',\n      stlName: '',\n      materialCo: DEFAULT_COLOR,\n      camera: null,\n      scene: null,\n      renderer: null,\n      controls: null,\n      infoShow: true,\n      stateShow: true,\n      files: null,\n      info: {\n        name: ''\n      },\n      isFault: false,\n      state: {\n        all: 20,\n        normal: 20,\n        fault: 0\n      },\n      objectStatus: {\n        status: 0\n      },\n      drawFunc: '整体模型',\n      select: '',\n      clickCurrentColor: '',\n      faultCurrentColor: '',\n      p2hDict: null,\n      hanziList: [],\n      selectObject: null,\n      faultObject: null,\n      faultIndex: '',\n      faultObjectName: ['电机_R', '传感器', '控制器'],\n      errorNameDict: {},\n      statusName: '',\n      // 新增模型交互工具实例\n      modelInteraction: null,\n\n      // 火箭尾焰特效相关\n      rocketFlameEffect: null,\n\n      // 故障提示浮窗相关数据\n      faultNotificationVisible: false,\n      faultNotificationType: 'fault', // 'fault' 或 'degradation'\n      faultNotificationPartName: '',\n      faultNotificationStatusName: '',\n      faultNotificationTime: '',\n\n      // 当前故障/退化状态\n      currentFaultType: null,\n      currentFaultPart: null,\n\n      // 系统信息面板折叠状态\n      systemInfoCollapsed: false\n    }\n  },\n  mounted() {\n    this.init()\n    const element = document.getElementById('model-container')\n    this.files = require.context('../../../public/3D', false, /.STL$/).keys()\n    this.m = this.$notify.warning({\n      title: '提示',\n      message: '模型正在加载，请稍后进行操作...',\n      duration: 0,\n      showClose: false\n    })\n    // this.onWindowResize()\n    this.loadPre()\n    element.addEventListener('click', this.onMouseClick, false)\n    window.addEventListener('resize', this.onWindowResize, false)\n  },\n  activated() {\n    console.log('进入主控台页面了')\n    // 在页面激活时检查诊断结果\n    this.checkDiagnosisResult()\n  },\n  deactivated() {\n    console.log('离开主控台页面了')\n  },\n  beforeDestroy() {\n    clearInterval(this.timer)\n\n    // 清理火箭尾焰特效\n    if (this.rocketFlameEffect) {\n      this.rocketFlameEffect.destroy()\n      this.rocketFlameEffect = null\n    }\n\n    // 移除事件监听器\n    const element = document.getElementById('model-container')\n    if (element) {\n      element.removeEventListener('click', this.onMouseClick, false)\n    }\n    window.removeEventListener('resize', this.onWindowResize, false)\n  },\n  methods: {\n    // 切换系统信息面板折叠状态\n    toggleSystemInfo() {\n      this.systemInfoCollapsed = !this.systemInfoCollapsed\n    },\n\n    // 整体初始化\n    init() {\n      this.createScene()\n      this.helper()\n      // this.initGui()\n      this.createLight()\n      this.createCamera()\n      this.createRender()\n      this.createControls()\n      this.render()\n\n      // 创建一个组来包含所有模型，并设置整体位置\n      this.moduleAll = new THREE.Group()\n      // 将模型放置在网格中央\n      this.moduleAll.position.set(0, 0, 0)\n      this.scene.add(this.moduleAll)\n\n      // 初始化模型交互工具，添加状态变化回调\n      this.modelInteraction = new ModelInteractionUtil(\n        this.scene,\n        this.info,\n        this.handleModelStatusChange\n      )\n\n      // 应用新的颜色设置\n      this.modelInteraction.resetAllModelsColor()\n\n      // 检查Vuex中是否有未处理的诊断结果\n      this.checkDiagnosisResult()\n    },\n\n    // 初始化火箭尾焰特效\n    initRocketFlameEffect() {\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel) {\n        try {\n          this.rocketFlameEffect = new RocketFlameEffect(this.scene, daodanModel, {\n            intensity: 1, // 固定强度值\n            speed: 3, // 固定速度值\n            turbulence: 1 // 固定湍流值\n          })\n          console.log('火箭尾焰特效已初始化')\n        } catch (error) {\n          console.error('初始化火箭尾焰特效失败:', error)\n        }\n      }\n    },\n\n    // 检查Vuex中是否有未处理的诊断结果\n    checkDiagnosisResult() {\n      console.log('检查诊断结果', this.$store.getters['diagnosis/hasUnprocessedResult'])\n      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        const result = this.$store.getters['diagnosis/currentDiagnosisResult']\n        console.log('发现未处理的诊断结果:', result)\n        this.handleDiagnosisResult(result)\n        this.$store.dispatch('diagnosis/processDiagnosisResult')\n        console.log('诊断结果处理完成')\n      } else {\n        console.log('没有未处理的诊断结果')\n      }\n    },\n\n    // 处理模型状态变化\n    handleModelStatusChange(statusData) {\n      console.log('模型状态变化:', statusData)\n\n      if (statusData.type === 'fault' || statusData.type === 'degradation') {\n        // 更新状态计数\n        this.state.fault = 1\n        this.state.normal = this.state.all - this.state.fault\n        this.isFault = statusData.type === 'fault'\n\n        // 获取部件中文名称\n        const partChineseName = getPartChineseName(statusData.object.name)\n        console.log('部件中文名称:', partChineseName)\n\n        // 更新状态名称\n        this.statusName = statusData.info.statusName\n        console.log('状态名称:', this.statusName)\n\n        // 显示故障提示浮窗\n        this.showFaultNotification({\n          type: statusData.type,\n          partName: partChineseName,\n          statusName: statusData.info.statusName,\n          diagnosisTime: statusData.info.diagnosisTime\n        })\n\n        console.log('已显示故障提示浮窗')\n      } else {\n        // 重置状态\n        this.state.fault = 0\n        this.state.normal = this.state.all\n        this.isFault = false\n        this.statusName = ''\n        console.log('已重置状态')\n      }\n    },\n\n    // 显示故障提示浮窗\n    showFaultNotification(notificationData) {\n      console.log('显示故障提示浮窗:', notificationData)\n      this.faultNotificationType = notificationData.type\n      this.faultNotificationPartName = notificationData.partName\n      this.faultNotificationStatusName = notificationData.statusName\n      this.faultNotificationTime = notificationData.diagnosisTime\n      this.faultNotificationVisible = true\n\n      // 确保浮窗在DOM更新后显示\n      this.$nextTick(() => {\n        console.log('浮窗显示状态:', this.faultNotificationVisible)\n      })\n    },\n\n    // 处理故障提示浮窗关闭\n    handleFaultNotificationClose() {\n      this.faultNotificationVisible = false\n    },\n\n    // 处理故障诊断结果\n    handleDiagnosisResult(result) {\n      console.log('处理诊断结果:', result)\n\n      // 如果诊断成功\n      if (result && result.success) {\n        const diagnosisDetails = result.diagnosis_details\n        const conclusion = diagnosisDetails.conclusion\n        const faultType = conclusion.predicted_fault_mode\n\n        console.log('诊断结论:', faultType)\n\n        // 如果是正常状态，重置所有部件状态\n        if (faultType === '0_normal') {\n          console.log('诊断结果为正常状态，重置所有部件')\n          this.resetAllPartsStatus()\n          return\n        }\n\n        // 获取对应的部件名称\n        const partName = getPartNameByFaultType(faultType)\n        if (!partName) {\n          console.warn('未找到对应的部件:', faultType)\n          return\n        }\n\n        console.log('对应的部件名称:', partName)\n\n        // 获取状态类型和状态名称\n        const statusType = getStatusType(faultType)\n        const statusName = getFaultTypeName(faultType)\n\n        console.log('状态类型:', statusType, '状态名称:', statusName)\n\n        // 获取当前时间作为诊断时间\n        const diagnosisTime = new Date().toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit',\n          hour12: false\n        })\n\n        // 保存当前故障信息\n        this.currentFaultType = faultType\n        this.currentFaultPart = partName\n\n        // 根据状态类型标记部件\n        if (statusType === 'fault') {\n          // 故障状态，标记为红色\n          console.log('标记部件为故障状态:', partName)\n          this.modelInteraction.markAsFault(partName, {\n            statusName,\n            diagnosisTime\n          })\n        } else if (statusType === 'degradation') {\n          // 退化状态，标记为褐色\n          console.log('标记部件为退化状态:', partName)\n          this.modelInteraction.markAsDegradation(partName, {\n            statusName,\n            diagnosisTime\n          })\n        }\n      } else {\n        // 诊断失败\n        console.error('诊断失败:', result ? result.message : '未知错误')\n      }\n    },\n\n    // 重置所有部件状态\n    resetAllPartsStatus() {\n      if (this.currentFaultPart) {\n        this.modelInteraction.resetStatus(this.currentFaultPart)\n        this.currentFaultPart = null\n        this.currentFaultType = null\n\n        // 重置状态计数\n        this.state.fault = 0\n        this.state.normal = this.state.all\n        this.isFault = false\n        this.statusName = ''\n      }\n    },\n\n    // 测试故障诊断\n    testFaultDiagnosis() {\n      console.log('测试故障诊断')\n\n      // 创建一个模拟的诊断结果\n      const mockDiagnosisResult = {\n        success: true,\n        diagnosis_details: {\n          conclusion: {\n            predicted_fault_mode: '4_fault_stator_short'\n          }\n        }\n      }\n\n      // 处理诊断结果\n      this.handleDiagnosisResult(mockDiagnosisResult)\n    },\n\n    // 三大件 创建场景\n    createScene() {\n      this.scene = new THREE.Scene()\n    },\n\n    // 创建相机\n    createCamera() {\n      const element = document.getElementById('model-container')\n      const k = element.clientWidth / element.clientHeight // 实际3d显示窗口宽高比\n      this.camera = new THREE.PerspectiveCamera(45, k, 0.1, 10000) // 透视相机 (for,aspect,near,far) 视场 长宽比 多近开始渲染 最远看到的距离\n      // 调整相机位置，以便更好地观察模型\n      this.camera.position.set(0, 400, 600)\n      this.camera.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(this.camera)\n    },\n\n    // 创建渲染器\n    createRender() {\n      const element = document.getElementById('model-container')\n      this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })\n      this.renderer.setSize(element.clientWidth, element.clientHeight) // 设置渲染区域尺寸\n      this.renderer.shadowMap.enabled = true\n      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap\n      this.renderer.setClearColor(0x050505, 0.6)\n      element.appendChild(this.renderer.domElement) // 渲染div到canvas\n    },\n\n    // 渲染\n    render() {\n      // 确保导弹壳体始终保持透明\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel && daodanModel.material && this.modelInteraction) {\n        this.modelInteraction.updateDaodanMaterial(daodanModel)\n      }\n\n      // 更新火箭尾焰特效\n      if (this.rocketFlameEffect) {\n        this.rocketFlameEffect.update()\n      }\n\n      this.renderer.render(this.scene, this.camera)\n      requestAnimationFrame(this.render)\n    },\n\n    // 底部网格和三轴指示器\n    helper() {\n      var gridplane = new THREE.GridHelper(1200, 60, 0xFF7F50, 0x4A4A4A) // 一格20\n      this.scene.add(gridplane)\n      const axes = new THREE.AxesHelper(200)\n      this.scene.add(axes)\n    },\n\n    // 控制模型组件\n    initGui() {\n      this.paras = {\n        // rotationSpeed: 0.005 // 中文也行\n        rotationSpeed: 0 // 中文也行\n      }\n      var gui = new dat.GUI({ autoPlace: false })\n      gui.domElement.id = 'gui'\n      document.getElementById('gui_container').appendChild(gui.domElement)\n      gui.add(this.paras, 'rotationSpeed', 0, 0.05)\n    },\n\n    // 创建光源\n    createLight() {\n      // 环境光 没有特定的光源，该光源不会影响阴影的产生，被应用到全局范围内的所有对象,使用该光源是为了弱化阴影或者添加一些颜色\n      const ambientLight = new THREE.AmbientLight(0x999999)\n      this.scene.add(ambientLight)\n      const point = new THREE.PointLight(0xffffff)\n      // const point = new THREE.SpotLight(0xffffff)\n      point.position.set(-300, 600, -400)\n      this.scene.add(point)\n    },\n\n    // 轨道控制、旋转缩放\n    createControls() {\n      this.controls = new OrbitControls(this.camera, this.renderer.domElement)\n    },\n\n    // 接收数据控制\n    dataGet() {\n      if (this.btntxt === '停止接收数据') {\n        this.btntxt = '开始接收数据'\n        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        if (this.faultCurrentColor === 14423100) {\n          this.faultObject.material.color.set(0x7CFC00)\n        } else {\n          this.faultObject.material.color.set(this.faultCurrentColor)\n        }\n      } else {\n        this.btntxt = '停止接收数据'\n        // 目前认为故障组件只有EMA总装一种，提前指定故障组件 应该在getData()中按照faultIndex再确定\n        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        this.faultCurrentColor = this.faultObject.material.color.getHex()\n        console.log('inital faultObjectColor', this.faultCurrentColor)\n        this.$axios.get('./errorDict.json').then(res => {\n          this.errorNameDict = res.data.errorNameDict\n          // console.log('字典', res.data.errorNameDict)\n        })\n      }\n      this.dataPermit = !this.dataPermit\n      console.log(this.dataPermit)\n      this.getData()\n    },\n\n    // 接收数据\n    getData() {\n      if (this.dataPermit) {\n        /* this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        this.faultCurrentColor = this.faultObject.material.color.getHex() */\n        this.timer = setInterval(() => {\n          this.$axios.get('/phm/getData/').then((res) => {\n            // console.log('111222', res.data.data, typeof (res.data.data)) 类型是object\n            this.dataShow.Xget = res.data.data.x_get\n            this.dataShow.Xgive = res.data.data.x_give\n            this.dataShow.Fget = res.data.data.f_get\n            // cw, ch, r, k, x, y, z\n            this.dataPanel(1600, 840, 50, 6, -380, 0, 210)\n            this.faultIndex = res.data.data.faultStatus\n            this.dataShow.healthStatus = res.data.data.healthStatus\n            this.statusName = this.errorNameDict[this.faultIndex]\n            console.log('faultIndex', this.faultIndex)\n            if (this.faultIndex !== '0') {\n              this.isFault = true\n              this.objectStatus.status = 1\n              this.state.fault = 1\n              this.state.normal = 19\n              // if (this.faultIndex !== '10') {\n              //   this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n              // }\n              this.faultObject.material.color.set(0xDC143C)\n            } else {\n              this.isFault = false\n              this.objectStatus.status = 0\n              this.faultObject.material.color.set(this.faultCurrentColor)\n              this.state.fault = 0\n              this.state.normal = 20\n            }\n          })\n        }, this.$Common.requestInterval)\n      } else {\n        clearInterval(this.timer)\n      }\n    },\n\n    // 加载STL模型\n    loadSTL(stlName) {\n      console.log('加载STL模型:', stlName)\n      // const THIS = this // eslint-disable-line no-unused-vars\n      const loader = new STLLoader()\n      loader.load(\n        this.folderName + stlName,\n        geometry => {\n          // 确保几何体居中\n          geometry.computeBoundingBox()\n\n          // 为导弹壳体模型设置特殊材质\n          let material\n          if (stlName === 'daodan.STL') {\n            material = new THREE.MeshPhongMaterial({\n              color: 0xffffff, // 白色\n              transparent: true,\n              opacity: 0.10, // 更高的透明度\n              side: THREE.DoubleSide, // 双面渲染\n              depthWrite: false, // 禁用深度写入以避免渲染问题\n              depthTest: false, // 禁用深度测试，使射线能够穿透\n              shininess: 150 // 增加光泽度\n            })\n          } else {\n            // 检查是否为需要设置为绿色的部件\n            const partName = stlName.split('.')[0] // 去掉.STL后缀\n            let color = DEFAULT_COLOR // 默认蓝色\n\n            // 检查是否为需要设置为绿色的部件\n            for (const greenPart of GREEN_PARTS) {\n              if (partName.includes(greenPart)) {\n                color = PART_COLOR // 设置为绿色\n                break\n              }\n            }\n\n            material = new THREE.MeshStandardMaterial({\n              color: color, // 使用确定的颜色\n              transparent: true,\n              opacity: 0.7\n            })\n          }\n\n          this.module = new THREE.Mesh(geometry, material) // 创建网格对象\n          this.module.name = stlName.split('.')[0] // 去掉.STL后缀\n          console.log('创建模型部件:', this.module.name)\n          this.info.name = '模型正在加载...'\n\n          // 旋转模型使底座底面与网格平行\n          this.module.rotateX(-Math.PI / 2) // 绕X轴旋转，使模型水平放置\n          this.module.rotateX(Math.PI / 2) // 绕X轴顺时针旋转90度\n\n          // 将所有模型添加到模型组\n          this.moduleAll.add(this.module)\n\n          // 如果是导弹模型，需要特殊处理\n          if (stlName === 'daodan.STL') {\n            console.log('处理导弹壳体模型位置')\n\n            // 设置导弹壳体的特殊属性，使其不阻挡交互\n            this.module.renderOrder = -1 // 负数renderOrder确保它在其他对象之前渲染\n            this.module.userData.isBackground = true // 标记为背景对象\n\n            // 计算所有模型的包围盒\n            const box = new THREE.Box3().setFromObject(this.moduleAll)\n            const center = box.getCenter(new THREE.Vector3())\n            const size = box.getSize(new THREE.Vector3())\n\n            // 重置导弹模型的位置和旋转\n            this.module.position.set(0, 0, 0)\n            this.module.rotation.set(0, 0, 0)\n\n            // 首先水平放置导弹模型\n            this.module.rotateX(-Math.PI / 2)\n\n            // 然后逆时针旋转90度，使导弹壳体与其他零件平行\n            this.module.rotateZ(Math.PI / 2)\n\n            // 计算导弹模型的包围盒\n            const daodanBox = new THREE.Box3().setFromObject(this.module)\n            const daodanSize = daodanBox.getSize(new THREE.Vector3())\n\n            // 调整导弹模型的缩放，确保能包含所有模型\n            // 增加缩放系数，使导弹壳体更大，伺服系统可以完全放在内部\n            const scaleFactor = Math.max(\n              size.x / daodanSize.x,\n              size.y / daodanSize.y,\n              size.z / daodanSize.z\n            ) * 2.5 // 放大3.5倍，确保伺服系统完全位于内部\n\n            this.module.scale.set(scaleFactor, scaleFactor, scaleFactor)\n\n            // 将导弹模型放置在所有模型的中心\n            this.module.position.copy(center)\n\n            // 计算导弹壳体的尺寸\n            const scaledDaodanLength = daodanSize.z * scaleFactor\n            const scaledDaodanWidth = daodanSize.x * scaleFactor\n            const scaledDaodanHeight = daodanSize.y * scaleFactor\n\n            // 根据图片反馈调整位置\n            // 向下移动导弹壳体（Y轴负方向）\n            this.module.position.y -= size.y * 1.3 // 向下移动\n\n            // 向右移动导弹壳体（Z轴正方向，由于旋转了90度）\n            this.module.position.z += size.z * 3.7 // 向右移动\n\n            // 向后移动导弹壳体（X轴正方向，由于旋转了90度）\n            this.module.position.x += scaledDaodanLength * 0.6// 向后移动\n\n            console.log('导弹壳体模型已调整位置和大小', {\n              center,\n              size,\n              scaleFactor,\n              daodanSize,\n              scaledDaodanLength,\n              scaledDaodanWidth,\n              scaledDaodanHeight,\n              adjustedPosition: this.module.position\n            })\n\n            // 延迟初始化火箭尾焰特效，确保导弹模型完全加载和定位\n            setTimeout(() => {\n              this.initRocketFlameEffect()\n            }, 800)\n          }\n\n          // 如果加载完成底座，说明主要模型都已加载完成\n          if (stlName === '底座.STL') {\n            // 调整整个模型组的位置，使其居中\n            this.moduleAll.position.set(0, 0, 0)\n\n            // 计算整个模型组的包围盒，用于居中\n            const box = new THREE.Box3().setFromObject(this.moduleAll)\n            const center = box.getCenter(new THREE.Vector3())\n\n            // 将模型移动到网格中央\n            this.moduleAll.position.x = -center.x\n            this.moduleAll.position.z = -center.z\n            // 保持y轴位置，使底座底面与网格平行\n            this.moduleAll.position.y = -box.min.y + 10 // 稍微抬高一点，避免穿过网格\n\n            this.m.close()\n            this.$notify.success({\n              title: '提示',\n              message: '模型加载完毕'\n            })\n            this.info.name = '待单击模型...'\n            console.log('模型已加载并居中放置')\n\n            // 打印所有模型部件名称，用于调试\n            console.log('所有模型部件:')\n            this.scene.traverse((object) => {\n              if (object.isMesh) {\n                console.log(' - ' + object.name)\n              }\n            })\n\n            // 确保所有部件都应用了正确的颜色\n            if (this.modelInteraction) {\n              console.log('应用颜色设置...')\n              this.modelInteraction.resetAllModelsColor()\n            }\n          }\n        }\n      )\n    },\n\n    // 改变显示组件重新绘制\n    loadPre() { // 加载哪个零件\n      // 先加载除导弹外的所有主要部件\n      let daodanModel = null\n      const otherModels = []\n\n      // 遍历所有模型文件\n      for (var i = 0; i < this.files.length; i++) {\n        var stlName = this.files[i].split('/')[1]\n        // 分开处理导弹模型和其他模型\n        if (stlName === 'daodan.STL') {\n          // 先记录导弹模型，稍后加载\n          daodanModel = stlName\n        } else if (!stlName.includes('GB╱T') && !stlName.includes('螺钉')) {\n          // 只加载主要零件，减少加载时间\n          this.loadSTL(stlName)\n          otherModels.push(stlName)\n        }\n      }\n\n      // 在所有其他模型加载完成后，最后加载导弹壳体模型\n      if (daodanModel) {\n        setTimeout(() => {\n          console.log('加载导弹壳体模型，包含所有其他模型')\n          this.loadSTL(daodanModel)\n        }, 1000) // 延迟加载，确保其他模型已经加载完成\n      }\n    },\n\n    // 调整导弹模型位置\n    adjustDaodanPosition() {\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (!daodanModel) return\n\n      // 计算所有其他模型的包围盒\n      const otherModelsBox = new THREE.Box3()\n      this.scene.traverse((object) => {\n        if (object.isMesh && object.name !== 'daodan') {\n          otherModelsBox.expandByObject(object)\n        }\n      })\n\n      // 计算导弹模型的包围盒\n      const daodanBox = new THREE.Box3().setFromObject(daodanModel)\n\n      // 调整导弹模型的位置，使其包含所有其他模型\n      // 并将其他模型放在导弹模型的尾部\n      const otherModelsCenter = otherModelsBox.getCenter(new THREE.Vector3())\n      const daodanCenter = daodanBox.getCenter(new THREE.Vector3())\n\n      // 计算需要移动的距离\n      const offsetX = otherModelsCenter.x - daodanCenter.x\n      const offsetY = otherModelsCenter.y - daodanCenter.y\n      const offsetZ = otherModelsCenter.z - daodanCenter.z\n\n      // 移动导弹模型\n      daodanModel.position.set(\n        offsetX,\n        offsetY,\n        offsetZ\n      )\n\n      console.log('调整了导弹模型位置')\n    },\n\n    // 数据面板\n    dataPanel(cw, ch, r, k, x, y, z) {\n      // 用canvas生成图片\n      var color = ['#008000', '#FF0000']\n      var canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      canvas.width = cw\n      canvas.height = ch\n      ctx.lineWidth = 10\n      ctx.fillStyle = 'rgba(255,255,255,1)'\n      this.roundRect(ctx, 0, 0, cw, ch, r)\n      var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0)\n      gradient.addColorStop('0', 'blue')\n      gradient.addColorStop('1.0', 'red')\n      ctx.font = 'normal 80pt \"楷体\"'\n      ctx.fillStyle = color[this.objectStatus.status]\n      ctx.fillText(this.statusName, 700, 150)\n      ctx.fillStyle = color[0]\n      // ctx.fillText('0.83', 700, 300)\n      ctx.fillText(this.dataShow.healthStatus, 700, 300)\n      ctx.fillStyle = gradient\n      ctx.fillText('当前状态：', 60, 150)\n      ctx.fillText('健康值：', 60, 300)\n      ctx.fillText('位移指令：', 60, 450)\n      ctx.fillText('实际位移：', 60, 600)\n      ctx.fillText('负载力：', 60, 750)\n      ctx.fillText(this.dataShow.Xgive, 700, 450)\n      ctx.fillText(this.dataShow.Xget, 700, 600)\n      ctx.fillText(this.dataShow.Fget, 700, 750)\n      // canvas.height = 500\n      const url = canvas.toDataURL('./img/png')\n      var geometry = new THREE.PlaneGeometry(cw / k, ch / k)\n      var texture = new THREE.TextureLoader().load(url)\n      // 将图像加载为纹理，然后将纹理赋给材质的map属性\n      var material = new THREE.MeshBasicMaterial({\n        map: texture,\n        side: THREE.DoubleSide,\n        opacity: 1,\n        transparent: true\n      })\n      const rect = new THREE.Mesh(geometry, material)\n      rect.position.set(x, z, y)\n      this.scene.add(rect)\n    },\n\n    // 画圆角矩形\n    roundRect(ctx, x, y, w, h, r) {\n      ctx.beginPath()\n      ctx.moveTo(x + r, y)\n      ctx.arcTo(x + w, y, x + w, y + h, r)\n      ctx.arcTo(x + w, y + h, x, y + h, r)\n      ctx.arcTo(x, y + h, x, y, r)\n      ctx.arcTo(x, y, x + w, y, r)\n      ctx.fill()\n      ctx.closePath()\n    },\n\n    // 监听函数部分 - 使用新的模型交互工具\n    onMouseClick(event) {\n      // 加载字典数据\n      this.$axios.get('./errorDict.json').then(res => {\n        this.p2hDict = res.data.pinyin2hanzi\n        const obj = this.p2hDict\n        this.hanziList = Object.keys(obj)\n      })\n\n      // 确保导弹壳体始终保持透明\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel && daodanModel.material) {\n        this.modelInteraction.updateDaodanMaterial(daodanModel)\n      }\n\n      const element = document.getElementById('model-container')\n      var raycaster = new THREE.Raycaster()\n      var mouse = new THREE.Vector2()\n\n      // 将鼠标点击位置的屏幕坐标转成threejs中的标准坐标\n      mouse.x = (event.offsetX / element.clientWidth) * 2 - 1\n      mouse.y = -(event.offsetY / element.clientHeight) * 2 + 1\n\n      // 通过摄像机和鼠标位置更新射线\n      raycaster.setFromCamera(mouse, this.camera)\n\n      // 计算物体和射线的焦点\n      var intersects = raycaster.intersectObjects(this.scene.children, true)\n        .filter(intersect => {\n          // 过滤掉导弹壳体，使其不响应点击\n          return intersect.object.name !== 'daodan' &&\n                 !intersect.object.userData.isBackground\n        })\n\n      if (intersects.length > 0) {\n        // 处理点击到的对象\n        this.modelInteraction.handleModelClick(intersects[0].object)\n      } else {\n        // 点击到空白区域\n        this.modelInteraction.handleBackgroundClick()\n      }\n    },\n\n    // 检测区域大小变化\n    onWindowResize() {\n      const element = document.getElementById('model-container')\n      this.camera.aspect = element.clientWidth / element.clientHeight\n      this.camera.updateProjectionMatrix()\n      this.render()\n      // 设置渲染区域尺寸\n      this.renderer.setSize(element.clientWidth, element.clientHeight)\n      console.log('3d area changes')\n\n      // 恢复到初始正常状态\n      this.resetAllPartsStatus()\n\n      // 清除Vuex中的诊断结果\n      this.$store.dispatch('diagnosis/clearDiagnosisResult')\n\n      // 重置信息显示\n      this.info.name = '待单击模型...'\n\n      console.log('已恢复到初始正常状态')\n    },\n\n    // 处理view-details事件\n    handleViewDetails() {\n      console.log('查看详情:', this.currentFaultType, this.currentFaultPart)\n\n      // 如果当前有故障或退化状态\n      if (this.currentFaultType && this.currentFaultPart) {\n        // 显示详细信息对话框\n        this.$alert(`\n          <div class=\"fault-details\">\n            <h3>${this.faultNotificationStatusName}</h3>\n            <p><strong>部件名称:</strong> ${this.faultNotificationPartName}</p>\n            <p><strong>故障类型:</strong> ${this.currentFaultType}</p>\n            <p><strong>诊断时间:</strong> ${this.faultNotificationTime}</p>\n            <p><strong>可能原因:</strong> ${this.getPossibleCauses(this.currentFaultType)}</p>\n            <p><strong>建议解决方案:</strong> ${this.getSuggestedSolutions(this.currentFaultType)}</p>\n          </div>\n        `, '故障详情', {\n          dangerouslyUseHTMLString: true,\n          confirmButtonText: '确定',\n          callback: action => {\n            console.log(action)\n          }\n        })\n      }\n    },\n\n    // 获取可能原因\n    getPossibleCauses(faultType) {\n      const causes = {\n        '1_degradation_magnet': '长时间使用导致永磁体性能下降；环境温度过高；机械冲击或振动。',\n        '2_degradation_brush_wear': '正常磨损；过载运行；环境中存在过多粉尘。',\n        '3_degradation_commutator_oxidation': '环境湿度过高；长期不使用；电刷与换向器接触不良。',\n        '4_fault_stator_short': '绝缘材料老化；过载运行；绕组温度过高；制造缺陷。',\n        '5_fault_rotor_open': '机械损伤；过载运行；焊接点断裂；制造缺陷。',\n        '6_degradation_bearing_wear': '正常磨损；润滑不足；轴承负载过大；轴承安装不当。',\n        '7_fault_bearing_stuck': '润滑失效；异物进入；轴承严重磨损；轴承锈蚀。',\n        '8_degradation_gear_wear': '正常磨损；润滑不足；齿轮负载过大；齿轮材料缺陷。',\n        '9_degradation_sensor_drift': '长期使用导致性能下降；环境温度变化；电源电压波动。',\n        '10_fault_sensor_loss': '传感器连接松动；传感器损坏；信号线断路；电源故障。',\n        '11_fault_mosfet_breakdown': '过电压；过电流；温度过高；静电放电损伤。',\n        '12_degradation_drive_distortion': '电路元件老化；电源电压不稳；信号干扰；温度变化。',\n        '13_fault_mcu_crash': '软件错误；电源问题；硬件故障；外部干扰。'\n      }\n      return causes[faultType] || '未知原因'\n    },\n\n    // 获取建议解决方案\n    getSuggestedSolutions(faultType) {\n      const solutions = {\n        '1_degradation_magnet': '更换永磁体；降低工作环境温度；减少机械冲击。',\n        '2_degradation_brush_wear': '更换电刷；检查负载是否过大；清洁工作环境。',\n        '3_degradation_commutator_oxidation': '清洁换向器表面；降低环境湿度；定期维护。',\n        '4_fault_stator_short': '更换定子绕组；检查负载情况；改善散热条件。',\n        '5_fault_rotor_open': '更换转子绕组；检查焊接点；降低负载。',\n        '6_degradation_bearing_wear': '更换轴承；增加润滑；检查轴承安装情况。',\n        '7_fault_bearing_stuck': '更换轴承；清洁轴承；检查润滑情况。',\n        '8_degradation_gear_wear': '更换齿轮；增加润滑；检查负载情况。',\n        '9_degradation_sensor_drift': '校准传感器；更换传感器；稳定工作环境。',\n        '10_fault_sensor_loss': '检查连接；更换传感器；检查信号线路。',\n        '11_fault_mosfet_breakdown': '更换MOSFET；检查电路保护措施；改善散热条件。',\n        '12_degradation_drive_distortion': '更换老化元件；稳定电源电压；增加信号滤波。',\n        '13_fault_mcu_crash': '更新软件；检查电源；更换MCU；增加抗干扰措施。'\n      }\n      return solutions[faultType] || '请联系专业维修人员'\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n/* 主容器现代化样式 */\n.home-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);\n\n  // 科技感粒子背景增强\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-image:\n      radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, rgba(255, 107, 53, 0.08) 0%, transparent 50%),\n      radial-gradient(circle at 50% 50%, rgba(0, 200, 150, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n    animation: particleFloat 20s ease-in-out infinite;\n  }\n}\n\n/* 标题样式现代化 */\n.headtxt {\n  text-align: center;\n  padding: 20px 0;\n  position: relative;\n  z-index: 10;\n\n  .main-title {\n    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;\n    font-size: 2.2em;\n    font-weight: 700;\n    margin: 0;\n    background: linear-gradient(135deg, $techBlue, $techBlueLight);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12px;\n\n    .title-icon {\n      font-size: 1.2em;\n      animation: pulse 2s infinite;\n    }\n\n    .title-subtitle {\n      font-size: 0.4em;\n      color: $textSecondary;\n      font-weight: 400;\n      margin-top: 8px;\n      letter-spacing: 2px;\n    }\n  }\n}\n\n/* 3D模型容器现代化 */\n#model-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 1px solid $borderPrimary;\n  border-radius: 16px;\n  background: rgba(26, 29, 41, 0.3);\n  backdrop-filter: blur(10px);\n  box-shadow: $shadowPrimary;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg,\n      rgba(0, 212, 255, 0.02) 0%,\n      transparent 50%,\n      rgba(255, 107, 53, 0.02) 100%);\n    pointer-events: none;\n  }\n}\n\n.ctl {\n  position: absolute;\n  left: 50%;\n  top: 20px;\n  transform: translateX(-50%);\n  z-index: 100;\n\n  .el-button-group {\n    .modern-button {\n      background: linear-gradient(135deg, $techBlue, $techBlueDark);\n      border: none;\n      color: white;\n      padding: 12px 24px;\n      border-radius: 8px;\n      font-weight: 500;\n      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n      box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);\n\n      &:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);\n        background: linear-gradient(135deg, $techBlueLight, $techBlue);\n      }\n\n      i {\n        margin-right: 8px;\n      }\n    }\n  }\n}\n\n.label-col {\n  padding: 8px 5px;\n}\n\n#gui_container {\n  position: absolute;\n  top: 84%;\n  left: 81%;\n}\n\n#gui {\n  transform: translate(-50%, -75px);\n}\n\n#infoBox {\n  position: absolute;\n  padding: 20px;\n  background: $bgCard;\n  backdrop-filter: blur(10px);\n  border: 1px solid $borderPrimary;\n  border-radius: 12px;\n  color: $textPrimary;\n  font-size: 16px;\n  min-width: 200px;\n  width: 400px;\n  box-shadow: $shadowPrimary;\n  transition: all 0.3s ease;\n  z-index: 100;\n\n  &:hover {\n    border-color: $borderHover;\n    box-shadow: $shadowHover;\n  }\n\n  ul {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n    color: $textPrimary;\n\n    li {\n      margin: 8px 0;\n      line-height: 1.6;\n      color: $textPrimary;\n    }\n  }\n}\n\n/* 现代化系统信息面板样式 */\n#systemInfoBox {\n  position: absolute;\n  right: 20px;\n  top: 20px;\n  min-width: 320px;\n  width: 380px;\n  background: $bgCard;\n  backdrop-filter: blur(15px);\n  border: 1px solid $borderPrimary;\n  border-radius: 16px;\n  box-shadow: $shadowPrimary;\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  z-index: 100;\n\n  &:hover {\n    border-color: $borderHover;\n    box-shadow: $shadowHover;\n    transform: translateY(-2px);\n  }\n\n  .card-header {\n    padding: 20px 24px 16px;\n    border-bottom: 1px solid $borderSecondary;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      background: rgba(0, 212, 255, 0.05);\n    }\n\n    .card-title {\n      color: $textPrimary;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 0;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      i {\n        color: $techBlue;\n        font-size: 20px;\n      }\n    }\n\n    .header-controls {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .status-indicator {\n      padding: 4px 12px;\n      border-radius: 12px;\n      font-size: 12px;\n      font-weight: 600;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n\n      &.online {\n        background: rgba(0, 200, 150, 0.2);\n        color: #00c896;\n        border: 1px solid rgba(0, 200, 150, 0.3);\n      }\n\n      &.offline {\n        background: rgba(255, 71, 87, 0.2);\n        color: #ff4757;\n        border: 1px solid rgba(255, 71, 87, 0.3);\n      }\n    }\n\n    .collapse-btn {\n      background: rgba(0, 212, 255, 0.1);\n      border: 1px solid rgba(0, 212, 255, 0.3);\n      border-radius: 6px;\n      width: 32px;\n      height: 32px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      color: $techBlue;\n\n      &:hover {\n        background: rgba(0, 212, 255, 0.2);\n        border-color: rgba(0, 212, 255, 0.5);\n        transform: scale(1.1);\n      }\n\n      i {\n        font-size: 14px;\n        transition: transform 0.3s ease;\n      }\n\n      &.collapsed i {\n        transform: rotate(180deg);\n      }\n    }\n  }\n\n  .card-content {\n    padding: 20px 24px;\n    transition: all 0.3s ease;\n    overflow: hidden;\n  }\n\n  // 折叠状态样式\n  &.collapsed {\n    .card-header {\n      border-bottom: none;\n    }\n\n    .card-content {\n      max-height: 0;\n      padding: 0 24px;\n      opacity: 0;\n    }\n  }\n\n  .info-grid {\n    display: grid;\n    gap: 16px;\n  }\n\n  .info-item {\n    display: flex;\n    align-items: center;\n    padding: 12px 16px;\n    background: rgba(255, 255, 255, 0.03);\n    border-radius: 8px;\n    border: 1px solid rgba(255, 255, 255, 0.05);\n    transition: all 0.3s ease;\n\n    &:hover {\n      background: rgba(0, 212, 255, 0.05);\n      border-color: rgba(0, 212, 255, 0.2);\n      transform: translateX(4px);\n    }\n\n    .info-icon {\n      font-size: 20px;\n      margin-right: 12px;\n      width: 24px;\n      text-align: center;\n    }\n\n    .info-details {\n      flex: 1;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .info-label {\n        color: $textSecondary;\n        font-size: 14px;\n        font-weight: 500;\n      }\n\n      .info-value {\n        color: $textPrimary;\n        font-size: 14px;\n        font-weight: 600;\n        background: linear-gradient(135deg, $techBlue, $techBlueLight);\n        -webkit-background-clip: text;\n        -webkit-text-fill-color: transparent;\n        background-clip: text;\n      }\n    }\n  }\n}\n\n/* 故障详情对话框样式 */\n.fault-details {\n  padding: 10px;\n}\n\n.fault-details h3 {\n  color: #F56C6C;\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n  text-align: center;\n  border-bottom: 1px solid #EBEEF5;\n  padding-bottom: 10px;\n}\n\n.fault-details p {\n  margin: 10px 0;\n  line-height: 1.6;\n}\n\n.fault-details strong {\n  color: #303133;\n  display: inline-block;\n  width: 100px;\n  vertical-align: top;\n}\n\n/* 自定义El-Alert样式 */\n.el-message-box {\n  width: 500px !important;\n  max-width: 90%;\n}\n\n.el-message-box__content {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n/* 动画效果 */\n@keyframes particleFloat {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n    opacity: 1;\n  }\n  33% {\n    transform: translateY(-20px) rotate(120deg);\n    opacity: 0.8;\n  }\n  66% {\n    transform: translateY(10px) rotate(240deg);\n    opacity: 0.9;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.05);\n  }\n}\n\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  #systemInfoBox {\n    width: 300px;\n    min-width: 280px;\n  }\n\n  .headtxt .main-title {\n    font-size: 1.8em;\n  }\n}\n\n@media (max-width: 768px) {\n  #systemInfoBox {\n    position: relative;\n    right: auto;\n    top: auto;\n    width: 100%;\n    margin: 20px;\n\n    .info-grid {\n      grid-template-columns: 1fr;\n    }\n  }\n\n  .ctl {\n    left: 50%;\n    top: 10px;\n  }\n\n  .headtxt .main-title {\n    font-size: 1.5em;\n    flex-direction: column;\n    gap: 8px;\n  }\n}\n\n/* Element UI 组件样式覆盖 */\n:deep(.el-button-group) {\n  .el-button {\n    border: none;\n\n    &:first-child {\n      border-radius: 8px 0 0 8px;\n    }\n\n    &:last-child {\n      border-radius: 0 8px 8px 0;\n    }\n\n    &:only-child {\n      border-radius: 8px;\n    }\n  }\n}\n\n:deep(.el-dialog) {\n  background: $bgCard;\n  backdrop-filter: blur(10px);\n  border: 1px solid $borderPrimary;\n  border-radius: 16px;\n\n  .el-dialog__header {\n    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));\n    border-bottom: 1px solid $borderSecondary;\n    border-radius: 16px 16px 0 0;\n\n    .el-dialog__title {\n      color: $textPrimary;\n      font-weight: 600;\n    }\n  }\n\n  .el-dialog__body {\n    color: $textSecondary;\n  }\n}\n</style>\n\n"]}]}