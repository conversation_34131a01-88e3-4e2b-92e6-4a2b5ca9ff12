
89c884d92d9b9b4e7c57045c9cc94f158fd9d8c8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"2ed9c527c50dba056a7ea5d7d9d607db\"}","integrity":"sha512-rf7cPrcxm9Jmxzec+eDV0XXoQHPpfaiPrxfYVQW84pqpUGc1gNid4zq/q/hGEi7NxUeEQoH2MwmyQhyBRyH46A==","time":1754202866852,"size":26776}