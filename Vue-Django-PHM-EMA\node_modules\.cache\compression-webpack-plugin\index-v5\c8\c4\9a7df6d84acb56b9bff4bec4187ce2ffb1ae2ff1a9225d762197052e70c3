
5f16d4fdf207f83044ac7e5fcbfad6421b7e070a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F5.js\",\"contentHash\":\"219ae20ff1ff3ce503c1b588abca04f2\"}","integrity":"sha512-o2PjbI/Y+B/0aldxnGOC1Sm5hrul1WvwH8sX9jLioN6foDTuzyGRI+XB0aIHb1MnQDzsZwPn/x8LGDumObpBqQ==","time":1754206056527,"size":107504}