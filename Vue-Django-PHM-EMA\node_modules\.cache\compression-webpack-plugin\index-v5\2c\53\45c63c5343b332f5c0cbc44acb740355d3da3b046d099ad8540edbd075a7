
dc14367f7831fe5c1b2ea647dea351ea2bd8dc8b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"1c920fdb6f3a83b1749881f5a0a810d2\"}","integrity":"sha512-tM4PDy1lidungnqOIb68HGjbCerXVPOv8btJl9PR0zswowC9pE3bQzGIARtw2TkUKUiBksr2/EvBmg2DGk6TUw==","time":1754204173552,"size":23546}