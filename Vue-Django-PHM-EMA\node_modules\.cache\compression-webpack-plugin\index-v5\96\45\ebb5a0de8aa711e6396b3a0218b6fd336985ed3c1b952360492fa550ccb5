
72b6fc4a0833a2d2c23106f8c2499ec322160c0c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"dfc4510295654083b87f6c8ee010ecc7\"}","integrity":"sha512-eBX/LtkqHpZQdmygxeY/WMY5Cqj3yfJ2H3OPgAl8bFvA13LFNOd5eJOcM24e24bkAUk31JPLpbpPdKdbS0S0FA==","time":1754202961627,"size":23487}