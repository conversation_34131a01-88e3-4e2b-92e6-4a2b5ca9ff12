<template>
  <div class="history-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-time"></i>
        历史数据管理
        <span class="page-subtitle">Historical Data Management</span>
      </h2>
    </div>
    <el-card>
      <div class="history-ctl" style="margin-bottom: 25px">
        <el-date-picker
          v-model="timeRange"
          :style="{ width: windowInnerWidth/3 + 'px' }"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
          @change="timeRangeSelect"
        >
        </el-date-picker>
        <el-select
          v-model="selectedDataType"
          placeholder="数据来源类型"
          style="margin-left:10px; width: 150px"
          clearable
        >
          <el-option label="全部" value="all"></el-option>
          <el-option label="设备监测" value="monitor"></el-option>
          <el-option label="故障诊断" value="diagnosis"></el-option>
        </el-select>
        <el-button type="primary" style="margin-left:10px" @click="historyData">获取历史数据</el-button>
        <el-button type="success" icon="el-icon-download" @click="handleDownload">下载表单</el-button>
      </div>
      <div class="diagnosisData">
        <el-table
          v-loading="listLoading"
          :data="tableData.slice((currentPage-1)*pageSize,currentPage*pageSize)"
          element-loading-text="Loading"
          height="290"
          :style="{height:windowInnerHeight/2.4 + 'px'}"
          border
          :default-sort="{prop: 'time', order: 'descending'}"
          :row-class-name="tableRowClassName"
        >
          <el-table-column
            type="index"
            align="center"
            label="序号"
            :index="indexMethod"
            width="80"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="time"
            label="时间"
            width="180"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="dataType"
            label="数据来源"
            width="120"
          >
            <template slot-scope="scope">
              <el-tag :type="getTagType(scope.row.dataType)">
                {{ getDataTypeLabel(scope.row.dataType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="status"
            label="故障状态"
            width="150"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'" size="medium">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="healthStatus"
            label="健康状态"
            width="100"
          >
            <template slot-scope="scope">
              <el-progress
                :percentage="parseFloat(scope.row.healthStatus) * 100"
                :color="getHealthColor(scope.row.healthStatus)"
                :format="formatHealth"
              ></el-progress>
            </template>
          </el-table-column>
          <el-table-column
            prop="detail"
            label="详细信息"
          >
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page.sync="currentPage"
          :page-sizes="[10,20,50,100,500]"
          :page-size="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
        <el-divider></el-divider>
        <div class="stateImg">
          <h3>状态历史图表</h3>
          <el-button type="primary" @click="draw">绘制状态图</el-button>
          <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
            <line-chart :chart-data="lineChartData" @func="faultBlastJump" />
          </el-row>
        </div>
      </div>
    </el-card>

  </div>
</template>

<script>
import LineChart from './components/LineChart.vue'
// import { getDataByTime } from '@/api/phm'

export default {
  name: '历史数据管理', // eslint-disable-line vue/name-property-casing
  components: {
    LineChart
  },
  data() {
    return {
      windowInnerWidth: '',
      windowInnerHeight: '',
      listLoading: false,
      dbclickType: '',
      errorNameDict: {},
      allTimeData: [],
      allStatusData: [],
      allHealthStatusData: [],
      allDataTypes: [],
      tableData: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      table1: false,
      timeRange: '',
      selectedDataType: 'all',
      pickerOptions: {
        shortcuts: [{
          text: '最近一分钟',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 60 * 1000 * 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近十分钟',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 600 * 1000 * 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一小时',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      // 父组件向子组件传值
      lineChartData: {
        title: '状态图',
        time: [],
        status: [],
        startTime: '',
        endTime: ''
      }
    }
  },
  mounted() {
    window.addEventListener('resize', this.onWindowResize, false)
    this.onWindowResize()
    console.log('34123', this.$store.state.historyClickType)
  },
  deactivated() {
    console.log('离开历史记录')
  },
  methods: {
    faultBlastJump(type) {
      this.dbclickType = parseInt(type)
      this.$store.commit('changeHistoryClickType', this.dbclickType)
      this.$router.push(`/faultBlast/index`)
    },

    // 开始画图
    draw() {
      console.log('draw')
      this.lineChartData.time = this.allTimeData
      this.lineChartData.status = this.allStatusData
    },

    // 查询历史数据
    historyData() { // Date对象 Fri Aug 27 2021 10:33:15 GMT+0800 (中国标准时间)
      this.$axios.get('./errorDict.json').then(res => {
        this.errorNameDict = res.data.errorNameDict
        console.log(this.errorNameDict)
      })
      this.listLoading = true

      // 构建请求URL，包含时间范围和可选的数据类型过滤
      let url = '/phm/getDataByTime/?startTime=' + this.lineChartData.startTime +
      '&endTime=' + this.lineChartData.endTime

      // 如果选择了特定数据类型（不是"all"），添加到URL
      if (this.selectedDataType !== 'all') {
        url += '&dataType=' + this.selectedDataType
      }

      this.$axios.get(url).then((res) => {
        this.total = res.data.data.length
        console.log(res.data)
        console.log(res.data.data)
        if (this.total !== 0) {
          console.log('1111', res.data.data[0].fields, typeof (res.data.data[0].fields))
          this.allTimeData = []
          this.allStatusData = []
          this.allHealthStatusData = []
          this.allDataTypes = [] // 添加数据类型数组
          this.tableData = [] // 清空之前的数据
          // console.log('23452', this.total)
          for (var i = 0; i < this.total; i++) {
            this.allTimeData.push(res.data.data[i].fields.mod_date) // res.data.data[i].fields得一个对象，然后对象中包含mod_date,faultStatus等
            this.allDataTypes.push(res.data.data[i].fields.type)

            // 获取故障状态并正确处理
            let statusValue = res.data.data[i].fields.faultStatus

            // 处理诊断数据的特殊状态值
            if (res.data.data[i].fields.type === 'diagnosis') {
              // 从data字段解析出故障模式
              const dataParts = res.data.data[i].fields.data.split('|')
              if (dataParts.length >= 3) {
                const faultMode = dataParts[2] // 第三部分是故障模式
                // 如果故障模式包含数字编号，提取该编号作为状态值
                if (faultMode) {
                  const faultNumber = faultMode.split('_')[0]
                  if (!isNaN(faultNumber)) {
                    statusValue = faultNumber
                  }
                }
              }
            }

            this.allStatusData.push(statusValue)
            this.allHealthStatusData.push(res.data.data[i].fields.healthStatus)

            var a = {}
            a['time'] = this.allTimeData[i]
            a['status'] = this.errorNameDict[statusValue] || (statusValue === '0' ? '正常' : '故障-' + statusValue)
            a['healthStatus'] = this.allHealthStatusData[i]
            a['dataType'] = this.allDataTypes[i]

            // 添加详细信息展示
            a['detail'] = this.getDetailInfo(res.data.data[i].fields)

            this.tableData.push(a)
          }
        } else {
          this.$notify({
            title: '提示',
            message: '所选时间段内无数据',
            duration: 2500,
            type: 'warning'
          })
          this.tableData = []
        }
        this.listLoading = false
      })
    },

    // 获取详细信息的展示
    getDetailInfo(fields) {
      let info = ''

      // 如果是设备监测数据
      if (fields.type && fields.type.startsWith('monitor_')) {
        const sensorType = fields.type.split('_')[1] // 提取传感器类型
        const sensorValue = fields.data

        // 根据传感器类型格式化显示
        switch (sensorType) {
          case 'position':
            info = `位置传感器: ${sensorValue}mm`
            break
          case 'current':
            info = `电流传感器: ${sensorValue}A`
            break
          case 'setpoint':
            info = `指令位移传感器: ${sensorValue}mm`
            break
          default:
            info = `${sensorType}: ${sensorValue}`
        }
      } else if (fields.type === 'diagnosis') {
        try {
          // 尝试解析诊断数据格式：data_file|model_used|fault_mode
          const parts = fields.data.split('|')
          if (parts.length >= 3) {
            const dataFile = parts[0]
            const modelUsed = parts[1]
            const faultMode = parts[2]

            // 格式化显示
            info = `使用模型[${modelUsed}]诊断文件[${dataFile}]的结果: ${this.getFaultModeName(faultMode)}`
          } else {
            info = fields.data
          }
        } catch (e) {
          info = fields.data
        }
      } else {
        info = fields.data
      }

      return info
    },

    // 获取故障模式的中文名称
    getFaultModeName(faultMode) {
      const faultModeMap = {
        '0_normal': '正常状态',
        '1_degradation_magnet': '永磁体退磁退化',
        '2_degradation_brush_wear': '电刷磨损退化',
        '3_degradation_commutator_oxidation': '换向器氧化退化',
        '4_fault_stator_short': '定子绕组短路故障',
        '5_fault_rotor_open': '转子绕组开路故障',
        '6_degradation_bearing_wear': '轴承磨损退化',
        '7_fault_bearing_stuck': '轴承卡死故障',
        '8_degradation_gear_wear': '齿轮磨损退化',
        '9_degradation_sensor_drift': '传感器漂移退化',
        '10_fault_sensor_loss': '传感器失效故障',
        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',
        '12_degradation_drive_distortion': '驱动信号失真退化',
        '13_fault_mcu_crash': 'MCU崩溃故障'
      }

      return faultModeMap[faultMode] || faultMode
    },

    // 获取标签类型
    getTagType(dataType) {
      if (dataType.startsWith('monitor_')) {
        return 'info'
      } else if (dataType === 'diagnosis') {
        return 'warning'
      }
      return ''
    },

    // 获取数据类型标签
    getDataTypeLabel(dataType) {
      if (dataType.startsWith('monitor_')) {
        return '设备监测'
      } else if (dataType === 'diagnosis') {
        return '故障诊断'
      }
      return dataType
    },

    // 获取健康状态颜色
    getHealthColor(healthStatus) {
      const value = parseFloat(healthStatus)
      if (value >= 0.8) {
        return '#67C23A' // 绿色，健康状态好
      } else if (value >= 0.6) {
        return '#E6A23C' // 黄色，健康状态一般
      } else {
        return '#F56C6C' // 红色，健康状态差
      }
    },

    // 格式化健康状态显示
    formatHealth(percentage) {
      return percentage + '%'
    },

    // 选择时间
    timeRangeSelect(timeRange) {
      console.log('start', timeRange[0])
      console.log('end:', timeRange[1])
      this.lineChartData.startTime = timeRange[0]
      this.lineChartData.endTime = timeRange[1]
    },
    tableRowClassName({ row, rowIndex }) {
      row.index = rowIndex
    },

    // 表格设置
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.pageSize = val
    },
    handleCurrentChange(val) {
      this.currentPage = val
      console.log(`当前页: ${val}`)
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1
    },

    // 下载表格
    handleDownload() {
      import('@/utils/Export2Excel').then(excel => {
        const tHeader = ['time', 'status']
        const filterVal = ['time', 'status']
        const data = this.formatJson(filterVal)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: 'table_' + this.$moment(new Date().getTime()).format('MM_DD_HH_mm_ss')
        })
      })
    },
    formatJson(filterVal) {
      return this.tableData.map(v => filterVal.map(j => {
        return v[j]
      }))
    },

    // 屏幕变化
    onWindowResize() {
      this.windowInnerHeight = window.innerHeight
      this.windowInnerWidth = window.innerWidth
    }
  }
}
</script>

<style lang="scss" scoped>
.history-container {
  margin: 30px;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);
  padding: 24px;

  /* 页面标题样式 */
  .page-header {
    margin-bottom: 32px;
    text-align: center;

    .page-title {
      font-size: 2em;
      font-weight: 700;
      margin: 0;
      background: linear-gradient(135deg, #00d4ff, #33ddff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;

      i {
        font-size: 1.2em;
        color: #00d4ff;
      }

      .page-subtitle {
        font-size: 0.4em;
        color: #b8c5d1;
        font-weight: 400;
        margin-top: 8px;
        letter-spacing: 1px;
        display: block;
      }
    }
  }
}

.stateImg{
  margin-top:30px
}
</style>

