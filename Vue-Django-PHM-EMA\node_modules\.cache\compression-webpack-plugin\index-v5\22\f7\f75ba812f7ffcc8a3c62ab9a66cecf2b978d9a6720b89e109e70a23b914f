
18c8ba3d4485ab7d5c47f9e12c574cc816aae157	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"20671f3fe18ff8b31a9c5a189c39aab2\"}","integrity":"sha512-iDyDyTpMMbnW/3SDhiHpuNOFbqHnqrIH21HybPzRdAVX9SUovpEv5/1dFJafa7nrqSQlmwc25ctCZn8cs9dp9Q==","time":1754202155813,"size":23565}