
16f9145563440b6c0c8d0d4bb16cfdbc96e54d60	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"2b4c308c2a645faeb1ecc80366602c67\"}","integrity":"sha512-ea79Rh9S4Sb5tMo6FIw3daJdvfUezGxWtuR9169kRo0ToL9VJckyo1b4034jaEmRHnKr1cS4r1i8E/VyZsUgSQ==","time":1754203844725,"size":26720}