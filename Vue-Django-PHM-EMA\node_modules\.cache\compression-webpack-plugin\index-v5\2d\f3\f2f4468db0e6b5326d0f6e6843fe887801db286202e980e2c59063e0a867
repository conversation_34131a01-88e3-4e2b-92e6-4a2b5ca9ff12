
3fd88e6cc97f9b4366d8043ffd33124717487f60	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"ef35d471c884bd66ef652db3f2ca1483\"}","integrity":"sha512-npvw3J1IRwHIHq6mn9zDdRsQOUgB/lagvQzAYHO+kL/r3wGx8NNafmOVN9e21BR/ZfEjFR4NO4JSH8jUM8G9gA==","time":1754201072582,"size":26671}