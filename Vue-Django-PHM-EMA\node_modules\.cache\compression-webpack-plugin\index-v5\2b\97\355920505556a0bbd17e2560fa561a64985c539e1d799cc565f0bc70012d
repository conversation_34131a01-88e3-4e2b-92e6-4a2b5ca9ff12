
3f2216eba95ac902ecd0d7ee2001f8f224ca230b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"e86021c2571041208dacd7a58e770063\"}","integrity":"sha512-kKcvMzyPcWYagkeK9CN4V4rrXjOXRH7DpBqPDiwrvove0lo+1LFAeTLUuD1xyAy1vQu+XKzPYworFKol8hxwCw==","time":1754202908289,"size":26724}