
63d71cf40a9d2ce245889d2156898c404c1ce248	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"42b2ca505c43f3981c7619b0bc42c800\"}","integrity":"sha512-1qAMf8flY0IAkdHFqoCadVXsUT1PGauUZ2vhtboeEGHb1dB6mnXERvn5Bu0N0sAnM0v6TmmGpZw7/wYIYtXtfg==","time":1754204173196,"size":153686}