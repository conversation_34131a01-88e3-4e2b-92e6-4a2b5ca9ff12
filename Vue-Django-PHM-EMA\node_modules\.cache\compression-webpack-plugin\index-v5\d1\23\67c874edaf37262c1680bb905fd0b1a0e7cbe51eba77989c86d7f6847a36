
8b72876777bb8d9d0be29ee2337fae14c66a9bcf	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.323f5cc6d70518bc133c.hot-update.js\",\"contentHash\":\"298be5304ff12c3f87a4fb6626015bd2\"}","integrity":"sha512-oKINGX+rLlkQfSsg3zCmFxtzB6CvL/JnsrorKYQbXbgImtfBe95vPFjrm2ls9DFYmL2/uAsx3Zfh/GcEgUOmgA==","time":1754202961167,"size":23276}