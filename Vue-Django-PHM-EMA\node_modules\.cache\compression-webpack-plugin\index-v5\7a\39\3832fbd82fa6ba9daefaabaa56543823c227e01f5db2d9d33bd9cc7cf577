
42559172d0d7b05b814f2ece9df7ee36b2a47e03	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"be8ad2ad69d5a9e66669b06348f65dc2\"}","integrity":"sha512-yZ+obiK+FBKTAYJYIJE8fSlzXshJ2y8g7BVMqfzha7oq8hp/4rTQ89q/aWqUysMSpkFVs1e3HUDq7MO0vKI1Gw==","time":1754205116958,"size":26748}