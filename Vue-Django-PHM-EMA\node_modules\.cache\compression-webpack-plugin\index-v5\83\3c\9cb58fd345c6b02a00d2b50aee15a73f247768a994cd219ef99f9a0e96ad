
c1bf6237e6310c6347a54a79a87247703a81a8d8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"2fc2dff4e9e1154f71533777e8c33dd2\"}","integrity":"sha512-E+svAImsSD6hG2XaZrIs1EHFMX9JCUFsomdnJK7ENhUMCxb8B9Ik+UyrWMcn+KZ0my/aKUeAfqxJTfhr51NL6w==","time":1754202867281,"size":107192}