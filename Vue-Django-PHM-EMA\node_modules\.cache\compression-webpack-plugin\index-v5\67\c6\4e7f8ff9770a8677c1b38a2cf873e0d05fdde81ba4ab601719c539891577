
42cf7ee12b18bece4a49eb45d3e47b88f096c28f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"f737d701bf1a8651e1aaa0fa0ebeae4a\"}","integrity":"sha512-ii/AzlXSv03HF4cNyB2BPLvD35XuG0YskLbmKa2mhmdspWeK9Oc3zqemREsZySnU0iFSm3d30fvtseUdhDR7rw==","time":1754204194837,"size":23546}