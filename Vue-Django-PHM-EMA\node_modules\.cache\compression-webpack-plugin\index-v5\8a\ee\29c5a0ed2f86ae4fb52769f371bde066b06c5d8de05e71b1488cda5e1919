
9ff9df90cdde8805bd7ea62dbf42177c6cfea1a2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"38cbb814b2ca9c678f5ac8f5580c82ef\"}","integrity":"sha512-x6wWC/z3p2AXSQXOzbxM3EkkAojejH65JlM7pdl7WhZOvfcpocgXad74ha6kQntePkjjrulHy7z6qTNfvDDBqw==","time":1754205133166,"size":23465}