
fa3064bb3c6d3831cbee56105822fc371077c66a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"86262e37ed0a36200d5f4b41e02b75fd\"}","integrity":"sha512-nuTNg2Pj+i+1vp3i2d1Ns8tz572O6HYG+ORg/O3VT86EuY0561NZYxYx7vV+ueDcUKzB7MLd0Ef18IhO1N/uwQ==","time":1754205154367,"size":102966}