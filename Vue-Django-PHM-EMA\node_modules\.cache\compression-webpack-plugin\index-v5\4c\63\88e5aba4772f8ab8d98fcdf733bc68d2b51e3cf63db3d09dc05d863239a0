
a302edec26718501b7a006fac5a9288f81b27e41	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"6d2a8d17407ca5fa0c37fe299a2f6cfc\"}","integrity":"sha512-kxQj+dnTy0egGMoIgtAAIsBwGpsdx89wDu12BC9WiKbWncNrETUuZsQF/ERJWD+G9EbsglBdGlAI9HIMMg+iXw==","time":1754204777093,"size":82744}