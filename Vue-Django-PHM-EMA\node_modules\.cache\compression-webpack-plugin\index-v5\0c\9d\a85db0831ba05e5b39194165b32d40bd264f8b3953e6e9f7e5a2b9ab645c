
9c0a0cb27c8732aa947d73fb452694d98d4cb282	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"0371224a184c03bfa74b7ec2c341f393\"}","integrity":"sha512-9tsXIfm8cFB0IUjPgbtB/RzTSY6WnnQaZFowByivU6ubTsjHC+uX9ekQporYwc60L3tD5ca5dgUB60tVtIfiLQ==","time":1754206025947,"size":26768}