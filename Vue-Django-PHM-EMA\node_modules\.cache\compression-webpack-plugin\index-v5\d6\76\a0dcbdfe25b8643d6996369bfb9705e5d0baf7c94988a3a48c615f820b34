
5b457a6fe6ded937cb70f47ea608c971d5fae371	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"ba71b34474bd02c48669130c5b3c8a8f\"}","integrity":"sha512-37tUx5DnGxzHNX8y+8vPlo4+Wt1u8b5AuwTZ7ydjN3pwNMqMTY5bfzqYEtWF+R8RP7XClCuNtP+Iz8NMZQ4+Xg==","time":1754204860114,"size":107349}