
5b7c880254201c47f678f26222a573ea9549e79e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.12e5083a5e90408b16f9.hot-update.js\",\"contentHash\":\"ff05f47a5693826327840e3efed702c8\"}","integrity":"sha512-mmxnFlCWba05KS34lp2qEI4VdpepK5ioQVPfPg9zGtzgS4lbXAtGLOwGaimY80hUbi1xA8LBW9r3Z8CrMXqGgQ==","time":1754204815193,"size":12245}