
ee3687a83dbb7bb5573ec222c4e601f3c8a3bb6e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"2c1673bd287cbcf6fbbbc66f158ec47c\"}","integrity":"sha512-JRc6EWS+wYAhxRMyP5y8kwJYAxgDAXn8oGIKNY5cWNyRWDcoU0BxjR8dK7SZoXPyua9NkhQ7OLC3354brESQRg==","time":1754204505143,"size":23583}