
9e697ae95264ac8c57de8af6479e7534fd525cfe	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"app.4c8fb6cdea279c7cee3b.hot-update.js\",\"contentHash\":\"bb562b9807300d21afc8c0192d1975e4\"}","integrity":"sha512-iJ0soCUv/RyJKulfaz/VTzAdVc7rSooUPap5SXnXvdfLFj4QIKlxge1glZ0vV4FhVOfFFCuP9fRBYhH4jY0Ycg==","time":1754204790186,"size":14177}