{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\history\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\history\\index.vue", "mtime": 1754206039963}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBMaW5lQ2hhcnQgZnJvbSAnLi9jb21wb25lbnRzL0xpbmVDaGFydC52dWUnCi8vIGltcG9ydCB7IGdldERhdGFCeVRpbWUgfSBmcm9tICdAL2FwaS9waG0nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ+WOhuWPsuaVsOaNrueuoeeQhicsIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgdnVlL25hbWUtcHJvcGVydHktY2FzaW5nCiAgY29tcG9uZW50czogewogICAgTGluZUNoYXJ0CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgd2luZG93SW5uZXJXaWR0aDogJycsCiAgICAgIHdpbmRvd0lubmVySGVpZ2h0OiAnJywKICAgICAgbGlzdExvYWRpbmc6IGZhbHNlLAogICAgICBkYmNsaWNrVHlwZTogJycsCiAgICAgIGVycm9yTmFtZURpY3Q6IHt9LAogICAgICBhbGxUaW1lRGF0YTogW10sCiAgICAgIGFsbFN0YXR1c0RhdGE6IFtdLAogICAgICBhbGxIZWFsdGhTdGF0dXNEYXRhOiBbXSwKICAgICAgYWxsRGF0YVR5cGVzOiBbXSwKICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgdG90YWw6IDAsCiAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgY3VycmVudFBhZ2U6IDEsCiAgICAgIHRhYmxlMTogZmFsc2UsCiAgICAgIHRpbWVSYW5nZTogJycsCiAgICAgIHNlbGVjdGVkRGF0YVR5cGU6ICdhbGwnLAogICAgICBwaWNrZXJPcHRpb25zOiB7CiAgICAgICAgc2hvcnRjdXRzOiBbewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWIhumSnycsCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpCiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDYwICogMTAwMCAqIDEpCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSkKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0ZXh0OiAn5pyA6L+R5Y2B5YiG6ZKfJywKICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCkKICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpCiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gNjAwICogMTAwMCAqIDEpCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSkKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0ZXh0OiAn5pyA6L+R5LiA5bCP5pe2JywKICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCkKICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpCiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAxKQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWkqScsCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpCiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiAxKQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWRqCcsCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpCiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOaciCcsCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpCiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQogICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiAzMCkKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKQogICAgICAgICAgfQogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOeItue7hOS7tuWQkeWtkOe7hOS7tuS8oOWAvAogICAgICBsaW5lQ2hhcnREYXRhOiB7CiAgICAgICAgdGl0bGU6ICfnirbmgIHlm74nLAogICAgICAgIHRpbWU6IFtdLAogICAgICAgIHN0YXR1czogW10sCiAgICAgICAgc3RhcnRUaW1lOiAnJywKICAgICAgICBlbmRUaW1lOiAnJwogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMub25XaW5kb3dSZXNpemUsIGZhbHNlKQogICAgdGhpcy5vbldpbmRvd1Jlc2l6ZSgpCiAgICBjb25zb2xlLmxvZygnMzQxMjMnLCB0aGlzLiRzdG9yZS5zdGF0ZS5oaXN0b3J5Q2xpY2tUeXBlKQogIH0sCiAgZGVhY3RpdmF0ZWQoKSB7CiAgICBjb25zb2xlLmxvZygn56a75byA5Y6G5Y+y6K6w5b2VJykKICB9LAogIG1ldGhvZHM6IHsKICAgIGZhdWx0Qmxhc3RKdW1wKHR5cGUpIHsKICAgICAgdGhpcy5kYmNsaWNrVHlwZSA9IHBhcnNlSW50KHR5cGUpCiAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgnY2hhbmdlSGlzdG9yeUNsaWNrVHlwZScsIHRoaXMuZGJjbGlja1R5cGUpCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKGAvZmF1bHRCbGFzdC9pbmRleGApCiAgICB9LAoKICAgIC8vIOW8gOWni+eUu+WbvgogICAgZHJhdygpIHsKICAgICAgY29uc29sZS5sb2coJ2RyYXcnKQogICAgICB0aGlzLmxpbmVDaGFydERhdGEudGltZSA9IHRoaXMuYWxsVGltZURhdGEKICAgICAgdGhpcy5saW5lQ2hhcnREYXRhLnN0YXR1cyA9IHRoaXMuYWxsU3RhdHVzRGF0YQogICAgfSwKCiAgICAvLyDmn6Xor6Lljoblj7LmlbDmja4KICAgIGhpc3RvcnlEYXRhKCkgeyAvLyBEYXRl5a+56LGhIEZyaSBBdWcgMjcgMjAyMSAxMDozMzoxNSBHTVQrMDgwMCAo5Lit5Zu95qCH5YeG5pe26Ze0KQogICAgICB0aGlzLiRheGlvcy5nZXQoJy4vZXJyb3JEaWN0Lmpzb24nKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5lcnJvck5hbWVEaWN0ID0gcmVzLmRhdGEuZXJyb3JOYW1lRGljdAogICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZXJyb3JOYW1lRGljdCkKICAgICAgfSkKICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWUKCiAgICAgIC8vIOaehOW7uuivt+axglVSTO+8jOWMheWQq+aXtumXtOiMg+WbtOWSjOWPr+mAieeahOaVsOaNruexu+Wei+i/h+a7pAogICAgICBsZXQgdXJsID0gJy9waG0vZ2V0RGF0YUJ5VGltZS8/c3RhcnRUaW1lPScgKyB0aGlzLmxpbmVDaGFydERhdGEuc3RhcnRUaW1lICsKICAgICAgJyZlbmRUaW1lPScgKyB0aGlzLmxpbmVDaGFydERhdGEuZW5kVGltZQoKICAgICAgLy8g5aaC5p6c6YCJ5oup5LqG54m55a6a5pWw5o2u57G75Z6L77yI5LiN5pivImFsbCLvvInvvIzmt7vliqDliLBVUkwKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWREYXRhVHlwZSAhPT0gJ2FsbCcpIHsKICAgICAgICB1cmwgKz0gJyZkYXRhVHlwZT0nICsgdGhpcy5zZWxlY3RlZERhdGFUeXBlCiAgICAgIH0KCiAgICAgIHRoaXMuJGF4aW9zLmdldCh1cmwpLnRoZW4oKHJlcykgPT4gewogICAgICAgIHRoaXMudG90YWwgPSByZXMuZGF0YS5kYXRhLmxlbmd0aAogICAgICAgIGNvbnNvbGUubG9nKHJlcy5kYXRhKQogICAgICAgIGNvbnNvbGUubG9nKHJlcy5kYXRhLmRhdGEpCiAgICAgICAgaWYgKHRoaXMudG90YWwgIT09IDApIHsKICAgICAgICAgIGNvbnNvbGUubG9nKCcxMTExJywgcmVzLmRhdGEuZGF0YVswXS5maWVsZHMsIHR5cGVvZiAocmVzLmRhdGEuZGF0YVswXS5maWVsZHMpKQogICAgICAgICAgdGhpcy5hbGxUaW1lRGF0YSA9IFtdCiAgICAgICAgICB0aGlzLmFsbFN0YXR1c0RhdGEgPSBbXQogICAgICAgICAgdGhpcy5hbGxIZWFsdGhTdGF0dXNEYXRhID0gW10KICAgICAgICAgIHRoaXMuYWxsRGF0YVR5cGVzID0gW10gLy8g5re75Yqg5pWw5o2u57G75Z6L5pWw57uECiAgICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IFtdIC8vIOa4heepuuS5i+WJjeeahOaVsOaNrgogICAgICAgICAgLy8gY29uc29sZS5sb2coJzIzNDUyJywgdGhpcy50b3RhbCkKICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdGhpcy50b3RhbDsgaSsrKSB7CiAgICAgICAgICAgIHRoaXMuYWxsVGltZURhdGEucHVzaChyZXMuZGF0YS5kYXRhW2ldLmZpZWxkcy5tb2RfZGF0ZSkgLy8gcmVzLmRhdGEuZGF0YVtpXS5maWVsZHPlvpfkuIDkuKrlr7nosaHvvIznhLblkI7lr7nosaHkuK3ljIXlkKttb2RfZGF0ZSxmYXVsdFN0YXR1c+etiQogICAgICAgICAgICB0aGlzLmFsbERhdGFUeXBlcy5wdXNoKHJlcy5kYXRhLmRhdGFbaV0uZmllbGRzLnR5cGUpCgogICAgICAgICAgICAvLyDojrflj5bmlYXpmpznirbmgIHlubbmraPnoa7lpITnkIYKICAgICAgICAgICAgbGV0IHN0YXR1c1ZhbHVlID0gcmVzLmRhdGEuZGF0YVtpXS5maWVsZHMuZmF1bHRTdGF0dXMKCiAgICAgICAgICAgIC8vIOWkhOeQhuiviuaWreaVsOaNrueahOeJueauiueKtuaAgeWAvAogICAgICAgICAgICBpZiAocmVzLmRhdGEuZGF0YVtpXS5maWVsZHMudHlwZSA9PT0gJ2RpYWdub3NpcycpIHsKICAgICAgICAgICAgICAvLyDku45kYXRh5a2X5q616Kej5p6Q5Ye65pWF6Zqc5qih5byPCiAgICAgICAgICAgICAgY29uc3QgZGF0YVBhcnRzID0gcmVzLmRhdGEuZGF0YVtpXS5maWVsZHMuZGF0YS5zcGxpdCgnfCcpCiAgICAgICAgICAgICAgaWYgKGRhdGFQYXJ0cy5sZW5ndGggPj0gMykgewogICAgICAgICAgICAgICAgY29uc3QgZmF1bHRNb2RlID0gZGF0YVBhcnRzWzJdIC8vIOesrOS4iemDqOWIhuaYr+aVhemanOaooeW8jwogICAgICAgICAgICAgICAgLy8g5aaC5p6c5pWF6Zqc5qih5byP5YyF5ZCr5pWw5a2X57yW5Y+377yM5o+Q5Y+W6K+l57yW5Y+35L2c5Li654q25oCB5YC8CiAgICAgICAgICAgICAgICBpZiAoZmF1bHRNb2RlKSB7CiAgICAgICAgICAgICAgICAgIGNvbnN0IGZhdWx0TnVtYmVyID0gZmF1bHRNb2RlLnNwbGl0KCdfJylbMF0KICAgICAgICAgICAgICAgICAgaWYgKCFpc05hTihmYXVsdE51bWJlcikpIHsKICAgICAgICAgICAgICAgICAgICBzdGF0dXNWYWx1ZSA9IGZhdWx0TnVtYmVyCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIHRoaXMuYWxsU3RhdHVzRGF0YS5wdXNoKHN0YXR1c1ZhbHVlKQogICAgICAgICAgICB0aGlzLmFsbEhlYWx0aFN0YXR1c0RhdGEucHVzaChyZXMuZGF0YS5kYXRhW2ldLmZpZWxkcy5oZWFsdGhTdGF0dXMpCgogICAgICAgICAgICB2YXIgYSA9IHt9CiAgICAgICAgICAgIGFbJ3RpbWUnXSA9IHRoaXMuYWxsVGltZURhdGFbaV0KICAgICAgICAgICAgYVsnc3RhdHVzJ10gPSB0aGlzLmVycm9yTmFtZURpY3Rbc3RhdHVzVmFsdWVdIHx8IChzdGF0dXNWYWx1ZSA9PT0gJzAnID8gJ+ato+W4uCcgOiAn5pWF6ZqcLScgKyBzdGF0dXNWYWx1ZSkKICAgICAgICAgICAgYVsnaGVhbHRoU3RhdHVzJ10gPSB0aGlzLmFsbEhlYWx0aFN0YXR1c0RhdGFbaV0KICAgICAgICAgICAgYVsnZGF0YVR5cGUnXSA9IHRoaXMuYWxsRGF0YVR5cGVzW2ldCgogICAgICAgICAgICAvLyDmt7vliqDor6bnu4bkv6Hmga/lsZXnpLoKICAgICAgICAgICAgYVsnZGV0YWlsJ10gPSB0aGlzLmdldERldGFpbEluZm8ocmVzLmRhdGEuZGF0YVtpXS5maWVsZHMpCgogICAgICAgICAgICB0aGlzLnRhYmxlRGF0YS5wdXNoKGEpCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywKICAgICAgICAgICAgbWVzc2FnZTogJ+aJgOmAieaXtumXtOauteWGheaXoOaVsOaNricsCiAgICAgICAgICAgIGR1cmF0aW9uOiAyNTAwLAogICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IFtdCiAgICAgICAgfQogICAgICAgIHRoaXMubGlzdExvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKCiAgICAvLyDojrflj5bor6bnu4bkv6Hmga/nmoTlsZXnpLoKICAgIGdldERldGFpbEluZm8oZmllbGRzKSB7CiAgICAgIGxldCBpbmZvID0gJycKCiAgICAgIC8vIOWmguaenOaYr+iuvuWkh+ebkea1i+aVsOaNrgogICAgICBpZiAoZmllbGRzLnR5cGUgJiYgZmllbGRzLnR5cGUuc3RhcnRzV2l0aCgnbW9uaXRvcl8nKSkgewogICAgICAgIGNvbnN0IHNlbnNvclR5cGUgPSBmaWVsZHMudHlwZS5zcGxpdCgnXycpWzFdIC8vIOaPkOWPluS8oOaEn+WZqOexu+WeiwogICAgICAgIGNvbnN0IHNlbnNvclZhbHVlID0gZmllbGRzLmRhdGEKCiAgICAgICAgLy8g5qC55o2u5Lyg5oSf5Zmo57G75Z6L5qC85byP5YyW5pi+56S6CiAgICAgICAgc3dpdGNoIChzZW5zb3JUeXBlKSB7CiAgICAgICAgICBjYXNlICdwb3NpdGlvbic6CiAgICAgICAgICAgIGluZm8gPSBg5L2N572u5Lyg5oSf5ZmoOiAke3NlbnNvclZhbHVlfW1tYAogICAgICAgICAgICBicmVhawogICAgICAgICAgY2FzZSAnY3VycmVudCc6CiAgICAgICAgICAgIGluZm8gPSBg55S15rWB5Lyg5oSf5ZmoOiAke3NlbnNvclZhbHVlfUFgCiAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICBjYXNlICdzZXRwb2ludCc6CiAgICAgICAgICAgIGluZm8gPSBg5oyH5Luk5L2N56e75Lyg5oSf5ZmoOiAke3NlbnNvclZhbHVlfW1tYAogICAgICAgICAgICBicmVhawogICAgICAgICAgZGVmYXVsdDoKICAgICAgICAgICAgaW5mbyA9IGAke3NlbnNvclR5cGV9OiAke3NlbnNvclZhbHVlfWAKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAoZmllbGRzLnR5cGUgPT09ICdkaWFnbm9zaXMnKSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIC8vIOWwneivleino+aekOiviuaWreaVsOaNruagvOW8j++8mmRhdGFfZmlsZXxtb2RlbF91c2VkfGZhdWx0X21vZGUKICAgICAgICAgIGNvbnN0IHBhcnRzID0gZmllbGRzLmRhdGEuc3BsaXQoJ3wnKQogICAgICAgICAgaWYgKHBhcnRzLmxlbmd0aCA+PSAzKSB7CiAgICAgICAgICAgIGNvbnN0IGRhdGFGaWxlID0gcGFydHNbMF0KICAgICAgICAgICAgY29uc3QgbW9kZWxVc2VkID0gcGFydHNbMV0KICAgICAgICAgICAgY29uc3QgZmF1bHRNb2RlID0gcGFydHNbMl0KCiAgICAgICAgICAgIC8vIOagvOW8j+WMluaYvuekugogICAgICAgICAgICBpbmZvID0gYOS9v+eUqOaooeWei1ske21vZGVsVXNlZH1d6K+K5pat5paH5Lu2WyR7ZGF0YUZpbGV9XeeahOe7k+aenDogJHt0aGlzLmdldEZhdWx0TW9kZU5hbWUoZmF1bHRNb2RlKX1gCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBpbmZvID0gZmllbGRzLmRhdGEKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICBpbmZvID0gZmllbGRzLmRhdGEKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaW5mbyA9IGZpZWxkcy5kYXRhCiAgICAgIH0KCiAgICAgIHJldHVybiBpbmZvCiAgICB9LAoKICAgIC8vIOiOt+WPluaVhemanOaooeW8j+eahOS4reaWh+WQjeensAogICAgZ2V0RmF1bHRNb2RlTmFtZShmYXVsdE1vZGUpIHsKICAgICAgY29uc3QgZmF1bHRNb2RlTWFwID0gewogICAgICAgICcwX25vcm1hbCc6ICfmraPluLjnirbmgIEnLAogICAgICAgICcxX2RlZ3JhZGF0aW9uX21hZ25ldCc6ICfmsLjno4HkvZPpgIDno4HpgIDljJYnLAogICAgICAgICcyX2RlZ3JhZGF0aW9uX2JydXNoX3dlYXInOiAn55S15Yi356Oo5o2f6YCA5YyWJywKICAgICAgICAnM19kZWdyYWRhdGlvbl9jb21tdXRhdG9yX294aWRhdGlvbic6ICfmjaLlkJHlmajmsKfljJbpgIDljJYnLAogICAgICAgICc0X2ZhdWx0X3N0YXRvcl9zaG9ydCc6ICflrprlrZDnu5Xnu4Tnn63ot6/mlYXpmpwnLAogICAgICAgICc1X2ZhdWx0X3JvdG9yX29wZW4nOiAn6L2s5a2Q57uV57uE5byA6Lev5pWF6ZqcJywKICAgICAgICAnNl9kZWdyYWRhdGlvbl9iZWFyaW5nX3dlYXInOiAn6L205om/56Oo5o2f6YCA5YyWJywKICAgICAgICAnN19mYXVsdF9iZWFyaW5nX3N0dWNrJzogJ+i9tOaJv+WNoeatu+aVhemanCcsCiAgICAgICAgJzhfZGVncmFkYXRpb25fZ2Vhcl93ZWFyJzogJ+m9v+i9ruejqOaNn+mAgOWMlicsCiAgICAgICAgJzlfZGVncmFkYXRpb25fc2Vuc29yX2RyaWZ0JzogJ+S8oOaEn+WZqOa8guenu+mAgOWMlicsCiAgICAgICAgJzEwX2ZhdWx0X3NlbnNvcl9sb3NzJzogJ+S8oOaEn+WZqOWkseaViOaVhemanCcsCiAgICAgICAgJzExX2ZhdWx0X21vc2ZldF9icmVha2Rvd24nOiAnTU9TRkVU5Ye756m/5pWF6ZqcJywKICAgICAgICAnMTJfZGVncmFkYXRpb25fZHJpdmVfZGlzdG9ydGlvbic6ICfpqbHliqjkv6Hlj7flpLHnnJ/pgIDljJYnLAogICAgICAgICcxM19mYXVsdF9tY3VfY3Jhc2gnOiAnTUNV5bSp5rqD5pWF6ZqcJwogICAgICB9CgogICAgICByZXR1cm4gZmF1bHRNb2RlTWFwW2ZhdWx0TW9kZV0gfHwgZmF1bHRNb2RlCiAgICB9LAoKICAgIC8vIOiOt+WPluagh+etvuexu+WeiwogICAgZ2V0VGFnVHlwZShkYXRhVHlwZSkgewogICAgICBpZiAoZGF0YVR5cGUuc3RhcnRzV2l0aCgnbW9uaXRvcl8nKSkgewogICAgICAgIHJldHVybiAnaW5mbycKICAgICAgfSBlbHNlIGlmIChkYXRhVHlwZSA9PT0gJ2RpYWdub3NpcycpIHsKICAgICAgICByZXR1cm4gJ3dhcm5pbmcnCiAgICAgIH0KICAgICAgcmV0dXJuICcnCiAgICB9LAoKICAgIC8vIOiOt+WPluaVsOaNruexu+Wei+agh+etvgogICAgZ2V0RGF0YVR5cGVMYWJlbChkYXRhVHlwZSkgewogICAgICBpZiAoZGF0YVR5cGUuc3RhcnRzV2l0aCgnbW9uaXRvcl8nKSkgewogICAgICAgIHJldHVybiAn6K6+5aSH55uR5rWLJwogICAgICB9IGVsc2UgaWYgKGRhdGFUeXBlID09PSAnZGlhZ25vc2lzJykgewogICAgICAgIHJldHVybiAn5pWF6Zqc6K+K5patJwogICAgICB9CiAgICAgIHJldHVybiBkYXRhVHlwZQogICAgfSwKCiAgICAvLyDojrflj5blgaXlurfnirbmgIHpopzoibIKICAgIGdldEhlYWx0aENvbG9yKGhlYWx0aFN0YXR1cykgewogICAgICBjb25zdCB2YWx1ZSA9IHBhcnNlRmxvYXQoaGVhbHRoU3RhdHVzKQogICAgICBpZiAodmFsdWUgPj0gMC44KSB7CiAgICAgICAgcmV0dXJuICcjNjdDMjNBJyAvLyDnu7/oibLvvIzlgaXlurfnirbmgIHlpb0KICAgICAgfSBlbHNlIGlmICh2YWx1ZSA+PSAwLjYpIHsKICAgICAgICByZXR1cm4gJyNFNkEyM0MnIC8vIOm7hOiJsu+8jOWBpeW6t+eKtuaAgeS4gOiIrAogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnI0Y1NkM2QycgLy8g57qi6Imy77yM5YGl5bq354q25oCB5beuCiAgICAgIH0KICAgIH0sCgogICAgLy8g5qC85byP5YyW5YGl5bq354q25oCB5pi+56S6CiAgICBmb3JtYXRIZWFsdGgocGVyY2VudGFnZSkgewogICAgICByZXR1cm4gcGVyY2VudGFnZSArICclJwogICAgfSwKCiAgICAvLyDpgInmi6nml7bpl7QKICAgIHRpbWVSYW5nZVNlbGVjdCh0aW1lUmFuZ2UpIHsKICAgICAgY29uc29sZS5sb2coJ3N0YXJ0JywgdGltZVJhbmdlWzBdKQogICAgICBjb25zb2xlLmxvZygnZW5kOicsIHRpbWVSYW5nZVsxXSkKICAgICAgdGhpcy5saW5lQ2hhcnREYXRhLnN0YXJ0VGltZSA9IHRpbWVSYW5nZVswXQogICAgICB0aGlzLmxpbmVDaGFydERhdGEuZW5kVGltZSA9IHRpbWVSYW5nZVsxXQogICAgfSwKICAgIHRhYmxlUm93Q2xhc3NOYW1lKHsgcm93LCByb3dJbmRleCB9KSB7CiAgICAgIHJvdy5pbmRleCA9IHJvd0luZGV4CiAgICB9LAoKICAgIC8vIOihqOagvOiuvue9rgogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgY29uc29sZS5sb2coYOavj+mhtSAke3ZhbH0g5p2hYCkKICAgICAgdGhpcy5wYWdlU2l6ZSA9IHZhbAogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSB2YWwKICAgICAgY29uc29sZS5sb2coYOW9k+WJjemhtTogJHt2YWx9YCkKICAgIH0sCiAgICBpbmRleE1ldGhvZChpbmRleCkgewogICAgICByZXR1cm4gKHRoaXMuY3VycmVudFBhZ2UgLSAxKSAqIHRoaXMucGFnZVNpemUgKyBpbmRleCArIDEKICAgIH0sCgogICAgLy8g5LiL6L296KGo5qC8CiAgICBoYW5kbGVEb3dubG9hZCgpIHsKICAgICAgaW1wb3J0KCdAL3V0aWxzL0V4cG9ydDJFeGNlbCcpLnRoZW4oZXhjZWwgPT4gewogICAgICAgIGNvbnN0IHRIZWFkZXIgPSBbJ3RpbWUnLCAnc3RhdHVzJ10KICAgICAgICBjb25zdCBmaWx0ZXJWYWwgPSBbJ3RpbWUnLCAnc3RhdHVzJ10KICAgICAgICBjb25zdCBkYXRhID0gdGhpcy5mb3JtYXRKc29uKGZpbHRlclZhbCkKICAgICAgICBleGNlbC5leHBvcnRfanNvbl90b19leGNlbCh7CiAgICAgICAgICBoZWFkZXI6IHRIZWFkZXIsCiAgICAgICAgICBkYXRhLAogICAgICAgICAgZmlsZW5hbWU6ICd0YWJsZV8nICsgdGhpcy4kbW9tZW50KG5ldyBEYXRlKCkuZ2V0VGltZSgpKS5mb3JtYXQoJ01NX0REX0hIX21tX3NzJykKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGZvcm1hdEpzb24oZmlsdGVyVmFsKSB7CiAgICAgIHJldHVybiB0aGlzLnRhYmxlRGF0YS5tYXAodiA9PiBmaWx0ZXJWYWwubWFwKGogPT4gewogICAgICAgIHJldHVybiB2W2pdCiAgICAgIH0pKQogICAgfSwKCiAgICAvLyDlsY/luZXlj5jljJYKICAgIG9uV2luZG93UmVzaXplKCkgewogICAgICB0aGlzLndpbmRvd0lubmVySGVpZ2h0ID0gd2luZG93LmlubmVySGVpZ2h0CiAgICAgIHRoaXMud2luZG93SW5uZXJXaWR0aCA9IHdpbmRvdy5pbm5lcldpZHRoCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/history", "sourcesContent": ["<template>\n  <div class=\"history-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-time\"></i>\n        历史数据管理\n        <span class=\"page-subtitle\">Historical Data Management</span>\n      </h2>\n    </div>\n    <el-card>\n      <div class=\"history-ctl\" style=\"margin-bottom: 25px\">\n        <el-date-picker\n          v-model=\"timeRange\"\n          :style=\"{ width: windowInnerWidth/3 + 'px' }\"\n          type=\"datetimerange\"\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          :picker-options=\"pickerOptions\"\n          range-separator=\"至\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          align=\"right\"\n          @change=\"timeRangeSelect\"\n        >\n        </el-date-picker>\n        <el-select\n          v-model=\"selectedDataType\"\n          placeholder=\"数据来源类型\"\n          style=\"margin-left:10px; width: 150px\"\n          clearable\n        >\n          <el-option label=\"全部\" value=\"all\"></el-option>\n          <el-option label=\"设备监测\" value=\"monitor\"></el-option>\n          <el-option label=\"故障诊断\" value=\"diagnosis\"></el-option>\n        </el-select>\n        <el-button type=\"primary\" style=\"margin-left:10px\" @click=\"historyData\">获取历史数据</el-button>\n        <el-button type=\"success\" icon=\"el-icon-download\" @click=\"handleDownload\">下载表单</el-button>\n      </div>\n      <div class=\"diagnosisData\">\n        <el-table\n          v-loading=\"listLoading\"\n          :data=\"tableData.slice((currentPage-1)*pageSize,currentPage*pageSize)\"\n          element-loading-text=\"Loading\"\n          height=\"290\"\n          :style=\"{height:windowInnerHeight/2.4 + 'px'}\"\n          border\n          :default-sort=\"{prop: 'time', order: 'descending'}\"\n          :row-class-name=\"tableRowClassName\"\n        >\n          <el-table-column\n            type=\"index\"\n            align=\"center\"\n            label=\"序号\"\n            :index=\"indexMethod\"\n            width=\"80\"\n            sortable\n          >\n          </el-table-column>\n          <el-table-column\n            prop=\"time\"\n            label=\"时间\"\n            width=\"180\"\n            sortable\n          >\n          </el-table-column>\n          <el-table-column\n            prop=\"dataType\"\n            label=\"数据来源\"\n            width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getTagType(scope.row.dataType)\">\n                {{ getDataTypeLabel(scope.row.dataType) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"status\"\n            label=\"故障状态\"\n            width=\"150\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.status === '正常' ? 'success' : 'danger'\" size=\"medium\">\n                {{ scope.row.status }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"healthStatus\"\n            label=\"健康状态\"\n            width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <el-progress\n                :percentage=\"parseFloat(scope.row.healthStatus) * 100\"\n                :color=\"getHealthColor(scope.row.healthStatus)\"\n                :format=\"formatHealth\"\n              ></el-progress>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"detail\"\n            label=\"详细信息\"\n          >\n          </el-table-column>\n        </el-table>\n        <el-pagination\n          :current-page.sync=\"currentPage\"\n          :page-sizes=\"[10,20,50,100,500]\"\n          :page-size=\"pageSize\"\n          :total=\"total\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        >\n        </el-pagination>\n        <el-divider></el-divider>\n        <div class=\"stateImg\">\n          <h3>状态历史图表</h3>\n          <el-button type=\"primary\" @click=\"draw\">绘制状态图</el-button>\n          <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\n            <line-chart :chart-data=\"lineChartData\" @func=\"faultBlastJump\" />\n          </el-row>\n        </div>\n      </div>\n    </el-card>\n\n  </div>\n</template>\n\n<script>\nimport LineChart from './components/LineChart.vue'\n// import { getDataByTime } from '@/api/phm'\n\nexport default {\n  name: '历史数据管理', // eslint-disable-line vue/name-property-casing\n  components: {\n    LineChart\n  },\n  data() {\n    return {\n      windowInnerWidth: '',\n      windowInnerHeight: '',\n      listLoading: false,\n      dbclickType: '',\n      errorNameDict: {},\n      allTimeData: [],\n      allStatusData: [],\n      allHealthStatusData: [],\n      allDataTypes: [],\n      tableData: [],\n      total: 0,\n      pageSize: 10,\n      currentPage: 1,\n      table1: false,\n      timeRange: '',\n      selectedDataType: 'all',\n      pickerOptions: {\n        shortcuts: [{\n          text: '最近一分钟',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 60 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近十分钟',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 600 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一小时',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一天',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一周',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一月',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\n            picker.$emit('pick', [start, end])\n          }\n        }]\n      },\n      // 父组件向子组件传值\n      lineChartData: {\n        title: '状态图',\n        time: [],\n        status: [],\n        startTime: '',\n        endTime: ''\n      }\n    }\n  },\n  mounted() {\n    window.addEventListener('resize', this.onWindowResize, false)\n    this.onWindowResize()\n    console.log('34123', this.$store.state.historyClickType)\n  },\n  deactivated() {\n    console.log('离开历史记录')\n  },\n  methods: {\n    faultBlastJump(type) {\n      this.dbclickType = parseInt(type)\n      this.$store.commit('changeHistoryClickType', this.dbclickType)\n      this.$router.push(`/faultBlast/index`)\n    },\n\n    // 开始画图\n    draw() {\n      console.log('draw')\n      this.lineChartData.time = this.allTimeData\n      this.lineChartData.status = this.allStatusData\n    },\n\n    // 查询历史数据\n    historyData() { // Date对象 Fri Aug 27 2021 10:33:15 GMT+0800 (中国标准时间)\n      this.$axios.get('./errorDict.json').then(res => {\n        this.errorNameDict = res.data.errorNameDict\n        console.log(this.errorNameDict)\n      })\n      this.listLoading = true\n\n      // 构建请求URL，包含时间范围和可选的数据类型过滤\n      let url = '/phm/getDataByTime/?startTime=' + this.lineChartData.startTime +\n      '&endTime=' + this.lineChartData.endTime\n\n      // 如果选择了特定数据类型（不是\"all\"），添加到URL\n      if (this.selectedDataType !== 'all') {\n        url += '&dataType=' + this.selectedDataType\n      }\n\n      this.$axios.get(url).then((res) => {\n        this.total = res.data.data.length\n        console.log(res.data)\n        console.log(res.data.data)\n        if (this.total !== 0) {\n          console.log('1111', res.data.data[0].fields, typeof (res.data.data[0].fields))\n          this.allTimeData = []\n          this.allStatusData = []\n          this.allHealthStatusData = []\n          this.allDataTypes = [] // 添加数据类型数组\n          this.tableData = [] // 清空之前的数据\n          // console.log('23452', this.total)\n          for (var i = 0; i < this.total; i++) {\n            this.allTimeData.push(res.data.data[i].fields.mod_date) // res.data.data[i].fields得一个对象，然后对象中包含mod_date,faultStatus等\n            this.allDataTypes.push(res.data.data[i].fields.type)\n\n            // 获取故障状态并正确处理\n            let statusValue = res.data.data[i].fields.faultStatus\n\n            // 处理诊断数据的特殊状态值\n            if (res.data.data[i].fields.type === 'diagnosis') {\n              // 从data字段解析出故障模式\n              const dataParts = res.data.data[i].fields.data.split('|')\n              if (dataParts.length >= 3) {\n                const faultMode = dataParts[2] // 第三部分是故障模式\n                // 如果故障模式包含数字编号，提取该编号作为状态值\n                if (faultMode) {\n                  const faultNumber = faultMode.split('_')[0]\n                  if (!isNaN(faultNumber)) {\n                    statusValue = faultNumber\n                  }\n                }\n              }\n            }\n\n            this.allStatusData.push(statusValue)\n            this.allHealthStatusData.push(res.data.data[i].fields.healthStatus)\n\n            var a = {}\n            a['time'] = this.allTimeData[i]\n            a['status'] = this.errorNameDict[statusValue] || (statusValue === '0' ? '正常' : '故障-' + statusValue)\n            a['healthStatus'] = this.allHealthStatusData[i]\n            a['dataType'] = this.allDataTypes[i]\n\n            // 添加详细信息展示\n            a['detail'] = this.getDetailInfo(res.data.data[i].fields)\n\n            this.tableData.push(a)\n          }\n        } else {\n          this.$notify({\n            title: '提示',\n            message: '所选时间段内无数据',\n            duration: 2500,\n            type: 'warning'\n          })\n          this.tableData = []\n        }\n        this.listLoading = false\n      })\n    },\n\n    // 获取详细信息的展示\n    getDetailInfo(fields) {\n      let info = ''\n\n      // 如果是设备监测数据\n      if (fields.type && fields.type.startsWith('monitor_')) {\n        const sensorType = fields.type.split('_')[1] // 提取传感器类型\n        const sensorValue = fields.data\n\n        // 根据传感器类型格式化显示\n        switch (sensorType) {\n          case 'position':\n            info = `位置传感器: ${sensorValue}mm`\n            break\n          case 'current':\n            info = `电流传感器: ${sensorValue}A`\n            break\n          case 'setpoint':\n            info = `指令位移传感器: ${sensorValue}mm`\n            break\n          default:\n            info = `${sensorType}: ${sensorValue}`\n        }\n      } else if (fields.type === 'diagnosis') {\n        try {\n          // 尝试解析诊断数据格式：data_file|model_used|fault_mode\n          const parts = fields.data.split('|')\n          if (parts.length >= 3) {\n            const dataFile = parts[0]\n            const modelUsed = parts[1]\n            const faultMode = parts[2]\n\n            // 格式化显示\n            info = `使用模型[${modelUsed}]诊断文件[${dataFile}]的结果: ${this.getFaultModeName(faultMode)}`\n          } else {\n            info = fields.data\n          }\n        } catch (e) {\n          info = fields.data\n        }\n      } else {\n        info = fields.data\n      }\n\n      return info\n    },\n\n    // 获取故障模式的中文名称\n    getFaultModeName(faultMode) {\n      const faultModeMap = {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n\n      return faultModeMap[faultMode] || faultMode\n    },\n\n    // 获取标签类型\n    getTagType(dataType) {\n      if (dataType.startsWith('monitor_')) {\n        return 'info'\n      } else if (dataType === 'diagnosis') {\n        return 'warning'\n      }\n      return ''\n    },\n\n    // 获取数据类型标签\n    getDataTypeLabel(dataType) {\n      if (dataType.startsWith('monitor_')) {\n        return '设备监测'\n      } else if (dataType === 'diagnosis') {\n        return '故障诊断'\n      }\n      return dataType\n    },\n\n    // 获取健康状态颜色\n    getHealthColor(healthStatus) {\n      const value = parseFloat(healthStatus)\n      if (value >= 0.8) {\n        return '#67C23A' // 绿色，健康状态好\n      } else if (value >= 0.6) {\n        return '#E6A23C' // 黄色，健康状态一般\n      } else {\n        return '#F56C6C' // 红色，健康状态差\n      }\n    },\n\n    // 格式化健康状态显示\n    formatHealth(percentage) {\n      return percentage + '%'\n    },\n\n    // 选择时间\n    timeRangeSelect(timeRange) {\n      console.log('start', timeRange[0])\n      console.log('end:', timeRange[1])\n      this.lineChartData.startTime = timeRange[0]\n      this.lineChartData.endTime = timeRange[1]\n    },\n    tableRowClassName({ row, rowIndex }) {\n      row.index = rowIndex\n    },\n\n    // 表格设置\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.pageSize = val\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n      console.log(`当前页: ${val}`)\n    },\n    indexMethod(index) {\n      return (this.currentPage - 1) * this.pageSize + index + 1\n    },\n\n    // 下载表格\n    handleDownload() {\n      import('@/utils/Export2Excel').then(excel => {\n        const tHeader = ['time', 'status']\n        const filterVal = ['time', 'status']\n        const data = this.formatJson(filterVal)\n        excel.export_json_to_excel({\n          header: tHeader,\n          data,\n          filename: 'table_' + this.$moment(new Date().getTime()).format('MM_DD_HH_mm_ss')\n        })\n      })\n    },\n    formatJson(filterVal) {\n      return this.tableData.map(v => filterVal.map(j => {\n        return v[j]\n      }))\n    },\n\n    // 屏幕变化\n    onWindowResize() {\n      this.windowInnerHeight = window.innerHeight\n      this.windowInnerWidth = window.innerWidth\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.history-container {\n  margin: 30px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);\n  padding: 24px;\n\n  /* 页面标题样式 */\n  .page-header {\n    margin-bottom: 32px;\n    text-align: center;\n\n    .page-title {\n      font-size: 2em;\n      font-weight: 700;\n      margin: 0;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n\n      i {\n        font-size: 1.2em;\n        color: #00d4ff;\n      }\n\n      .page-subtitle {\n        font-size: 0.4em;\n        color: #b8c5d1;\n        font-weight: 400;\n        margin-top: 8px;\n        letter-spacing: 1px;\n        display: block;\n      }\n    }\n  }\n}\n\n.stateImg{\n  margin-top:30px\n}\n</style>\n\n"]}]}