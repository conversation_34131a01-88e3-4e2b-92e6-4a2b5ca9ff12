
d62ee6abe60fefe96cfd01f0e0c5962f150af0b7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"479d4a9bba164a03b6073c5772a2bc02\"}","integrity":"sha512-0jSU30RyqlSia+UoYEQhpU7wSYBS57RpLa1qtJ1WtiEi8RjhpaLzQiPtrtUM8n4zSHbnM3m8QXuQOpfafQzVNA==","time":1754201768688,"size":23558}