
3eb1e9446f7f415a74254d5fabe1992babdea4f5	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"3875d722d82274ef68bec0c09a22e772\"}","integrity":"sha512-iE8TukfrysIucgiYMA6au00vwtJZfFIoREyBROBPtiiG7cvELEvanSPqdGqwvCwB4bwmVedqe/9029Qf9/fOHw==","time":1754205259464,"size":26646}