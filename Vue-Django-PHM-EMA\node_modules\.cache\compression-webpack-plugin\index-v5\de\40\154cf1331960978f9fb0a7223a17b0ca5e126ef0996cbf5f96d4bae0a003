
7327e44e81bd80bc95b67cbacaa9f9712846ed42	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"9b7b92c19218a2e1ca992cecb3f68183\"}","integrity":"sha512-PT107heaveRXtv2xNNWzGUl/tDBXWNyG6YxpyiNGniBrsXsEgZ5Rqrx5mk42avwc+qlguX+Yxm3Y1LUq1O9xgQ==","time":1754204906637,"size":108240}