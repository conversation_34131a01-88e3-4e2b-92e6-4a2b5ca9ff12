
0100bb4d9e1626442253b08ccb473595bc764833	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"efbe147b4e3738b091b292d05e2cbc00\"}","integrity":"sha512-ANv2YZC/p1elBG7o8l2puyGzQS4djt/2EQRmIggis6UeLEDatgrLAcDcOvzPrOFop4n4M9YqfeA37vFW3166iA==","time":1754203664297,"size":113335}