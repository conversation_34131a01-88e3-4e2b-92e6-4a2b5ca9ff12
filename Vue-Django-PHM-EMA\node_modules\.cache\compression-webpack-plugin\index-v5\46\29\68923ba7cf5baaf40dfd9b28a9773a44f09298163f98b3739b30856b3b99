
d5b29a7d46aa0781a26e438f51fc0749ba4f2c23	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.febcc0f830ad9d2b2134.hot-update.js\",\"contentHash\":\"682be3154843f04f1456581f279aaa0e\"}","integrity":"sha512-Rjmd4BEypoJeSFGAEPX2Db7PGmqF4P64Fv3JxRsy52b76WgnwsN7KIWHQ7/qK9+zjOqdxLqcgkSKAs2g0qmwbA==","time":1754204860475,"size":26362}