
b4828f20a105ffca32411764d3352b7ba0908d03	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"fdc2f53b786fa036cddd730600c06fb0\"}","integrity":"sha512-kxGYQyzvd7P9n9lcrsYrjG62SltWvjCB5zbv9akO7cv8rlnZtSjvzq5D13WmCiJjFIsk1M1d27mfhHDIHbQsBA==","time":1754201111758,"size":383929}