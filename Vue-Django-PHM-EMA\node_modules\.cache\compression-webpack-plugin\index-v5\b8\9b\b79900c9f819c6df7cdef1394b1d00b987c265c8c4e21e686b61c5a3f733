
a0f60ffb5aa3b711e9ed39304f1b6fd69a7c6833	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"5.a56cf2fcb7f3f366770e.hot-update.js\",\"contentHash\":\"5e37107d67e6bcadd4ca83d067dad212\"}","integrity":"sha512-1YvKbiXP1F+o8WVd35AGP51srGQoizN8vc12BXGwyduYWJ3LsfMkH1nr8snr2+ZjJHZjo0WESuMWL1IiE86OuA==","time":1754206072113,"size":12127}