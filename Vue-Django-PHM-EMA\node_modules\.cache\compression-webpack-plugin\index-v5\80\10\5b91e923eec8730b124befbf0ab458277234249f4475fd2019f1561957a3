
71b32935535f67a4552918c9ded7449b5d2c0e95	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.7c50275df76515c960f9.hot-update.js\",\"contentHash\":\"d3186e704e952a70c1ba2be9b7e5628c\"}","integrity":"sha512-R4fjAERgy2q6T7wzr2f4RzRm5OvR+HkdqmasHS89k6k/AbSZuWPpX7x2teT9zISPpnOJsOdV3T71eI2virS7Mw==","time":1754205117402,"size":39332}