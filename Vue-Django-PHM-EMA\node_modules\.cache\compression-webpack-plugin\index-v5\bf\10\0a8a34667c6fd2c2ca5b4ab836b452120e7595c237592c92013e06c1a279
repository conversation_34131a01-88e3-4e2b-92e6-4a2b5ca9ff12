
74bac23f322c312fe2b499b1a450544be27cc636	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"73a6bfbd4a0d1aa342b000f7a5c6316c\"}","integrity":"sha512-kXRFKc2ZyFAPmcnOMAii9kDv4a4wfZ6ozJ4/nlW+7tnclrz64J2x1Xh/Wplyt8VF6nbGJGsJsWdmc4Rw4QyxKw==","time":1754204764920,"size":23563}