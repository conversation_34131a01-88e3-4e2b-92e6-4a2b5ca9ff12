
064fa5444e3bcb0dc8d95f9256d31e8bb3447bc5	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"d31f918496bf4999def6e8260eead391\"}","integrity":"sha512-+ERlCJ/GaRrDzXXvuBuxhrjHa2c/9lQquGjgJnTsLw7QgyFZ2FuN6LBJAy7d99gzM9ZiQQ8T3O0ZOYuf/JI2ug==","time":1754201792795,"size":23476}