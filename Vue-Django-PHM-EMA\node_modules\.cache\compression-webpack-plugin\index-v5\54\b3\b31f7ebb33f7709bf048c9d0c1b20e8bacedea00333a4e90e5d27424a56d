
bcf9dbc98e2389753a28163e26b9f084aab360ee	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F3.js\",\"contentHash\":\"5f6a438a45381a9dff763ada491642d0\"}","integrity":"sha512-eq+OOP/r1j2e7bDd3W1QbKybRdBbDoY/2KcZcnPaomhKCN9KrVa+UC8y9DqBAsbHpjsABn79PDcB/5nB0jCD7A==","time":1754201768716,"size":110356}