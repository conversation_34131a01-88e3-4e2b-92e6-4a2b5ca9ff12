
0eaad4c0bca0eff14959aeb7ddd575c925ac374e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"f3e9cc76a1bb53c6dec6bb4b1ace66a8\"}","integrity":"sha512-oaW6bGQyENHZiFj49cGpcsmL+IwbgtYBa5tNF0E8dqL1l+buwh6wMg89rG0FyWbZBQoRacht5oXsIfus0zdgew==","time":1754203679043,"size":149037}