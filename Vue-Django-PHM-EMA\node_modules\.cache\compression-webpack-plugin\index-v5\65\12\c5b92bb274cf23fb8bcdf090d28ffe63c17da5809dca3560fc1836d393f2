
9927f7791525c41bc64dda775ada33e662adfe04	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"0.652386ff57efb9b7268c.hot-update.js\",\"contentHash\":\"6f04792e8f16ac04aaea451652c8096c\"}","integrity":"sha512-PpgWYvUctAvTWwLjjOaLlEKRsoNGCqnsUzBIYH9KmQ49r0hPkdeGEtS1bF4PZ3BYDinNSF2NllD2l/V7Hn1sXA==","time":1754201014458,"size":121412}