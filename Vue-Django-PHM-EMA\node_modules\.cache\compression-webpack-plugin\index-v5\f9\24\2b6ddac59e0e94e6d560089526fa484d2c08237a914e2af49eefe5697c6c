
6968fcac78a78d40b33a2e63fe26633d97ebdb73	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"9de1e1dc8c39ea8911e8af7eefe313ef\"}","integrity":"sha512-EWvCxTSHfC00LOr7JXFixc2IJknvg7jPHK9ngNHoCJu2ITERRRBmHLwK5+4OM/ox8I1xdEj3vR5a8Ny0rgW3aw==","time":1754200994082,"size":26653}