
a5521fdaa894f9bb0b735c3d1f2a5120a69f2b5c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.e50b7f4aab9e1fa7e9d3.hot-update.js\",\"contentHash\":\"461b8123740d2d365aa4488aacae74d1\"}","integrity":"sha512-1D93VijzX6omYMBt/ABBXV/VqkD+m39/223nx8tqsaUQxES2TkpNlo6dQCFAHkGHhNU2ReJ2QORteZhNtaDWvA==","time":1754203211969,"size":21346}