
d77b8b59f738b57bbfd7b1c5e48af219c92bf31a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"7ec5e6059a6a377eac051f7138bddce6\"}","integrity":"sha512-NT9HDAQeG5jOnKY+BV4VADZpAmmHYydX0fadc933Qj1WpPpY7UB2EWm1peinBYnJ2lO9wi1n0vWf8bx0CKxBbw==","time":1754202893532,"size":23561}