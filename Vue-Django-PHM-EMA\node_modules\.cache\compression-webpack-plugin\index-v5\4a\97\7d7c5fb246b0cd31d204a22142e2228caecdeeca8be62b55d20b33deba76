
53cedb2bae594c567c7f480a55600b9b43056c7a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"532a4c009a1383035729163d3f9edd79\"}","integrity":"sha512-INe+4gSX9NKA2od91/t8Y7rjgJEpJHdz/3kgKfT40domlez8blHX185KYvqMX4LrhmF/q2M55eyCbJJluIBFAg==","time":1754205133152,"size":87800}