
f4c6e34ca4fc84dae8dfca1204b6e43a95bf83b2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"5.56cc3b7105f6ffde936d.hot-update.js\",\"contentHash\":\"35dcf0d14faee293810c9de36f4527dc\"}","integrity":"sha512-4rvMfEySdj0HcTOoQR6PEs+kUtuEdkspu2ZPPuUn/0ODh1uuRTM1pjobkBzvPP19306TKcuQvdDPuEVLffoYbw==","time":1754206056150,"size":69117}