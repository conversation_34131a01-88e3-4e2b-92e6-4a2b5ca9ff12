
d7d533a144b8a22ebf72da6208ffd50406c6c47c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"aa32f56e69d7962d90f6020149864c75\"}","integrity":"sha512-YXvPLpOSBNlUEWeImilhx5DbazV0M/r+gcf57ogbyi1OwOX0DBpJtjnUyjIz9S0vhMOEbCEOQLfknCcNSYy3Eg==","time":1754202857644,"size":872779}