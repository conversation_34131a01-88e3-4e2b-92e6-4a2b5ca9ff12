
ed6df02c26cb47035e55dd6b6b7106b459e4286a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"9e9bef438a349c3f21f3d1b167dd7e38\"}","integrity":"sha512-xjHo/runb8qYEWFQAZzIWuuFhXrf1I6xBgUZXD6Hpj9pQxkF7M2yGccF5ASlcc//Ha2AYuFjI1PKsUCjwoG1Ng==","time":1754204007018,"size":115283}