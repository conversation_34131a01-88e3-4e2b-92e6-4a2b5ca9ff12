
c197db2e43765d4ca6cf21c2dad31fd805fbd684	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.e50b7f4aab9e1fa7e9d3.hot-update.js\",\"contentHash\":\"461b8123740d2d365aa4488aacae74d1\"}","integrity":"sha512-jsSYMDfExF9gp7id+z9SNqVY/GoAaSXe7tCqyGyyHZHvQfqxLn6nxFJU82hUD8d5JXEiJ26c4UjBkFTg2FDN8g==","time":1754203211599,"size":24187}