
d1646a9b846929c3c07618598d0a4f9b74cd8bbb	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"e449b9dd78c33a3c983e06ec764d6244\"}","integrity":"sha512-Ym9EVaNs0ad2jYCvAvLvY5WfhRoVWQcGHCSuYbxQ8NdEmukQcOVyWJDSQ3MjPFYz1JddWy8Ol4MwEwMnLvZ8nw==","time":1754206042143,"size":23493}