
0e2c4198ba61555294d083addbddb59edfcd119a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.12e5083a5e90408b16f9.hot-update.js\",\"contentHash\":\"ff05f47a5693826327840e3efed702c8\"}","integrity":"sha512-Bp8TlCG/LnKehBHPCjGb1lvabaSx2NuLmAJ3FAm3I6nYWZQIj1LC+PFZh+MQBS6irPQBDpi87eGAGYawlSe7Zw==","time":1754204815577,"size":11163}