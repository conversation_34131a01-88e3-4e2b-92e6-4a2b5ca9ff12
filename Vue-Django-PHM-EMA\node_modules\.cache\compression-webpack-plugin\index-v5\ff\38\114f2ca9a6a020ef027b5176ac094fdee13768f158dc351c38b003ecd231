
2542db3ce76ed3365018fb202ee9635e35b69792	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.8cd52fc88e0b30b72cee.hot-update.js\",\"contentHash\":\"9e98ccf5ff8f40fbf2796bb43a9dc56c\"}","integrity":"sha512-VmDXs830wWPbcD/mVKm+1piRXoxGdW5NKK9SPXbLS1yn05/zkVDsXMWAoyxPyfi5ItqXu6dsXjyCUVOI3kcGxA==","time":1754203186095,"size":108211}