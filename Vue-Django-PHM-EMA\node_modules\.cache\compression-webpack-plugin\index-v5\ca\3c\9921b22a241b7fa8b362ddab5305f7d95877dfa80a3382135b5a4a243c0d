
65fc63b2bb305e44ff4a2f086cd25e18fe441817	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.5e5c89c825d67e86668f.hot-update.js\",\"contentHash\":\"c0c1fcde1654af0f5635e9e5b9f53811\"}","integrity":"sha512-ZeQ2xoSB3e97LMXS5ROALA+p091InhvWDGImnqxA+8x/CMXVX0BjB8xkwv7K7nbKy+6FkFP4UQr1T/CIBTkJ8w==","time":1754202942250,"size":21791}