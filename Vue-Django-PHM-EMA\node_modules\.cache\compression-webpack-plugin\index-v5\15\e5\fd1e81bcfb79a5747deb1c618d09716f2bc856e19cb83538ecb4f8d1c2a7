
b3bcab8a5ca8b08b330c0f3688ebc0cb59c7a73b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"82d1d7de942b0e455810fc6f09694a72\"}","integrity":"sha512-lYCvHGhdvI9WOfDf9r7yHjhNvUmXDoIiE2LN+AP+oawYg6DWMIiwZRXLk4uV5wQTOrC8tE+EaHLxgLoGe5GkUQ==","time":1754205424531,"size":103844}