
066d3657c86fd2a38141ad9628b5128bb83ccfeb	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"871e99a2ec8a98990a474e9bedda1a4a\"}","integrity":"sha512-ilMFCc45HwU/mkFvvN6YJ6Kx47QzFO8W9NVQuJJPLhz59qyhmHKtzzqFc37i59aI3KWJ6NAKDblHBSBvecBUhQ==","time":1754204941487,"size":23548}