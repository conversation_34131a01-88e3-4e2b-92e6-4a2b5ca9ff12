
efffd4cca3a9b579c94f1eb70648c86b5332d6af	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"1918c0e4c960f805e07f189b7f84f4ae\"}","integrity":"sha512-WuFtk2LjxaOJH3++N5UUGGmMwDpBXxE1HKqaO6n44ZQMrgJpR5CE28Rn8fSTpr8LArCEsfFbsJBYhzMIgEyKig==","time":1754203211600,"size":26790}