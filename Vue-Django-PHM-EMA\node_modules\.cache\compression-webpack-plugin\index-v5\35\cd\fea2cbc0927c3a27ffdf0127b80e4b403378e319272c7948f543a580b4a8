
8408011e3d5de9130bd7abd33934ba8cdab9cbec	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"0.52bf6c6f01e08e80b507.hot-update.js\",\"contentHash\":\"52cd56e105f77c20670ce14c23be3478\"}","integrity":"sha512-EbgDdzQ2uEvleH+y/T2FKdHr5LKmgbFsU6rcOiqqSAxWw0aOulUenr26Mmdn8/d1oEhjbJwqNjHJtDqisGfI+w==","time":1754201111687,"size":34594}