
de0b26b0edea7990eb5db790393f19860c52af14	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"0bf5aeea5253d50df54ba6bf0af7e345\"}","integrity":"sha512-iMNHyt5qdb2l/hoUs0z91VBzUzrhO1LIpcVDk5x+XHeeoQcOjICja3x59aPS8y6v1FOTS4IYQR7rNfGQCBdlpg==","time":1754202893182,"size":138437}