
be2e29377175fd826277c07c19f639e1d4597a2b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"bc9055fe9fd68411bcaa841fbbd58079\"}","integrity":"sha512-6JxXDg5KMG+fZXdvj46pZieqMhZrcqHMLgeyQK0j7RhOKrrZ1Qx0KYL9fL+WxFOaNtiUQyTARRcm6f6np0XTuw==","time":1754203859465,"size":23449}