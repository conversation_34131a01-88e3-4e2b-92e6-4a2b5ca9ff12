
0cf0b3e8456bcd162c2908b7f0b39b11509479e6	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"deafeb1d881060d60ca47defaf903e18\"}","integrity":"sha512-MYrzlDgTLHfTc2u3ml7f/HpD8Ok5gVjJKNw5NBcZ2cCllqJ18DhM1A8EPcCSgV14EZNKs9m7GvWGFvfGGtcEjA==","time":1754200994965,"size":274392}