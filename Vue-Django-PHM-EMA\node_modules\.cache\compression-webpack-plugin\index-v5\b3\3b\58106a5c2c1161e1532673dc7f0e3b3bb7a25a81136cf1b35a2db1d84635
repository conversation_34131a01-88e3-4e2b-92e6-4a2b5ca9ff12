
bda324261f95301159d1f35f3246c9dfa7edb6d9	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F2.js\",\"contentHash\":\"c66d54b33b9da3aff5d42dfc423a2d70\"}","integrity":"sha512-H0ezOI+TYanbkD16dcoZ3NlVZb9iK0F/CagbULiGholA7/L4atmX20+EUNToSz/e7wd14svNPvbS8FTq1Q4APw==","time":1754206041787,"size":183260}