
96b297457819760f7c8c1f3bca94566a90c71367	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"0.69db7f9c81fbd9e43607.hot-update.js\",\"contentHash\":\"be6b2a12bc1a433e7ea45b53445dd1ab\"}","integrity":"sha512-aiYqqSo7FfcWs2d54H5c816qiPkC7cq2pEdk8yVQEQHbXVsf4AAd3CpwPZcGzio9+w+gRpht8Vo1z0IlU6n9bQ==","time":1754200970263,"size":115666}