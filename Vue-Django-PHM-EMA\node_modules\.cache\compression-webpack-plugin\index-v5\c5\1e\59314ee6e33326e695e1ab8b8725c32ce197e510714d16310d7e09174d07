
ae421ce41c8e89ceb0e096b8bc0441c52207edd8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"7c8c345339707a5697fa73e82c3a34e1\"}","integrity":"sha512-9TN8bLtLc+LTvndL+Hv5rAS2edqLTAsY55s0qTmvxTrlwhMJUm+mJPUCFWhzF2CKEckJI5+JBBG6plLTxXjRFw==","time":1754204006981,"size":23564}