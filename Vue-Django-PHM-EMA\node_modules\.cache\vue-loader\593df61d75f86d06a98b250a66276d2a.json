{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\sensor-monitor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\sensor-monitor\\index.vue", "mtime": 1754202095623}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBTZW5zb3JDaGFydCBmcm9tICcuL2NvbXBvbmVudHMvU2Vuc29yQ2hhcnQnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0RldmljZVN0YXR1c01vbml0b3InLAogIGNvbXBvbmVudHM6IHsKICAgIFNlbnNvckNoYXJ0CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2Vuc29yVHlwZXM6IFtdLAogICAgICBzZWxlY3RlZFNlbnNvclR5cGU6ICcnLAogICAgICBpc0NvbGxlY3Rpbmc6IGZhbHNlLAogICAgICBjaGFydERhdGE6IHsKICAgICAgICB0aW1lc3RhbXBzOiBbXSwKICAgICAgICB2YWx1ZXM6IFtdCiAgICAgIH0sCiAgICAgIGRhdGFGZXRjaFRpbWVyOiBudWxsLAogICAgICBtYXhEYXRhUG9pbnRzOiA2MCwgLy8g5pyA5aSa5pi+56S6NjDkuKrmlbDmja7ngrkKICAgICAgaGFzRGF0YTogZmFsc2UsCiAgICAgIHNhbXBsaW5nUmF0ZXM6IFsKICAgICAgICB7IHZhbHVlOiA1MCwgbGFiZWw6ICcyMEh6ICg1MG1zKScgfSwKICAgICAgICB7IHZhbHVlOiAxMDAsIGxhYmVsOiAnMTBIeiAoMTAwbXMpJyB9LAogICAgICAgIHsgdmFsdWU6IDIwMCwgbGFiZWw6ICc1SHogKDIwMG1zKScgfSwKICAgICAgICB7IHZhbHVlOiA1MDAsIGxhYmVsOiAnMkh6ICg1MDBtcyknIH0sCiAgICAgICAgeyB2YWx1ZTogMTAwMCwgbGFiZWw6ICcxSHogKDEwMDBtcyknIH0KICAgICAgXSwKICAgICAgc2VsZWN0ZWRTYW1wbGluZ1JhdGU6IDEwMCAvLyDpu5jorqQxMEh6ICgxMDBtcykKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDlvZPliY3lgLwKICAgIGN1cnJlbnRWYWx1ZSgpIHsKICAgICAgaWYgKHRoaXMuY2hhcnREYXRhLnZhbHVlcy5sZW5ndGggPT09IDApIHJldHVybiAnLS0nCiAgICAgIHJldHVybiB0aGlzLmNoYXJ0RGF0YS52YWx1ZXNbdGhpcy5jaGFydERhdGEudmFsdWVzLmxlbmd0aCAtIDFdLnRvRml4ZWQoMykKICAgIH0sCgogICAgLy8g5bmz5Z2H5YC8CiAgICBhdmVyYWdlVmFsdWUoKSB7CiAgICAgIGlmICh0aGlzLmNoYXJ0RGF0YS52YWx1ZXMubGVuZ3RoID09PSAwKSByZXR1cm4gJy0tJwogICAgICBjb25zdCBzdW0gPSB0aGlzLmNoYXJ0RGF0YS52YWx1ZXMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCkKICAgICAgcmV0dXJuIChzdW0gLyB0aGlzLmNoYXJ0RGF0YS52YWx1ZXMubGVuZ3RoKS50b0ZpeGVkKDMpCiAgICB9LAoKICAgIC8vIOacgOWkp+WAvAogICAgbWF4VmFsdWUoKSB7CiAgICAgIGlmICh0aGlzLmNoYXJ0RGF0YS52YWx1ZXMubGVuZ3RoID09PSAwKSByZXR1cm4gJy0tJwogICAgICByZXR1cm4gTWF0aC5tYXgoLi4udGhpcy5jaGFydERhdGEudmFsdWVzKS50b0ZpeGVkKDMpCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5mZXRjaFNlbnNvclR5cGVzKCkKICB9LAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLmNsZWFyVGltZXIoKQogIH0sCiAgbWV0aG9kczogewogICAgLy8g6I635Y+W5Lyg5oSf5Zmo57G75Z6L5YiX6KGoCiAgICBmZXRjaFNlbnNvclR5cGVzKCkgewogICAgICB0aGlzLiRheGlvcy5nZXQoJy9waG0vZ2V0X3NlbnNvcl90eXBlcy8nKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuc2Vuc29yVHlwZXMgPSByZXNwb25zZS5kYXRhLmRhdGEuc2Vuc29yX3R5cGVzCiAgICAgICAgICBpZiAodGhpcy5zZW5zb3JUeXBlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRTZW5zb3JUeXBlID0gdGhpcy5zZW5zb3JUeXBlc1swXQogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bkvKDmhJ/lmajnsbvlnovlpLHotKU6ICcgKyByZXNwb25zZS5kYXRhLm1zZykKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bkvKDmhJ/lmajnsbvlnovlh7rplJk6ICcgKyBlcnJvci5tZXNzYWdlKQogICAgICB9KQogICAgfSwKCiAgICAvLyDlvIDlp4vmlbDmja7ph4fpm4YKICAgIHN0YXJ0Q29sbGVjdGlvbigpIHsKICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkU2Vuc29yVHlwZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup5Lyg5oSf5Zmo57G75Z6LJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g6YeN572u5Zu+6KGo5pWw5o2uCiAgICAgIHRoaXMuY2hhcnREYXRhID0gewogICAgICAgIHRpbWVzdGFtcHM6IFtdLAogICAgICAgIHZhbHVlczogW10KICAgICAgfQoKICAgICAgLy8g5Y+R6YCB5byA5aeL6YeH6ZuG6K+35rGCCiAgICAgIHRoaXMuJGF4aW9zLnBvc3QoJy9waG0vc3RhcnRfZGF0YV9jb2xsZWN0aW9uLycsIHsKICAgICAgICBzZW5zb3JfdHlwZTogdGhpcy5zZWxlY3RlZFNlbnNvclR5cGUsCiAgICAgICAgc2FtcGxpbmdfcmF0ZTogdGhpcy5zZWxlY3RlZFNhbXBsaW5nUmF0ZQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuaXNDb2xsZWN0aW5nID0gdHJ1ZQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflvIDlp4vph4fpm4bmlbDmja4nKQogICAgICAgICAgdGhpcy5zdGFydERhdGFGZXRjaGluZygpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+W8gOWni+mHh+mbhuWksei0pTogJyArIHJlc3BvbnNlLmRhdGEubXNnKQogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+W8gOWni+mHh+mbhuWHuumUmTogJyArIGVycm9yLm1lc3NhZ2UpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOWBnOatouaVsOaNrumHh+mbhgogICAgc3RvcENvbGxlY3Rpb24oKSB7CiAgICAgIHRoaXMuJGF4aW9zLnBvc3QoJy9waG0vc3RvcF9kYXRhX2NvbGxlY3Rpb24vJykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLmlzQ29sbGVjdGluZyA9IGZhbHNlCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WBnOatoumHh+mbhuaVsOaNricpCiAgICAgICAgICB0aGlzLmNsZWFyVGltZXIoKQogICAgICAgICAgdGhpcy5oYXNEYXRhID0gdGhpcy5jaGFydERhdGEudmFsdWVzLmxlbmd0aCA+IDAKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5YGc5q2i6YeH6ZuG5aSx6LSlOiAnICsgcmVzcG9uc2UuZGF0YS5tc2cpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5YGc5q2i6YeH6ZuG5Ye66ZSZOiAnICsgZXJyb3IubWVzc2FnZSkKICAgICAgfSkKICAgIH0sCgogICAgLy8g5byA5aeL5a6a5pe26I635Y+W5pWw5o2uCiAgICBzdGFydERhdGFGZXRjaGluZygpIHsKICAgICAgdGhpcy5jbGVhclRpbWVyKCkKICAgICAgdGhpcy5kYXRhRmV0Y2hUaW1lciA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgICB0aGlzLmZldGNoU2Vuc29yRGF0YSgpCiAgICAgIH0sIHRoaXMuc2VsZWN0ZWRTYW1wbGluZ1JhdGUpIC8vIOS9v+eUqOmAieWumueahOmHh+agt+mXtOmalAogICAgfSwKCiAgICAvLyDojrflj5bkvKDmhJ/lmajmlbDmja4KICAgIGZldGNoU2Vuc29yRGF0YSgpIHsKICAgICAgdGhpcy4kYXhpb3MuZ2V0KCcvcGhtL2dldF9zaW11bGF0ZWRfZGF0YS8nKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhLmRhdGEKCiAgICAgICAgICBpZiAoZGF0YS5zdGF0dXMgPT09ICdjb2xsZWN0aW5nJykgewogICAgICAgICAgICAvLyDmt7vliqDmlrDmlbDmja7ngrkKICAgICAgICAgICAgdGhpcy5jaGFydERhdGEudGltZXN0YW1wcy5wdXNoKC4uLmRhdGEudGltZXN0YW1wcykKICAgICAgICAgICAgdGhpcy5jaGFydERhdGEudmFsdWVzLnB1c2goLi4uZGF0YS52YWx1ZXMpCgogICAgICAgICAgICAvLyDpmZDliLbmlbDmja7ngrnmlbDph48KICAgICAgICAgICAgaWYgKHRoaXMuY2hhcnREYXRhLnRpbWVzdGFtcHMubGVuZ3RoID4gdGhpcy5tYXhEYXRhUG9pbnRzKSB7CiAgICAgICAgICAgICAgY29uc3QgZXhjZXNzID0gdGhpcy5jaGFydERhdGEudGltZXN0YW1wcy5sZW5ndGggLSB0aGlzLm1heERhdGFQb2ludHMKICAgICAgICAgICAgICB0aGlzLmNoYXJ0RGF0YS50aW1lc3RhbXBzLnNwbGljZSgwLCBleGNlc3MpCiAgICAgICAgICAgICAgdGhpcy5jaGFydERhdGEudmFsdWVzLnNwbGljZSgwLCBleGNlc3MpCiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOabtOaWsOWbvuihqAogICAgICAgICAgICBpZiAodGhpcy4kcmVmcy5zZW5zb3JDaGFydCkgewogICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2Vuc29yQ2hhcnQudXBkYXRlQ2hhcnQoKQogICAgICAgICAgICB9CgogICAgICAgICAgICB0aGlzLmhhc0RhdGEgPSB0cnVlCgogICAgICAgICAgICAvLyDoh6rliqjkv53lrZjmlbDmja7liLDljoblj7LorrDlvZUKICAgICAgICAgICAgdGhpcy5zYXZlRGF0YVRvSGlzdG9yeShkYXRhLnRpbWVzdGFtcHMsIGRhdGEudmFsdWVzKQogICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnN0YXR1cyA9PT0gJ25vdF9jb2xsZWN0aW5nJykgewogICAgICAgICAgICAvLyDlpoLmnpzmnI3liqHlmajnq6/lgZzmraLkuobph4fpm4bvvIzlrqLmiLfnq6/kuZ/lupTlgZzmraIKICAgICAgICAgICAgaWYgKHRoaXMuaXNDb2xsZWN0aW5nKSB7CiAgICAgICAgICAgICAgdGhpcy5pc0NvbGxlY3RpbmcgPSBmYWxzZQogICAgICAgICAgICAgIHRoaXMuY2xlYXJUaW1lcigpCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmnI3liqHlmajlt7LlgZzmraLmlbDmja7ph4fpm4YnKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaVsOaNruWksei0pTogJyArIHJlc3BvbnNlLmRhdGEubXNnKQogICAgICAgICAgdGhpcy5jbGVhclRpbWVyKCkKICAgICAgICAgIHRoaXMuaXNDb2xsZWN0aW5nID0gZmFsc2UKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmlbDmja7lh7rplJk6ICcgKyBlcnJvci5tZXNzYWdlKQogICAgICAgIHRoaXMuY2xlYXJUaW1lcigpCiAgICAgICAgdGhpcy5pc0NvbGxlY3RpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKCiAgICAvLyDkv53lrZjmlbDmja7liLDljoblj7LorrDlvZUKICAgIHNhdmVEYXRhVG9IaXN0b3J5KHRpbWVzdGFtcHMsIHZhbHVlcykgewogICAgICBpZiAoIXRpbWVzdGFtcHMgfHwgIXZhbHVlcyB8fCB0aW1lc3RhbXBzLmxlbmd0aCA9PT0gMCB8fCB2YWx1ZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOavj+asoeWPquWPkemAgeacgOaWsOeahOaVsOaNrueCuQogICAgICBjb25zdCBsYXRlc3RJbmRleCA9IHRpbWVzdGFtcHMubGVuZ3RoIC0gMQoKICAgICAgLy8g5p6E6YCg5L+d5a2Y5pWw5o2u55qE6K+35rGCCiAgICAgIHRoaXMuJGF4aW9zLnBvc3QoJy9waG0vc2F2ZU1vbml0b3JEYXRhLycsIHsKICAgICAgICB0aW1lc3RhbXA6IHRpbWVzdGFtcHNbbGF0ZXN0SW5kZXhdLAogICAgICAgIHZhbHVlOiB2YWx1ZXNbbGF0ZXN0SW5kZXhdLAogICAgICAgIHNlbnNvcl90eXBlOiB0aGlzLnNlbGVjdGVkU2Vuc29yVHlwZSwKICAgICAgICBzdGF0dXM6ICcwJywgLy8g6buY6K6k54q25oCB5Li65q2j5bi4CiAgICAgICAgaGVhbHRoX3N0YXR1czogJzEnIC8vIOm7mOiupOWBpeW6t+eKtuaAgeS4uuato+W4uAogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5jb2RlICE9PSAyMDApIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOebkea1i+aVsOaNruWksei0pTonLCByZXNwb25zZS5kYXRhLm1zZykKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmVycm9yKCfkv53lrZjnm5HmtYvmlbDmja7lh7rplJk6JywgZXJyb3IpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOa4hemZpOWumuaXtuWZqAogICAgY2xlYXJUaW1lcigpIHsKICAgICAgaWYgKHRoaXMuZGF0YUZldGNoVGltZXIpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMuZGF0YUZldGNoVGltZXIpCiAgICAgICAgdGhpcy5kYXRhRmV0Y2hUaW1lciA9IG51bGwKICAgICAgfQogICAgfSwKCiAgICAvLyDkuIvovb3mlbDmja4KICAgIGRvd25sb2FkRGF0YSgpIHsKICAgICAgaWYgKCF0aGlzLmhhc0RhdGEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieWPr+S4i+i9veeahOaVsOaNricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuJGF4aW9zLnBvc3QoJy9waG0vZG93bmxvYWRfc2Vuc29yX2RhdGEvJywgewogICAgICAgIHNlbnNvcl90eXBlOiB0aGlzLnNlbGVjdGVkU2Vuc29yVHlwZSwKICAgICAgICB2YWx1ZXM6IHRoaXMuY2hhcnREYXRhLnZhbHVlcywKICAgICAgICB0aW1lc3RhbXBzOiB0aGlzLmNoYXJ0RGF0YS50aW1lc3RhbXBzCiAgICAgIH0sIHsKICAgICAgICByZXNwb25zZVR5cGU6ICdibG9iJyAvLyDmjIflrprlk43lupTnsbvlnovkuLrkuozov5vliLbmlbDmja4KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgLy8g5Yib5bu65LiL6L296ZO+5o6lCiAgICAgICAgY29uc3QgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwobmV3IEJsb2IoW3Jlc3BvbnNlLmRhdGFdKSkKICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpCiAgICAgICAgbGluay5ocmVmID0gdXJsCiAgICAgICAgbGluay5zZXRBdHRyaWJ1dGUoJ2Rvd25sb2FkJywgYCR7dGhpcy5zZWxlY3RlZFNlbnNvclR5cGV9X2RhdGFfJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkucmVwbGFjZSgvWzouXS9nLCAnXycpfS54bHN4YCkKICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspCiAgICAgICAgbGluay5jbGljaygpCiAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQogICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKHVybCkKICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4i+i9veaVsOaNruWHuumUmTogJyArIGVycm9yLm1lc3NhZ2UpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOiOt+WPluS8oOaEn+WZqOexu+Wei+eahOaYvuekuuagh+etvgogICAgZ2V0U2Vuc29yVHlwZUxhYmVsKHR5cGUpIHsKICAgICAgY29uc3QgbGFiZWxzID0gewogICAgICAgICdwb3NpdGlvbic6ICfkvY3nva7kvKDmhJ/lmagnLAogICAgICAgICdjdXJyZW50JzogJ+eUtea1geS8oOaEn+WZqCcsCiAgICAgICAgJ3NldHBvaW50JzogJ+aMh+S7pOS9jeenu+S8oOaEn+WZqCcKICAgICAgfQogICAgICByZXR1cm4gbGFiZWxzW3R5cGVdIHx8IHR5cGUKICAgIH0sCgogICAgLy8g6I635Y+W5b2T5YmN6YCJ5oup55qE6YeH5qC36aKR546H5qCH562+CiAgICBnZXRTZWxlY3RlZFNhbXBsaW5nUmF0ZUxhYmVsKCkgewogICAgICBjb25zdCByYXRlID0gdGhpcy5zYW1wbGluZ1JhdGVzLmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09PSB0aGlzLnNlbGVjdGVkU2FtcGxpbmdSYXRlKQogICAgICByZXR1cm4gcmF0ZSA/IHJhdGUubGFiZWwgOiBgJHt0aGlzLnNlbGVjdGVkU2FtcGxpbmdSYXRlfW1zYAogICAgfSwKCiAgICAvLyDojrflj5bkvKDmhJ/lmajljZXkvY0KICAgIGdldFVuaXQodHlwZSkgewogICAgICBjb25zdCB1bml0cyA9IHsKICAgICAgICAncG9zaXRpb24nOiAnbW0nLAogICAgICAgICdjdXJyZW50JzogJ0EnLAogICAgICAgICdzZXRwb2ludCc6ICdtbScKICAgICAgfQogICAgICByZXR1cm4gdW5pdHNbdHlwZV0gfHwgJycKICAgIH0sCgogICAgLy8g5riF6Zmk5a6a5pe25ZmoCiAgICBjbGVhclRpbWVyKCkgewogICAgICBpZiAodGhpcy5kYXRhRmV0Y2hUaW1lcikgewogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5kYXRhRmV0Y2hUaW1lcikKICAgICAgICB0aGlzLmRhdGFGZXRjaFRpbWVyID0gbnVsbAogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkLA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/sensor-monitor", "sourcesContent": ["<template>\n  <div class=\"sensor-monitor-container modern-theme\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-monitor\"></i>\n        设备状态监测\n        <span class=\"page-subtitle\">Real-time Device Status Monitoring</span>\n      </h2>\n    </div>\n\n    <!-- 控制面板 -->\n    <div class=\"control-panel modern-card\">\n      <div class=\"card-header\">\n        <h3 class=\"card-title\">监测控制台</h3>\n        <div class=\"status-indicator\" :class=\"isCollecting ? 'online' : 'offline'\">\n          {{ isCollecting ? '采集中' : '待机' }}\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"6\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">传感器类型</label>\n              <el-select\n                v-model=\"selectedSensorType\"\n                :disabled=\"isCollecting\"\n                placeholder=\"请选择传感器类型\"\n                style=\"width: 100%\"\n                class=\"modern-select\"\n              >\n                <el-option\n                  v-for=\"item in sensorTypes\"\n                  :key=\"item\"\n                  :label=\"getSensorTypeLabel(item)\"\n                  :value=\"item\"\n                >\n                </el-option>\n              </el-select>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">采样频率</label>\n              <el-select\n                v-model=\"selectedSamplingRate\"\n                :disabled=\"isCollecting\"\n                placeholder=\"请选择采样频率\"\n                style=\"width: 100%\"\n                class=\"modern-select\"\n              >\n                <el-option\n                  v-for=\"item in samplingRates\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">操作控制</label>\n              <div class=\"button-group\">\n                <el-button\n                  type=\"primary\"\n                  :disabled=\"isCollecting || !selectedSensorType\"\n                  @click=\"startCollection\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-video-play\"></i>\n                  开始采集\n                </el-button>\n                <el-button\n                  type=\"danger\"\n                  :disabled=\"!isCollecting\"\n                  @click=\"stopCollection\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-video-pause\"></i>\n                  停止采集\n                </el-button>\n                <el-button\n                  type=\"success\"\n                  :disabled=\"!hasData\"\n                  @click=\"downloadData\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-download\"></i>\n                  下载数据\n                </el-button>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n    </div>\n\n    <!-- 数据展示区域 -->\n    <div class=\"data-display-area\">\n      <el-row :gutter=\"24\">\n        <!-- 实时数据指标 -->\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">当前值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ currentValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n            <div class=\"metric-chart\">\n              <!-- 迷你图表占位 -->\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">平均值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ averageValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">最大值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ maxValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">数据点数</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ chartData.values.length }}</span>\n              <span class=\"unit\">个</span>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 图表展示区域 -->\n    <div class=\"chart-container modern-card\">\n      <div class=\"card-header\">\n        <h3 class=\"card-title\">\n          <i class=\"el-icon-data-line\"></i>\n          {{ getSensorTypeLabel(selectedSensorType) }} 实时数据\n        </h3>\n        <div class=\"chart-info\">\n          <span class=\"sampling-rate\">采样频率: {{ getSelectedSamplingRateLabel() }}</span>\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"chart-wrapper\">\n          <sensor-chart\n            ref=\"sensorChart\"\n            :chart-data=\"chartData\"\n            :sensor-type=\"selectedSensorType\"\n          ></sensor-chart>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport SensorChart from './components/SensorChart'\n\nexport default {\n  name: 'DeviceStatusMonitor',\n  components: {\n    SensorChart\n  },\n  data() {\n    return {\n      sensorTypes: [],\n      selectedSensorType: '',\n      isCollecting: false,\n      chartData: {\n        timestamps: [],\n        values: []\n      },\n      dataFetchTimer: null,\n      maxDataPoints: 60, // 最多显示60个数据点\n      hasData: false,\n      samplingRates: [\n        { value: 50, label: '20Hz (50ms)' },\n        { value: 100, label: '10Hz (100ms)' },\n        { value: 200, label: '5Hz (200ms)' },\n        { value: 500, label: '2Hz (500ms)' },\n        { value: 1000, label: '1Hz (1000ms)' }\n      ],\n      selectedSamplingRate: 100 // 默认10Hz (100ms)\n    }\n  },\n  computed: {\n    // 当前值\n    currentValue() {\n      if (this.chartData.values.length === 0) return '--'\n      return this.chartData.values[this.chartData.values.length - 1].toFixed(3)\n    },\n\n    // 平均值\n    averageValue() {\n      if (this.chartData.values.length === 0) return '--'\n      const sum = this.chartData.values.reduce((a, b) => a + b, 0)\n      return (sum / this.chartData.values.length).toFixed(3)\n    },\n\n    // 最大值\n    maxValue() {\n      if (this.chartData.values.length === 0) return '--'\n      return Math.max(...this.chartData.values).toFixed(3)\n    }\n  },\n  created() {\n    this.fetchSensorTypes()\n  },\n  beforeDestroy() {\n    this.clearTimer()\n  },\n  methods: {\n    // 获取传感器类型列表\n    fetchSensorTypes() {\n      this.$axios.get('/phm/get_sensor_types/').then(response => {\n        if (response.data.code === 200) {\n          this.sensorTypes = response.data.data.sensor_types\n          if (this.sensorTypes.length > 0) {\n            this.selectedSensorType = this.sensorTypes[0]\n          }\n        } else {\n          this.$message.error('获取传感器类型失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('获取传感器类型出错: ' + error.message)\n      })\n    },\n\n    // 开始数据采集\n    startCollection() {\n      if (!this.selectedSensorType) {\n        this.$message.warning('请先选择传感器类型')\n        return\n      }\n\n      // 重置图表数据\n      this.chartData = {\n        timestamps: [],\n        values: []\n      }\n\n      // 发送开始采集请求\n      this.$axios.post('/phm/start_data_collection/', {\n        sensor_type: this.selectedSensorType,\n        sampling_rate: this.selectedSamplingRate\n      }).then(response => {\n        if (response.data.code === 200) {\n          this.isCollecting = true\n          this.$message.success('开始采集数据')\n          this.startDataFetching()\n        } else {\n          this.$message.error('开始采集失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('开始采集出错: ' + error.message)\n      })\n    },\n\n    // 停止数据采集\n    stopCollection() {\n      this.$axios.post('/phm/stop_data_collection/').then(response => {\n        if (response.data.code === 200) {\n          this.isCollecting = false\n          this.$message.success('停止采集数据')\n          this.clearTimer()\n          this.hasData = this.chartData.values.length > 0\n        } else {\n          this.$message.error('停止采集失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('停止采集出错: ' + error.message)\n      })\n    },\n\n    // 开始定时获取数据\n    startDataFetching() {\n      this.clearTimer()\n      this.dataFetchTimer = setInterval(() => {\n        this.fetchSensorData()\n      }, this.selectedSamplingRate) // 使用选定的采样间隔\n    },\n\n    // 获取传感器数据\n    fetchSensorData() {\n      this.$axios.get('/phm/get_simulated_data/').then(response => {\n        if (response.data.code === 200) {\n          const data = response.data.data\n\n          if (data.status === 'collecting') {\n            // 添加新数据点\n            this.chartData.timestamps.push(...data.timestamps)\n            this.chartData.values.push(...data.values)\n\n            // 限制数据点数量\n            if (this.chartData.timestamps.length > this.maxDataPoints) {\n              const excess = this.chartData.timestamps.length - this.maxDataPoints\n              this.chartData.timestamps.splice(0, excess)\n              this.chartData.values.splice(0, excess)\n            }\n\n            // 更新图表\n            if (this.$refs.sensorChart) {\n              this.$refs.sensorChart.updateChart()\n            }\n\n            this.hasData = true\n\n            // 自动保存数据到历史记录\n            this.saveDataToHistory(data.timestamps, data.values)\n          } else if (data.status === 'not_collecting') {\n            // 如果服务器端停止了采集，客户端也应停止\n            if (this.isCollecting) {\n              this.isCollecting = false\n              this.clearTimer()\n              this.$message.info('服务器已停止数据采集')\n            }\n          }\n        } else {\n          this.$message.error('获取数据失败: ' + response.data.msg)\n          this.clearTimer()\n          this.isCollecting = false\n        }\n      }).catch(error => {\n        this.$message.error('获取数据出错: ' + error.message)\n        this.clearTimer()\n        this.isCollecting = false\n      })\n    },\n\n    // 保存数据到历史记录\n    saveDataToHistory(timestamps, values) {\n      if (!timestamps || !values || timestamps.length === 0 || values.length === 0) {\n        return\n      }\n\n      // 每次只发送最新的数据点\n      const latestIndex = timestamps.length - 1\n\n      // 构造保存数据的请求\n      this.$axios.post('/phm/saveMonitorData/', {\n        timestamp: timestamps[latestIndex],\n        value: values[latestIndex],\n        sensor_type: this.selectedSensorType,\n        status: '0', // 默认状态为正常\n        health_status: '1' // 默认健康状态为正常\n      }).then(response => {\n        if (response.data.code !== 200) {\n          console.error('保存监测数据失败:', response.data.msg)\n        }\n      }).catch(error => {\n        console.error('保存监测数据出错:', error)\n      })\n    },\n\n    // 清除定时器\n    clearTimer() {\n      if (this.dataFetchTimer) {\n        clearInterval(this.dataFetchTimer)\n        this.dataFetchTimer = null\n      }\n    },\n\n    // 下载数据\n    downloadData() {\n      if (!this.hasData) {\n        this.$message.warning('没有可下载的数据')\n        return\n      }\n\n      this.$axios.post('/phm/download_sensor_data/', {\n        sensor_type: this.selectedSensorType,\n        values: this.chartData.values,\n        timestamps: this.chartData.timestamps\n      }, {\n        responseType: 'blob' // 指定响应类型为二进制数据\n      }).then(response => {\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n        link.setAttribute('download', `${this.selectedSensorType}_data_${new Date().toISOString().replace(/[:.]/g, '_')}.xlsx`)\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n      }).catch(error => {\n        this.$message.error('下载数据出错: ' + error.message)\n      })\n    },\n\n    // 获取传感器类型的显示标签\n    getSensorTypeLabel(type) {\n      const labels = {\n        'position': '位置传感器',\n        'current': '电流传感器',\n        'setpoint': '指令位移传感器'\n      }\n      return labels[type] || type\n    },\n\n    // 获取当前选择的采样频率标签\n    getSelectedSamplingRateLabel() {\n      const rate = this.samplingRates.find(item => item.value === this.selectedSamplingRate)\n      return rate ? rate.label : `${this.selectedSamplingRate}ms`\n    },\n\n    // 获取传感器单位\n    getUnit(type) {\n      const units = {\n        'position': 'mm',\n        'current': 'A',\n        'setpoint': 'mm'\n      }\n      return units[type] || ''\n    },\n\n    // 清除定时器\n    clearTimer() {\n      if (this.dataFetchTimer) {\n        clearInterval(this.dataFetchTimer)\n        this.dataFetchTimer = null\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n.sensor-monitor-container {\n  padding: 24px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);\n}\n\n/* 页面标题样式 */\n.page-header {\n  margin-bottom: 32px;\n  text-align: center;\n\n  .page-title {\n    font-size: 2em;\n    font-weight: 700;\n    margin: 0;\n    background: linear-gradient(135deg, $techBlue, $techBlueLight);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12px;\n\n    i {\n      font-size: 1.2em;\n      color: $techBlue;\n    }\n\n    .page-subtitle {\n      font-size: 0.4em;\n      color: $textSecondary;\n      font-weight: 400;\n      margin-top: 8px;\n      letter-spacing: 1px;\n      display: block;\n    }\n  }\n}\n\n/* 控制面板样式 */\n.control-panel {\n  margin-bottom: 32px;\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  .form-item {\n    margin-bottom: 0;\n\n    .form-label {\n      color: $textSecondary;\n      font-size: 14px;\n      font-weight: 500;\n      margin-bottom: 8px;\n      display: block;\n    }\n\n    .button-group {\n      display: flex;\n      gap: 12px;\n      flex-wrap: wrap;\n    }\n  }\n}\n\n/* 数据展示区域 */\n.data-display-area {\n  margin-bottom: 32px;\n}\n\n/* 图表容器样式 */\n.chart-container {\n  .chart-info {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n\n    .sampling-rate {\n      color: $textSecondary;\n      font-size: 14px;\n      padding: 4px 12px;\n      background: rgba(0, 212, 255, 0.1);\n      border-radius: 12px;\n      border: 1px solid rgba(0, 212, 255, 0.2);\n    }\n  }\n}\n\n.chart-wrapper {\n  height: 450px;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg,\n      rgba(0, 212, 255, 0.02) 0%,\n      transparent 50%,\n      rgba(255, 107, 53, 0.02) 100%);\n    pointer-events: none;\n    border-radius: 8px;\n  }\n}\n\n/* 现代化选择器样式 */\n.modern-select {\n  :deep(.el-input__inner) {\n    background: rgba(255, 255, 255, 0.05) !important;\n    border: 1px solid $borderSecondary !important;\n    border-radius: 8px !important;\n    color: $textPrimary !important;\n    transition: all 0.3s ease !important;\n\n    &:focus {\n      border-color: $techBlue !important;\n      box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1) !important;\n      background: rgba(255, 255, 255, 0.08) !important;\n    }\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .data-display-area {\n    .el-col {\n      margin-bottom: 16px;\n    }\n  }\n}\n\n@media (max-width: 768px) {\n  .sensor-monitor-container {\n    padding: 16px;\n  }\n\n  .page-header .page-title {\n    font-size: 1.5em;\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .control-panel .card-content {\n    .el-row .el-col {\n      margin-bottom: 16px;\n    }\n  }\n\n  .chart-wrapper {\n    height: 300px;\n  }\n\n  .button-group {\n    justify-content: center;\n  }\n}\n</style>\n"]}]}