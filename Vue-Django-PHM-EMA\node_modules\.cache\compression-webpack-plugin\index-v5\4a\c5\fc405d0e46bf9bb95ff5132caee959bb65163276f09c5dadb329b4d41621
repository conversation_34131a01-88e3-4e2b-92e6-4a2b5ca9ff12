
a9a5d86ef22b04c9e2df4b7bdcd348e8520a2091	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.b51bac6574f99c3443f0.hot-update.js\",\"contentHash\":\"667da60c2b32e865a8dc6ac001ea8ae0\"}","integrity":"sha512-T/e14gBHZ4cLPI26b4rnu09mS4Zp9ymw3FABSJ1KaavwFqC7p7Bp63sDt6em7sexyUC3RZF4BHmg58fLmjl1AQ==","time":1754203859108,"size":26776}