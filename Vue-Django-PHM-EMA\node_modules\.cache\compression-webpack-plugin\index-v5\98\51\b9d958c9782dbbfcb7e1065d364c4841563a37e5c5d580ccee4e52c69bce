
4424ade277d372b9772e4ad92334b6bf2cb3e886	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"2c1673bd287cbcf6fbbbc66f158ec47c\"}","integrity":"sha512-J5BX0Dfpxd1uemJ/SMzgEhd75djUXDymxupPWzlqe4xAhqCqzZ+FoduwKUZPkRCRq8Lp35dn28rTDGehjYAVyQ==","time":1754204504771,"size":26737}