
a2dc468c3366054389b8d8ed90c3663ab04cf08a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"7ec5e6059a6a377eac051f7138bddce6\"}","integrity":"sha512-UGepv7p8vG82brM9fHuqk9oyd3I8sKkwEdQ25BAV61mf17fzjCwmAf08m6H8S+MtOiCaK2dHpnLM/nNrpv6N+w==","time":1754202893182,"size":26807}