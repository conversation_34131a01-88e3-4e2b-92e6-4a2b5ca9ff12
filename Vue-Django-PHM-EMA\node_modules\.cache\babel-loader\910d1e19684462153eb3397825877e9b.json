{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\history\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\history\\index.vue", "mtime": 1754206039963}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA,OAAA,SAAA,MAAA,4BAAA,C,CACA;;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,QADA;AACA;AACA,EAAA,UAAA,EAAA;AACA,IAAA,SAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,gBAAA,EAAA,EADA;AAEA,MAAA,iBAAA,EAAA,EAFA;AAGA,MAAA,WAAA,EAAA,KAHA;AAIA,MAAA,WAAA,EAAA,EAJA;AAKA,MAAA,aAAA,EAAA,EALA;AAMA,MAAA,WAAA,EAAA,EANA;AAOA,MAAA,aAAA,EAAA,EAPA;AAQA,MAAA,mBAAA,EAAA,EARA;AASA,MAAA,YAAA,EAAA,EATA;AAUA,MAAA,SAAA,EAAA,EAVA;AAWA,MAAA,KAAA,EAAA,CAXA;AAYA,MAAA,QAAA,EAAA,EAZA;AAaA,MAAA,WAAA,EAAA,CAbA;AAcA,MAAA,MAAA,EAAA,KAdA;AAeA,MAAA,SAAA,EAAA,EAfA;AAgBA,MAAA,gBAAA,EAAA,KAhBA;AAiBA,MAAA,aAAA,EAAA;AACA,QAAA,SAAA,EAAA,CAAA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,gBAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,gBAAA,KAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,OAAA,KAAA,KAAA,IAAA,GAAA,CAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA,KAAA,EAAA,GAAA,CAAA;AACA;AAPA,SAAA,EAQA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,gBAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,gBAAA,KAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,OAAA,KAAA,MAAA,IAAA,GAAA,CAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA,KAAA,EAAA,GAAA,CAAA;AACA;AAPA,SARA,EAgBA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,gBAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,gBAAA,KAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,OAAA,KAAA,OAAA,IAAA,GAAA,CAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA,KAAA,EAAA,GAAA,CAAA;AACA;AAPA,SAhBA,EAwBA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,gBAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,gBAAA,KAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,OAAA,KAAA,OAAA,IAAA,GAAA,EAAA,GAAA,CAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA,KAAA,EAAA,GAAA,CAAA;AACA;AAPA,SAxBA,EAgCA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,gBAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,gBAAA,KAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,OAAA,KAAA,OAAA,IAAA,GAAA,EAAA,GAAA,CAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA,KAAA,EAAA,GAAA,CAAA;AACA;AAPA,SAhCA,EAwCA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,gBAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,gBAAA,KAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,OAAA,KAAA,OAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA,KAAA,EAAA,GAAA,CAAA;AACA;AAPA,SAxCA;AADA,OAjBA;AAoEA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,EAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,OAAA,EAAA;AALA;AArEA,KAAA;AA6EA,GAnFA;AAoFA,EAAA,OApFA,qBAoFA;AACA,IAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,KAAA,cAAA,EAAA,KAAA;AACA,SAAA,cAAA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA,MAAA,CAAA,KAAA,CAAA,gBAAA;AACA,GAxFA;AAyFA,EAAA,WAzFA,yBAyFA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,QAAA;AACA,GA3FA;AA4FA,EAAA,OAAA,EAAA;AACA,IAAA,cADA,0BACA,IADA,EACA;AACA,WAAA,WAAA,GAAA,QAAA,CAAA,IAAA,CAAA;AACA,WAAA,MAAA,CAAA,MAAA,CAAA,wBAAA,EAAA,KAAA,WAAA;AACA,WAAA,OAAA,CAAA,IAAA;AACA,KALA;AAOA;AACA,IAAA,IARA,kBAQA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA;AACA,WAAA,aAAA,CAAA,IAAA,GAAA,KAAA,WAAA;AACA,WAAA,aAAA,CAAA,MAAA,GAAA,KAAA,aAAA;AACA,KAZA;AAcA;AACA,IAAA,WAfA,yBAeA;AAAA;;AAAA;AACA,WAAA,MAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,aAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,KAAA,CAAA,aAAA;AACA,OAHA;AAIA,WAAA,WAAA,GAAA,IAAA,CALA,CAOA;;AACA,UAAA,GAAA,GAAA,mCAAA,KAAA,aAAA,CAAA,SAAA,GACA,WADA,GACA,KAAA,aAAA,CAAA,OADA,CARA,CAWA;;AACA,UAAA,KAAA,gBAAA,KAAA,KAAA,EAAA;AACA,QAAA,GAAA,IAAA,eAAA,KAAA,gBAAA;AACA;;AAEA,WAAA,MAAA,CAAA,GAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,KAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA;AACA,UAAA,KAAA,CAAA,WAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,aAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,mBAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,YAAA,GAAA,EAAA,CALA,CAKA;;AACA,UAAA,KAAA,CAAA,SAAA,GAAA,EAAA,CANA,CAMA;AACA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,CAAA,QAAA,EADA,CACA;;;AACA,YAAA,KAAA,CAAA,YAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,CAAA,IAAA,EAFA,CAIA;;;AACA,gBAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,CAAA,WAAA,CALA,CAOA;;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,CAAA,IAAA,KAAA,WAAA,EAAA;AACA;AACA,kBAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA;;AACA,kBAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,oBAAA,SAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CADA,CACA;AACA;;AACA,oBAAA,SAAA,EAAA;AACA,sBAAA,WAAA,GAAA,SAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA;;AACA,sBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA;AACA,oBAAA,WAAA,GAAA,WAAA;AACA;AACA;AACA;AACA;;AAEA,YAAA,KAAA,CAAA,aAAA,CAAA,IAAA,CAAA,WAAA;;AACA,YAAA,KAAA,CAAA,mBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA;;AAEA,gBAAA,CAAA,GAAA,EAAA;AACA,YAAA,CAAA,CAAA,MAAA,CAAA,GAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AACA,YAAA,CAAA,CAAA,QAAA,CAAA,GAAA,KAAA,CAAA,aAAA,CAAA,WAAA,MAAA,WAAA,KAAA,GAAA,GAAA,IAAA,GAAA,QAAA,WAAA,CAAA;AACA,YAAA,CAAA,CAAA,cAAA,CAAA,GAAA,KAAA,CAAA,mBAAA,CAAA,CAAA,CAAA;AACA,YAAA,CAAA,CAAA,UAAA,CAAA,GAAA,KAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CA9BA,CAgCA;;AACA,YAAA,CAAA,CAAA,QAAA,CAAA,GAAA,KAAA,CAAA,aAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,CAAA;;AAEA,YAAA,KAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA;AACA,SA7CA,MA6CA;AACA,UAAA,KAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,OAAA,EAAA,WAFA;AAGA,YAAA,QAAA,EAAA,IAHA;AAIA,YAAA,IAAA,EAAA;AAJA,WAAA;;AAMA,UAAA,KAAA,CAAA,SAAA,GAAA,EAAA;AACA;;AACA,QAAA,KAAA,CAAA,WAAA,GAAA,KAAA;AACA,OA3DA;AA4DA,KA3FA;AA6FA;AACA,IAAA,aA9FA,yBA8FA,MA9FA,EA8FA;AACA,UAAA,IAAA,GAAA,EAAA,CADA,CAGA;;AACA,UAAA,MAAA,CAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,YAAA,UAAA,GAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CADA,CACA;;AACA,YAAA,WAAA,GAAA,MAAA,CAAA,IAAA,CAFA,CAIA;;AACA,gBAAA,UAAA;AACA,eAAA,UAAA;AACA,YAAA,IAAA,6CAAA,WAAA,OAAA;AACA;;AACA,eAAA,SAAA;AACA,YAAA,IAAA,6CAAA,WAAA,MAAA;AACA;;AACA,eAAA,UAAA;AACA,YAAA,IAAA,yDAAA,WAAA,OAAA;AACA;;AACA;AACA,YAAA,IAAA,aAAA,UAAA,eAAA,WAAA,CAAA;AAXA;AAaA,OAlBA,MAkBA,IAAA,MAAA,CAAA,IAAA,KAAA,WAAA,EAAA;AACA,YAAA;AACA;AACA,cAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA;;AACA,cAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,gBAAA,QAAA,GAAA,KAAA,CAAA,CAAA,CAAA;AACA,gBAAA,SAAA,GAAA,KAAA,CAAA,CAAA,CAAA;AACA,gBAAA,SAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAHA,CAKA;;AACA,YAAA,IAAA,sCAAA,SAAA,uCAAA,QAAA,kCAAA,KAAA,gBAAA,CAAA,SAAA,CAAA,CAAA;AACA,WAPA,MAOA;AACA,YAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA;AACA,SAbA,CAaA,OAAA,CAAA,EAAA;AACA,UAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA;AACA,OAjBA,MAiBA;AACA,QAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA;;AAEA,aAAA,IAAA;AACA,KA1IA;AA4IA;AACA,IAAA,gBA7IA,4BA6IA,SA7IA,EA6IA;AACA,UAAA,YAAA,GAAA;AACA,oBAAA,MADA;AAEA,gCAAA,SAFA;AAGA,oCAAA,QAHA;AAIA,8CAAA,SAJA;AAKA,gCAAA,UALA;AAMA,8BAAA,UANA;AAOA,sCAAA,QAPA;AAQA,iCAAA,QARA;AASA,mCAAA,QATA;AAUA,sCAAA,SAVA;AAWA,gCAAA,SAXA;AAYA,qCAAA,YAZA;AAaA,2CAAA,UAbA;AAcA,8BAAA;AAdA,OAAA;AAiBA,aAAA,YAAA,CAAA,SAAA,CAAA,IAAA,SAAA;AACA,KAhKA;AAkKA;AACA,IAAA,UAnKA,sBAmKA,QAnKA,EAmKA;AACA,UAAA,QAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,MAAA;AACA,OAFA,MAEA,IAAA,QAAA,KAAA,WAAA,EAAA;AACA,eAAA,SAAA;AACA;;AACA,aAAA,EAAA;AACA,KA1KA;AA4KA;AACA,IAAA,gBA7KA,4BA6KA,QA7KA,EA6KA;AACA,UAAA,QAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,MAAA;AACA,OAFA,MAEA,IAAA,QAAA,KAAA,WAAA,EAAA;AACA,eAAA,MAAA;AACA;;AACA,aAAA,QAAA;AACA,KApLA;AAsLA;AACA,IAAA,cAvLA,0BAuLA,YAvLA,EAuLA;AACA,UAAA,KAAA,GAAA,UAAA,CAAA,YAAA,CAAA;;AACA,UAAA,KAAA,IAAA,GAAA,EAAA;AACA,eAAA,SAAA,CADA,CACA;AACA,OAFA,MAEA,IAAA,KAAA,IAAA,GAAA,EAAA;AACA,eAAA,SAAA,CADA,CACA;AACA,OAFA,MAEA;AACA,iBAAA,SAAA,CADA,CACA;AACA;AACA,KAhMA;AAkMA;AACA,IAAA,YAnMA,wBAmMA,UAnMA,EAmMA;AACA,aAAA,UAAA,GAAA,GAAA;AACA,KArMA;AAuMA;AACA,IAAA,eAxMA,2BAwMA,SAxMA,EAwMA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA,WAAA,aAAA,CAAA,SAAA,GAAA,SAAA,CAAA,CAAA,CAAA;AACA,WAAA,aAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA;AACA,KA7MA;AA8MA,IAAA,iBA9MA,mCA8MA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,QAAA,QAAA,QAAA;AACA,MAAA,GAAA,CAAA,KAAA,GAAA,QAAA;AACA,KAhNA;AAkNA;AACA,IAAA,gBAnNA,4BAmNA,GAnNA,EAmNA;AACA,MAAA,OAAA,CAAA,GAAA,wBAAA,GAAA;AACA,WAAA,QAAA,GAAA,GAAA;AACA,KAtNA;AAuNA,IAAA,mBAvNA,+BAuNA,GAvNA,EAuNA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,GAAA,+BAAA,GAAA;AACA,KA1NA;AA2NA,IAAA,WA3NA,uBA2NA,KA3NA,EA2NA;AACA,aAAA,CAAA,KAAA,WAAA,GAAA,CAAA,IAAA,KAAA,QAAA,GAAA,KAAA,GAAA,CAAA;AACA,KA7NA;AA+NA;AACA,IAAA,cAhOA,4BAgOA;AAAA;;AACA,aAAA,sBAAA,EAAA,IAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,OAAA,GAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AACA,YAAA,SAAA,GAAA,CAAA,MAAA,EAAA,QAAA,CAAA;;AACA,YAAA,IAAA,GAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA;;AACA,QAAA,KAAA,CAAA,oBAAA,CAAA;AACA,UAAA,MAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,WAAA,MAAA,CAAA,OAAA,CAAA,IAAA,IAAA,GAAA,OAAA,EAAA,EAAA,MAAA,CAAA,gBAAA;AAHA,SAAA;AAKA,OATA;AAUA,KA3OA;AA4OA,IAAA,UA5OA,sBA4OA,SA5OA,EA4OA;AACA,aAAA,KAAA,SAAA,CAAA,GAAA,CAAA,UAAA,CAAA;AAAA,eAAA,SAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA;AACA,iBAAA,CAAA,CAAA,CAAA,CAAA;AACA,SAFA,CAAA;AAAA,OAAA,CAAA;AAGA,KAhPA;AAkPA;AACA,IAAA,cAnPA,4BAmPA;AACA,WAAA,iBAAA,GAAA,MAAA,CAAA,WAAA;AACA,WAAA,gBAAA,GAAA,MAAA,CAAA,UAAA;AACA;AAtPA;AA5FA,CAAA", "sourcesContent": ["<template>\n  <div class=\"history-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-time\"></i>\n        历史数据管理\n        <span class=\"page-subtitle\">Historical Data Management</span>\n      </h2>\n    </div>\n    <el-card>\n      <div class=\"history-ctl\" style=\"margin-bottom: 25px\">\n        <el-date-picker\n          v-model=\"timeRange\"\n          :style=\"{ width: windowInnerWidth/3 + 'px' }\"\n          type=\"datetimerange\"\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          :picker-options=\"pickerOptions\"\n          range-separator=\"至\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          align=\"right\"\n          @change=\"timeRangeSelect\"\n        >\n        </el-date-picker>\n        <el-select\n          v-model=\"selectedDataType\"\n          placeholder=\"数据来源类型\"\n          style=\"margin-left:10px; width: 150px\"\n          clearable\n        >\n          <el-option label=\"全部\" value=\"all\"></el-option>\n          <el-option label=\"设备监测\" value=\"monitor\"></el-option>\n          <el-option label=\"故障诊断\" value=\"diagnosis\"></el-option>\n        </el-select>\n        <el-button type=\"primary\" style=\"margin-left:10px\" @click=\"historyData\">获取历史数据</el-button>\n        <el-button type=\"success\" icon=\"el-icon-download\" @click=\"handleDownload\">下载表单</el-button>\n      </div>\n      <div class=\"diagnosisData\">\n        <el-table\n          v-loading=\"listLoading\"\n          :data=\"tableData.slice((currentPage-1)*pageSize,currentPage*pageSize)\"\n          element-loading-text=\"Loading\"\n          height=\"290\"\n          :style=\"{height:windowInnerHeight/2.4 + 'px'}\"\n          border\n          :default-sort=\"{prop: 'time', order: 'descending'}\"\n          :row-class-name=\"tableRowClassName\"\n        >\n          <el-table-column\n            type=\"index\"\n            align=\"center\"\n            label=\"序号\"\n            :index=\"indexMethod\"\n            width=\"80\"\n            sortable\n          >\n          </el-table-column>\n          <el-table-column\n            prop=\"time\"\n            label=\"时间\"\n            width=\"180\"\n            sortable\n          >\n          </el-table-column>\n          <el-table-column\n            prop=\"dataType\"\n            label=\"数据来源\"\n            width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getTagType(scope.row.dataType)\">\n                {{ getDataTypeLabel(scope.row.dataType) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"status\"\n            label=\"故障状态\"\n            width=\"150\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.status === '正常' ? 'success' : 'danger'\" size=\"medium\">\n                {{ scope.row.status }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"healthStatus\"\n            label=\"健康状态\"\n            width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <el-progress\n                :percentage=\"parseFloat(scope.row.healthStatus) * 100\"\n                :color=\"getHealthColor(scope.row.healthStatus)\"\n                :format=\"formatHealth\"\n              ></el-progress>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"detail\"\n            label=\"详细信息\"\n          >\n          </el-table-column>\n        </el-table>\n        <el-pagination\n          :current-page.sync=\"currentPage\"\n          :page-sizes=\"[10,20,50,100,500]\"\n          :page-size=\"pageSize\"\n          :total=\"total\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        >\n        </el-pagination>\n        <el-divider></el-divider>\n        <div class=\"stateImg\">\n          <h3>状态历史图表</h3>\n          <el-button type=\"primary\" @click=\"draw\">绘制状态图</el-button>\n          <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\n            <line-chart :chart-data=\"lineChartData\" @func=\"faultBlastJump\" />\n          </el-row>\n        </div>\n      </div>\n    </el-card>\n\n  </div>\n</template>\n\n<script>\nimport LineChart from './components/LineChart.vue'\n// import { getDataByTime } from '@/api/phm'\n\nexport default {\n  name: '历史数据管理', // eslint-disable-line vue/name-property-casing\n  components: {\n    LineChart\n  },\n  data() {\n    return {\n      windowInnerWidth: '',\n      windowInnerHeight: '',\n      listLoading: false,\n      dbclickType: '',\n      errorNameDict: {},\n      allTimeData: [],\n      allStatusData: [],\n      allHealthStatusData: [],\n      allDataTypes: [],\n      tableData: [],\n      total: 0,\n      pageSize: 10,\n      currentPage: 1,\n      table1: false,\n      timeRange: '',\n      selectedDataType: 'all',\n      pickerOptions: {\n        shortcuts: [{\n          text: '最近一分钟',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 60 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近十分钟',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 600 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一小时',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一天',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一周',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一月',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\n            picker.$emit('pick', [start, end])\n          }\n        }]\n      },\n      // 父组件向子组件传值\n      lineChartData: {\n        title: '状态图',\n        time: [],\n        status: [],\n        startTime: '',\n        endTime: ''\n      }\n    }\n  },\n  mounted() {\n    window.addEventListener('resize', this.onWindowResize, false)\n    this.onWindowResize()\n    console.log('34123', this.$store.state.historyClickType)\n  },\n  deactivated() {\n    console.log('离开历史记录')\n  },\n  methods: {\n    faultBlastJump(type) {\n      this.dbclickType = parseInt(type)\n      this.$store.commit('changeHistoryClickType', this.dbclickType)\n      this.$router.push(`/faultBlast/index`)\n    },\n\n    // 开始画图\n    draw() {\n      console.log('draw')\n      this.lineChartData.time = this.allTimeData\n      this.lineChartData.status = this.allStatusData\n    },\n\n    // 查询历史数据\n    historyData() { // Date对象 Fri Aug 27 2021 10:33:15 GMT+0800 (中国标准时间)\n      this.$axios.get('./errorDict.json').then(res => {\n        this.errorNameDict = res.data.errorNameDict\n        console.log(this.errorNameDict)\n      })\n      this.listLoading = true\n\n      // 构建请求URL，包含时间范围和可选的数据类型过滤\n      let url = '/phm/getDataByTime/?startTime=' + this.lineChartData.startTime +\n      '&endTime=' + this.lineChartData.endTime\n\n      // 如果选择了特定数据类型（不是\"all\"），添加到URL\n      if (this.selectedDataType !== 'all') {\n        url += '&dataType=' + this.selectedDataType\n      }\n\n      this.$axios.get(url).then((res) => {\n        this.total = res.data.data.length\n        console.log(res.data)\n        console.log(res.data.data)\n        if (this.total !== 0) {\n          console.log('1111', res.data.data[0].fields, typeof (res.data.data[0].fields))\n          this.allTimeData = []\n          this.allStatusData = []\n          this.allHealthStatusData = []\n          this.allDataTypes = [] // 添加数据类型数组\n          this.tableData = [] // 清空之前的数据\n          // console.log('23452', this.total)\n          for (var i = 0; i < this.total; i++) {\n            this.allTimeData.push(res.data.data[i].fields.mod_date) // res.data.data[i].fields得一个对象，然后对象中包含mod_date,faultStatus等\n            this.allDataTypes.push(res.data.data[i].fields.type)\n\n            // 获取故障状态并正确处理\n            let statusValue = res.data.data[i].fields.faultStatus\n\n            // 处理诊断数据的特殊状态值\n            if (res.data.data[i].fields.type === 'diagnosis') {\n              // 从data字段解析出故障模式\n              const dataParts = res.data.data[i].fields.data.split('|')\n              if (dataParts.length >= 3) {\n                const faultMode = dataParts[2] // 第三部分是故障模式\n                // 如果故障模式包含数字编号，提取该编号作为状态值\n                if (faultMode) {\n                  const faultNumber = faultMode.split('_')[0]\n                  if (!isNaN(faultNumber)) {\n                    statusValue = faultNumber\n                  }\n                }\n              }\n            }\n\n            this.allStatusData.push(statusValue)\n            this.allHealthStatusData.push(res.data.data[i].fields.healthStatus)\n\n            var a = {}\n            a['time'] = this.allTimeData[i]\n            a['status'] = this.errorNameDict[statusValue] || (statusValue === '0' ? '正常' : '故障-' + statusValue)\n            a['healthStatus'] = this.allHealthStatusData[i]\n            a['dataType'] = this.allDataTypes[i]\n\n            // 添加详细信息展示\n            a['detail'] = this.getDetailInfo(res.data.data[i].fields)\n\n            this.tableData.push(a)\n          }\n        } else {\n          this.$notify({\n            title: '提示',\n            message: '所选时间段内无数据',\n            duration: 2500,\n            type: 'warning'\n          })\n          this.tableData = []\n        }\n        this.listLoading = false\n      })\n    },\n\n    // 获取详细信息的展示\n    getDetailInfo(fields) {\n      let info = ''\n\n      // 如果是设备监测数据\n      if (fields.type && fields.type.startsWith('monitor_')) {\n        const sensorType = fields.type.split('_')[1] // 提取传感器类型\n        const sensorValue = fields.data\n\n        // 根据传感器类型格式化显示\n        switch (sensorType) {\n          case 'position':\n            info = `位置传感器: ${sensorValue}mm`\n            break\n          case 'current':\n            info = `电流传感器: ${sensorValue}A`\n            break\n          case 'setpoint':\n            info = `指令位移传感器: ${sensorValue}mm`\n            break\n          default:\n            info = `${sensorType}: ${sensorValue}`\n        }\n      } else if (fields.type === 'diagnosis') {\n        try {\n          // 尝试解析诊断数据格式：data_file|model_used|fault_mode\n          const parts = fields.data.split('|')\n          if (parts.length >= 3) {\n            const dataFile = parts[0]\n            const modelUsed = parts[1]\n            const faultMode = parts[2]\n\n            // 格式化显示\n            info = `使用模型[${modelUsed}]诊断文件[${dataFile}]的结果: ${this.getFaultModeName(faultMode)}`\n          } else {\n            info = fields.data\n          }\n        } catch (e) {\n          info = fields.data\n        }\n      } else {\n        info = fields.data\n      }\n\n      return info\n    },\n\n    // 获取故障模式的中文名称\n    getFaultModeName(faultMode) {\n      const faultModeMap = {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n\n      return faultModeMap[faultMode] || faultMode\n    },\n\n    // 获取标签类型\n    getTagType(dataType) {\n      if (dataType.startsWith('monitor_')) {\n        return 'info'\n      } else if (dataType === 'diagnosis') {\n        return 'warning'\n      }\n      return ''\n    },\n\n    // 获取数据类型标签\n    getDataTypeLabel(dataType) {\n      if (dataType.startsWith('monitor_')) {\n        return '设备监测'\n      } else if (dataType === 'diagnosis') {\n        return '故障诊断'\n      }\n      return dataType\n    },\n\n    // 获取健康状态颜色\n    getHealthColor(healthStatus) {\n      const value = parseFloat(healthStatus)\n      if (value >= 0.8) {\n        return '#67C23A' // 绿色，健康状态好\n      } else if (value >= 0.6) {\n        return '#E6A23C' // 黄色，健康状态一般\n      } else {\n        return '#F56C6C' // 红色，健康状态差\n      }\n    },\n\n    // 格式化健康状态显示\n    formatHealth(percentage) {\n      return percentage + '%'\n    },\n\n    // 选择时间\n    timeRangeSelect(timeRange) {\n      console.log('start', timeRange[0])\n      console.log('end:', timeRange[1])\n      this.lineChartData.startTime = timeRange[0]\n      this.lineChartData.endTime = timeRange[1]\n    },\n    tableRowClassName({ row, rowIndex }) {\n      row.index = rowIndex\n    },\n\n    // 表格设置\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.pageSize = val\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n      console.log(`当前页: ${val}`)\n    },\n    indexMethod(index) {\n      return (this.currentPage - 1) * this.pageSize + index + 1\n    },\n\n    // 下载表格\n    handleDownload() {\n      import('@/utils/Export2Excel').then(excel => {\n        const tHeader = ['time', 'status']\n        const filterVal = ['time', 'status']\n        const data = this.formatJson(filterVal)\n        excel.export_json_to_excel({\n          header: tHeader,\n          data,\n          filename: 'table_' + this.$moment(new Date().getTime()).format('MM_DD_HH_mm_ss')\n        })\n      })\n    },\n    formatJson(filterVal) {\n      return this.tableData.map(v => filterVal.map(j => {\n        return v[j]\n      }))\n    },\n\n    // 屏幕变化\n    onWindowResize() {\n      this.windowInnerHeight = window.innerHeight\n      this.windowInnerWidth = window.innerWidth\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.history-container {\n  margin: 30px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);\n  padding: 24px;\n\n  /* 页面标题样式 */\n  .page-header {\n    margin-bottom: 32px;\n    text-align: center;\n\n    .page-title {\n      font-size: 2em;\n      font-weight: 700;\n      margin: 0;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n\n      i {\n        font-size: 1.2em;\n        color: #00d4ff;\n      }\n\n      .page-subtitle {\n        font-size: 0.4em;\n        color: #b8c5d1;\n        font-weight: 400;\n        margin-top: 8px;\n        letter-spacing: 1px;\n        display: block;\n      }\n    }\n  }\n}\n\n.stateImg{\n  margin-top:30px\n}\n</style>\n\n"], "sourceRoot": "src/views/history"}]}