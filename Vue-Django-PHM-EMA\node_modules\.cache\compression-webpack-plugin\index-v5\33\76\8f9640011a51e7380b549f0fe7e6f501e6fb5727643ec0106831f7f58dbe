
7300f7e9d32061cc61a6a04480c7388c62bcfdde	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"32005fe7f5cf6be05ee5318bb43a57f6\"}","integrity":"sha512-MlDAgY+RXM6jaX/l0S+M3kHvMKJjX1pL0kn5XsQ7tAbeuw3TJGZ6YkuQQ5gJNg/WZIV1j0B8WvbTKmsibNMFvA==","time":1754204764504,"size":97475}