
6e7e09f1566954e90e0cf5c9d323103e5144a4cf	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"44d47681801519e2826d16b86dd51771\"}","integrity":"sha512-6sUK++TirOE7fEeY8XK9xiQUIio6fGNduYmexCp22kKbArDxO3i8AW7G/j2QwIMA6KJk3AuROnN1jiIzmti6NA==","time":1754206056525,"size":23569}