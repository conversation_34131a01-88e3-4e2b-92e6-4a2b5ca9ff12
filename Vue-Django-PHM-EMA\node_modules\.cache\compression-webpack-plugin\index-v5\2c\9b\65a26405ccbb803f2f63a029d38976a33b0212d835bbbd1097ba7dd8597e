
f8f26792a8219bcea7649e24dee5052be4f5c241	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"0da117fa796d5016cda3ae095b2da399\"}","integrity":"sha512-1A1H0IZRPM0kDZRdMhBt/C24KrOPm1mfRTS3f1Zr5B+QBhyLuCp0NPC053Xey7RLMxScUGMv5sgBgFf23u+sDw==","time":1754205766219,"size":23560}