
3b4a22cd28890a3c85f01a84cd1e044140291de9	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"2ed9c527c50dba056a7ea5d7d9d607db\"}","integrity":"sha512-deo0ic22vv2T1On2QLvI4/O8Ioei6y1DCBVoeIn4ItAgla6m1ISDISLmfsyRWWqys7IjHt6UBKrMjrbDhFaoZg==","time":1754202867242,"size":23604}