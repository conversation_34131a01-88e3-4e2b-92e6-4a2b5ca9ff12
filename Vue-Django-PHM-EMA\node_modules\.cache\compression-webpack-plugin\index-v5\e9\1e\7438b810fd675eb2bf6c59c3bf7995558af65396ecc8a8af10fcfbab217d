
12651477faba27380284d8e3558ff7b4762d4c6f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F3.js\",\"contentHash\":\"5f6a438a45381a9dff763ada491642d0\"}","integrity":"sha512-qrwlu+w3IeVugvQv5p1VoCtI1fTzDKXS20Yqydy7wSnkmUE0ushxWZxNpncxTuqBxGOimwes3/OA48NJmsNNWg==","time":1754201768294,"size":137074}