
e62b3cbe6ed3427d3d7cbbb2bd2b7c8a37563ec4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"0.52bf6c6f01e08e80b507.hot-update.js\",\"contentHash\":\"52cd56e105f77c20670ce14c23be3478\"}","integrity":"sha512-/LoAZfKidqjN00PCImljUYK+wu1/UHDVoiNIJiqFxK/NnsHTKcDK5b5uAAmXxClVo1BxKYA29po8lcRZIi3PUQ==","time":1754201112049,"size":29875}