
9f8527bb2718f346f011eb13e94eaf266f551533	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"0.a0205138a9d2907055e3.hot-update.js\",\"contentHash\":\"04c59fee3192f54fae76e45ccad31644\"}","integrity":"sha512-4LTDxGPR3TDTYuw5jMbFhJYl7GVPsEqyKVHFy6FJKFxptk3Qn4eY36sOgDc1ESk62Hek1bzw8Gx5xaMUXpNBxg==","time":1754201059343,"size":33193}