
1ef3727c73e22201ffb4fdffd9dac92af9a09cf6	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"14d7e9bda618516a78a77b87114379fb\"}","integrity":"sha512-1SK6Duq8dT1fAYcqHuFt2SDkodpxF3FRmVEWhrNMocQOkJCUysit+FtjkUnwvFkZHz8x4U1nEr5/J+nVVm19JA==","time":1754204941500,"size":93028}