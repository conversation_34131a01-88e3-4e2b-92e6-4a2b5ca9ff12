
97434cdec69f16dbc2f94a429281613d1e11acb0	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.6fad956d7762a987d74f.hot-update.js\",\"contentHash\":\"90c20e6b8e62951064fca941336d3aa7\"}","integrity":"sha512-Ko3b4ONYEfluwtJKFTcfwnszSL6+aI4ESrgjsSUck93OpDJmyINNeFl2hmlJ/aNbH4TuDEAts7RXftlm73w7Sg==","time":1754204153626,"size":28083}