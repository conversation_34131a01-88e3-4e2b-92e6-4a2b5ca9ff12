
20da57cd2a3bc323c98e50f01e44de4cb9964d95	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"ba71b34474bd02c48669130c5b3c8a8f\"}","integrity":"sha512-hNjBJYJ36CuZXo1UneS2DuCtbl9M99z0IvN4Ya0F8vHjfGvJI/ruY+XDJPQD6GqtZ2V3T+Xu/B2B8WqvNg0aaA==","time":1754204860476,"size":90042}