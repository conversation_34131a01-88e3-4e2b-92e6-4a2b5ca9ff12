
1f30ef7667c48cde8c9eb774d0a29bca19e2e4e9	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.66ee804ab54fdd5df723.hot-update.js\",\"contentHash\":\"f8c6b9a81594593525df992e4c226dc9\"}","integrity":"sha512-IyqyjpKdeVNBMDyU/Axz79roEpBzLF9XyvcafGtWVBmlilbAEJFkiwjH+8r1LxT4bkHKKaqAjhkW6It5zJ9Sgg==","time":1754204281539,"size":25744}