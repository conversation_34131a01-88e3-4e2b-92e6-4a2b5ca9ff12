
9d64756110ab7e8138519708f859d27cb829aba3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"f3e9cc76a1bb53c6dec6bb4b1ace66a8\"}","integrity":"sha512-dvkDsInLOF2yLX6qtr0q3bTC7mwCpiT+p0DHffBZVLOOLg6pJdHgY/4U1x4WKXmehPVJpPtjzDX62BHTCs1Oxg==","time":1754203679479,"size":114275}