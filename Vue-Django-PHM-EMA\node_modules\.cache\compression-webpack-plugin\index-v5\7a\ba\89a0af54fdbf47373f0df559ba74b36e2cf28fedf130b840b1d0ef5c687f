
8260b6062a506f79650846a7323938876da83fd2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"ed08beddaffb182194ecde60cf4f7324\"}","integrity":"sha512-WNiOu0aArLp6XcJ9Z4qDy5JPlVFeV+qjLHllcMmSqFUZ7O12pIq0c2szggjC7iqJcIZF8PR5tSKdSFg60kCbRw==","time":1754205154367,"size":26751}