
d32b25dc645c8a0b9ddc2ea8144f55b65557d864	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F3.js\",\"contentHash\":\"c492e26227386eb03e98f40cd30a87a6\"}","integrity":"sha512-5bTgbzQHzoCNNePn2SSpvK8owvTLGEYsvn4TDedFPczdVq64U2OZEdEZm6fHHRxk7VbRcgvutqbnJAJ/xbLk9w==","time":1754202098192,"size":137110}