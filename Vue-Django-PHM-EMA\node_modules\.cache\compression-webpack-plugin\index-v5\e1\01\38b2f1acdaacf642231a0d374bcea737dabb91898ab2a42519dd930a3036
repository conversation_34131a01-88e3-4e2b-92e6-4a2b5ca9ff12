
834213aba5663598829af3faaae6c0937d956c68	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"871e99a2ec8a98990a474e9bedda1a4a\"}","integrity":"sha512-+O1E9UjB6MY7eYNQRXKcBQH/fkXOLeaALbvgvz5L6lnRZqLQKkgHIMvkunzTCZVqnbEtxkvM76EtNnO/glCTlA==","time":1754204941106,"size":26786}