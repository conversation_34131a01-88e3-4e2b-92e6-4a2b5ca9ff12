
a37a599eb7eba88df5ac8a8f587ef23119f41b91	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.f39724c9f0994d14a52d.hot-update.js\",\"contentHash\":\"7e9537ae66c7f8158ebbacb82d4b45f1\"}","integrity":"sha512-kKcswAVdekBzFPNIz/7uVtikwLtcsm1RgGAzC+s0rUesnIqR5poruGziKn+invo+3ofF9sbmVZizq6xT7zYq1g==","time":1754204407655,"size":61180}