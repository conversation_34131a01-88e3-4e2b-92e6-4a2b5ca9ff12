
9de6ffce28b12d3d23fa738693998c60b45ca53b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.00a632a0ccef64f9fd63.hot-update.js\",\"contentHash\":\"3d9cd83e88566134a61debd0b470302c\"}","integrity":"sha512-1POWN5sQvCZZK2/d/8OO5usYTIdxXpA7Gsdnkpof+0UyI+beTdntEi89evk4QmDHO1Rc2vGHy9UVPKSuawi0OA==","time":1754202893532,"size":30046}