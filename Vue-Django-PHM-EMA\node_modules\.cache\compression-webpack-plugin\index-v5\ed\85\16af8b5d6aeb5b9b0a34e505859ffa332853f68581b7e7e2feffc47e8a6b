
88e8cdf317ac956f1d720a1bf8a359d3898b2ce0	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"a310d8c5e7f4318e9732726ea8e68f60\"}","integrity":"sha512-H51Ybm0tPsgVMt67Y1slSOaz3lAgUDL45p1fJaqJqMy4C3SzaLKNJqwUX7rvoxX9LLi6KyUfQIgerhAqF7vAEA==","time":1754204408020,"size":23489}