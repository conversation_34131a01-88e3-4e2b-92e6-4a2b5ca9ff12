
3f56042268b3b52f8b7148378ac58b7913485a11	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"b911408b9141600d4b99cf2ae6430ae7\"}","integrity":"sha512-w83VYx7OTmL46mgD0W8lwfAl9BsoARGVgfbYPMGKiz7OSMbRHCEkLORFFGdta/0AGDLFrra0aenSGUfz6UtXtA==","time":1754204907015,"size":23544}