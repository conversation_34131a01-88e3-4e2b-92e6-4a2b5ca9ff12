
c1576aaa2476f38f402ab60a7518708e4803846a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"657f0f95f4d8f000bea335ae8417ed69\"}","integrity":"sha512-LI+ZC8FO7n6H7N0H5n+LJgyoBrY9+XE/flvJ65GLCj+rd3jY79SBTFG4qz04LK1YnJjDWgkfAoaDGkMuEJo/qQ==","time":1754204839579,"size":23525}