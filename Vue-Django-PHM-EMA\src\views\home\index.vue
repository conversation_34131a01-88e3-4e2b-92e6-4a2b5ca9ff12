<template>
  <div class="home-container modern-theme">
    <div class="headtxt">
      <h3 class="main-title">
        <span class="title-icon">⚡</span>
        伺服系统 PHM 软件平台
        <div class="title-subtitle">Prognostics and Health Management System</div>
      </h3>
    </div>
    <div id="model-container" class="modern-card">
      <div class="ctl">
        <el-button-group>
          <el-button class="modern-button" @click="onWindowResize">
            <i class="el-icon-refresh"></i>
            刷新3D视图
          </el-button>
        </el-button-group>
      </div>
      <div v-show="infoShow" id="infoBox">
        <ul style="font-size:20px; line-height:31px">
          <li>组件名称: {{ info.name }}</li>
        </ul>
      </div>
      <!-- 现代化伺服系统信息面板 -->
      <div id="systemInfoBox" class="modern-card" :class="{ 'collapsed': systemInfoCollapsed }">
        <div class="card-header" @click="toggleSystemInfo">
          <h4 class="card-title">
            <i class="el-icon-cpu"></i>
            伺服系统信息
          </h4>
          <div class="header-controls">
            <div class="status-indicator online">在线</div>
            <button class="collapse-btn" :class="{ 'collapsed': systemInfoCollapsed }">
              <i :class="systemInfoCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </button>
          </div>
        </div>
        <div class="card-content" v-show="!systemInfoCollapsed">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-icon">🔧</div>
              <div class="info-details">
                <span class="info-label">型号</span>
                <span class="info-value">XXXX-EMA</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">⚡</div>
              <div class="info-details">
                <span class="info-label">额定功率</span>
                <span class="info-value">200W</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🔋</div>
              <div class="info-details">
                <span class="info-label">额定电压</span>
                <span class="info-value">24V DC</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🔄</div>
              <div class="info-details">
                <span class="info-label">额定转速</span>
                <span class="info-value">3000RPM</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🎛️</div>
              <div class="info-details">
                <span class="info-label">控制方式</span>
                <span class="info-value">闭环位置控制</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">📅</div>
              <div class="info-details">
                <span class="info-label">生产日期</span>
                <span class="info-value">2023-05-15</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">🏷️</div>
              <div class="info-details">
                <span class="info-label">序列号</span>
                <span class="info-value">SN20230515001</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 故障提示浮窗组件 -->
    <fault-notification
      :visible.sync="faultNotificationVisible"
      :type="faultNotificationType"
      :part-name="faultNotificationPartName"
      :status-name="faultNotificationStatusName"
      :diagnosis-time="faultNotificationTime"
      @close="handleFaultNotificationClose"
      @view-details="handleViewDetails"
    />
  </div>
</template>

<script>
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'
import dat from 'three/examples/js/libs/dat.gui.min.js'
import ModelInteractionUtil, {
  DEFAULT_COLOR,
  PART_COLOR,
  GREEN_PARTS
} from '@/utils/ModelInteractionUtil'
import RocketFlameEffect from '@/utils/RocketFlameEffect'
import FaultNotification from '@/components/FaultNotification.vue'
import {
  getPartNameByFaultType,
  getFaultTypeName,
  getPartChineseName,
  getStatusType
} from '@/utils/faultToPartMapping'

export default {
  name: '主控台', // eslint-disable-line vue/name-property-casing
  components: {
    FaultNotification
  },
  data() {
    return {
      dataShow: {
        Xgive: '',
        Xget: '',
        Fget: '',
        healthStatus: ''
      },
      dataPermit: false,
      btntxt: '开始接收数据',
      // mesh: null,
      module: null,
      moduleAll: null,
      folderName: './3D/',
      stlName: '',
      materialCo: DEFAULT_COLOR,
      camera: null,
      scene: null,
      renderer: null,
      controls: null,
      infoShow: true,
      stateShow: true,
      files: null,
      info: {
        name: ''
      },
      isFault: false,
      state: {
        all: 20,
        normal: 20,
        fault: 0
      },
      objectStatus: {
        status: 0
      },
      drawFunc: '整体模型',
      select: '',
      clickCurrentColor: '',
      faultCurrentColor: '',
      p2hDict: null,
      hanziList: [],
      selectObject: null,
      faultObject: null,
      faultIndex: '',
      faultObjectName: ['电机_R', '传感器', '控制器'],
      errorNameDict: {},
      statusName: '',
      // 新增模型交互工具实例
      modelInteraction: null,

      // 火箭尾焰特效相关
      rocketFlameEffect: null,

      // 故障提示浮窗相关数据
      faultNotificationVisible: false,
      faultNotificationType: 'fault', // 'fault' 或 'degradation'
      faultNotificationPartName: '',
      faultNotificationStatusName: '',
      faultNotificationTime: '',

      // 当前故障/退化状态
      currentFaultType: null,
      currentFaultPart: null,

      // 系统信息面板折叠状态
      systemInfoCollapsed: false
    }
  },
  mounted() {
    this.init()
    const element = document.getElementById('model-container')
    this.files = require.context('../../../public/3D', false, /.STL$/).keys()
    this.m = this.$notify.warning({
      title: '提示',
      message: '模型正在加载，请稍后进行操作...',
      duration: 0,
      showClose: false
    })
    // this.onWindowResize()
    this.loadPre()
    element.addEventListener('click', this.onMouseClick, false)
    window.addEventListener('resize', this.onWindowResize, false)
  },
  activated() {
    console.log('进入主控台页面了')
    // 在页面激活时检查诊断结果
    this.checkDiagnosisResult()
  },
  deactivated() {
    console.log('离开主控台页面了')
  },
  beforeDestroy() {
    clearInterval(this.timer)

    // 清理火箭尾焰特效
    if (this.rocketFlameEffect) {
      this.rocketFlameEffect.destroy()
      this.rocketFlameEffect = null
    }

    // 移除事件监听器
    const element = document.getElementById('model-container')
    if (element) {
      element.removeEventListener('click', this.onMouseClick, false)
    }
    window.removeEventListener('resize', this.onWindowResize, false)
  },
  methods: {
    // 切换系统信息面板折叠状态
    toggleSystemInfo() {
      this.systemInfoCollapsed = !this.systemInfoCollapsed
    },

    // 整体初始化
    init() {
      this.createScene()
      this.helper()
      // this.initGui()
      this.createLight()
      this.createCamera()
      this.createRender()
      this.createControls()
      this.render()

      // 创建一个组来包含所有模型，并设置整体位置
      this.moduleAll = new THREE.Group()
      // 将模型放置在网格中央
      this.moduleAll.position.set(0, 0, 0)
      this.scene.add(this.moduleAll)

      // 初始化模型交互工具，添加状态变化回调
      this.modelInteraction = new ModelInteractionUtil(
        this.scene,
        this.info,
        this.handleModelStatusChange
      )

      // 应用新的颜色设置
      this.modelInteraction.resetAllModelsColor()

      // 检查Vuex中是否有未处理的诊断结果
      this.checkDiagnosisResult()
    },

    // 初始化火箭尾焰特效
    initRocketFlameEffect() {
      const daodanModel = this.scene.getObjectByName('daodan')
      if (daodanModel) {
        try {
          this.rocketFlameEffect = new RocketFlameEffect(this.scene, daodanModel, {
            intensity: 1, // 固定强度值
            speed: 3, // 固定速度值
            turbulence: 1 // 固定湍流值
          })
          console.log('火箭尾焰特效已初始化')
        } catch (error) {
          console.error('初始化火箭尾焰特效失败:', error)
        }
      }
    },

    // 检查Vuex中是否有未处理的诊断结果
    checkDiagnosisResult() {
      console.log('检查诊断结果', this.$store.getters['diagnosis/hasUnprocessedResult'])
      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {
        const result = this.$store.getters['diagnosis/currentDiagnosisResult']
        console.log('发现未处理的诊断结果:', result)
        this.handleDiagnosisResult(result)
        this.$store.dispatch('diagnosis/processDiagnosisResult')
        console.log('诊断结果处理完成')
      } else {
        console.log('没有未处理的诊断结果')
      }
    },

    // 处理模型状态变化
    handleModelStatusChange(statusData) {
      console.log('模型状态变化:', statusData)

      if (statusData.type === 'fault' || statusData.type === 'degradation') {
        // 更新状态计数
        this.state.fault = 1
        this.state.normal = this.state.all - this.state.fault
        this.isFault = statusData.type === 'fault'

        // 获取部件中文名称
        const partChineseName = getPartChineseName(statusData.object.name)
        console.log('部件中文名称:', partChineseName)

        // 更新状态名称
        this.statusName = statusData.info.statusName
        console.log('状态名称:', this.statusName)

        // 显示故障提示浮窗
        this.showFaultNotification({
          type: statusData.type,
          partName: partChineseName,
          statusName: statusData.info.statusName,
          diagnosisTime: statusData.info.diagnosisTime
        })

        console.log('已显示故障提示浮窗')
      } else {
        // 重置状态
        this.state.fault = 0
        this.state.normal = this.state.all
        this.isFault = false
        this.statusName = ''
        console.log('已重置状态')
      }
    },

    // 显示故障提示浮窗
    showFaultNotification(notificationData) {
      console.log('显示故障提示浮窗:', notificationData)
      this.faultNotificationType = notificationData.type
      this.faultNotificationPartName = notificationData.partName
      this.faultNotificationStatusName = notificationData.statusName
      this.faultNotificationTime = notificationData.diagnosisTime
      this.faultNotificationVisible = true

      // 确保浮窗在DOM更新后显示
      this.$nextTick(() => {
        console.log('浮窗显示状态:', this.faultNotificationVisible)
      })
    },

    // 处理故障提示浮窗关闭
    handleFaultNotificationClose() {
      this.faultNotificationVisible = false
    },

    // 处理故障诊断结果
    handleDiagnosisResult(result) {
      console.log('处理诊断结果:', result)

      // 如果诊断成功
      if (result && result.success) {
        const diagnosisDetails = result.diagnosis_details
        const conclusion = diagnosisDetails.conclusion
        const faultType = conclusion.predicted_fault_mode

        console.log('诊断结论:', faultType)

        // 如果是正常状态，重置所有部件状态
        if (faultType === '0_normal') {
          console.log('诊断结果为正常状态，重置所有部件')
          this.resetAllPartsStatus()
          return
        }

        // 获取对应的部件名称
        const partName = getPartNameByFaultType(faultType)
        if (!partName) {
          console.warn('未找到对应的部件:', faultType)
          return
        }

        console.log('对应的部件名称:', partName)

        // 获取状态类型和状态名称
        const statusType = getStatusType(faultType)
        const statusName = getFaultTypeName(faultType)

        console.log('状态类型:', statusType, '状态名称:', statusName)

        // 获取当前时间作为诊断时间
        const diagnosisTime = new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        })

        // 保存当前故障信息
        this.currentFaultType = faultType
        this.currentFaultPart = partName

        // 根据状态类型标记部件
        if (statusType === 'fault') {
          // 故障状态，标记为红色
          console.log('标记部件为故障状态:', partName)
          this.modelInteraction.markAsFault(partName, {
            statusName,
            diagnosisTime
          })
        } else if (statusType === 'degradation') {
          // 退化状态，标记为褐色
          console.log('标记部件为退化状态:', partName)
          this.modelInteraction.markAsDegradation(partName, {
            statusName,
            diagnosisTime
          })
        }
      } else {
        // 诊断失败
        console.error('诊断失败:', result ? result.message : '未知错误')
      }
    },

    // 重置所有部件状态
    resetAllPartsStatus() {
      if (this.currentFaultPart) {
        this.modelInteraction.resetStatus(this.currentFaultPart)
        this.currentFaultPart = null
        this.currentFaultType = null

        // 重置状态计数
        this.state.fault = 0
        this.state.normal = this.state.all
        this.isFault = false
        this.statusName = ''
      }
    },

    // 测试故障诊断
    testFaultDiagnosis() {
      console.log('测试故障诊断')

      // 创建一个模拟的诊断结果
      const mockDiagnosisResult = {
        success: true,
        diagnosis_details: {
          conclusion: {
            predicted_fault_mode: '4_fault_stator_short'
          }
        }
      }

      // 处理诊断结果
      this.handleDiagnosisResult(mockDiagnosisResult)
    },

    // 三大件 创建场景
    createScene() {
      this.scene = new THREE.Scene()
    },

    // 创建相机
    createCamera() {
      const element = document.getElementById('model-container')
      const k = element.clientWidth / element.clientHeight // 实际3d显示窗口宽高比
      this.camera = new THREE.PerspectiveCamera(45, k, 0.1, 10000) // 透视相机 (for,aspect,near,far) 视场 长宽比 多近开始渲染 最远看到的距离
      // 调整相机位置，以便更好地观察模型
      this.camera.position.set(0, 400, 600)
      this.camera.lookAt(new THREE.Vector3(0, 0, 0))
      this.scene.add(this.camera)
    },

    // 创建渲染器
    createRender() {
      const element = document.getElementById('model-container')
      this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
      this.renderer.setSize(element.clientWidth, element.clientHeight) // 设置渲染区域尺寸
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      this.renderer.setClearColor(0x050505, 0.6)
      element.appendChild(this.renderer.domElement) // 渲染div到canvas
    },

    // 渲染
    render() {
      // 确保导弹壳体始终保持透明
      const daodanModel = this.scene.getObjectByName('daodan')
      if (daodanModel && daodanModel.material && this.modelInteraction) {
        this.modelInteraction.updateDaodanMaterial(daodanModel)
      }

      // 更新火箭尾焰特效
      if (this.rocketFlameEffect) {
        this.rocketFlameEffect.update()
      }

      this.renderer.render(this.scene, this.camera)
      requestAnimationFrame(this.render)
    },

    // 底部网格和三轴指示器
    helper() {
      var gridplane = new THREE.GridHelper(1200, 60, 0xFF7F50, 0x4A4A4A) // 一格20
      this.scene.add(gridplane)
      const axes = new THREE.AxesHelper(200)
      this.scene.add(axes)
    },

    // 控制模型组件
    initGui() {
      this.paras = {
        // rotationSpeed: 0.005 // 中文也行
        rotationSpeed: 0 // 中文也行
      }
      var gui = new dat.GUI({ autoPlace: false })
      gui.domElement.id = 'gui'
      document.getElementById('gui_container').appendChild(gui.domElement)
      gui.add(this.paras, 'rotationSpeed', 0, 0.05)
    },

    // 创建光源
    createLight() {
      // 环境光 没有特定的光源，该光源不会影响阴影的产生，被应用到全局范围内的所有对象,使用该光源是为了弱化阴影或者添加一些颜色
      const ambientLight = new THREE.AmbientLight(0x999999)
      this.scene.add(ambientLight)
      const point = new THREE.PointLight(0xffffff)
      // const point = new THREE.SpotLight(0xffffff)
      point.position.set(-300, 600, -400)
      this.scene.add(point)
    },

    // 轨道控制、旋转缩放
    createControls() {
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    },

    // 接收数据控制
    dataGet() {
      if (this.btntxt === '停止接收数据') {
        this.btntxt = '开始接收数据'
        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])
        if (this.faultCurrentColor === 14423100) {
          this.faultObject.material.color.set(0x7CFC00)
        } else {
          this.faultObject.material.color.set(this.faultCurrentColor)
        }
      } else {
        this.btntxt = '停止接收数据'
        // 目前认为故障组件只有EMA总装一种，提前指定故障组件 应该在getData()中按照faultIndex再确定
        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])
        this.faultCurrentColor = this.faultObject.material.color.getHex()
        console.log('inital faultObjectColor', this.faultCurrentColor)
        this.$axios.get('./errorDict.json').then(res => {
          this.errorNameDict = res.data.errorNameDict
          // console.log('字典', res.data.errorNameDict)
        })
      }
      this.dataPermit = !this.dataPermit
      console.log(this.dataPermit)
      this.getData()
    },

    // 接收数据
    getData() {
      if (this.dataPermit) {
        /* this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])
        this.faultCurrentColor = this.faultObject.material.color.getHex() */
        this.timer = setInterval(() => {
          this.$axios.get('/phm/getData/').then((res) => {
            // console.log('111222', res.data.data, typeof (res.data.data)) 类型是object
            this.dataShow.Xget = res.data.data.x_get
            this.dataShow.Xgive = res.data.data.x_give
            this.dataShow.Fget = res.data.data.f_get
            // cw, ch, r, k, x, y, z
            this.dataPanel(1600, 840, 50, 6, -380, 0, 210)
            this.faultIndex = res.data.data.faultStatus
            this.dataShow.healthStatus = res.data.data.healthStatus
            this.statusName = this.errorNameDict[this.faultIndex]
            console.log('faultIndex', this.faultIndex)
            if (this.faultIndex !== '0') {
              this.isFault = true
              this.objectStatus.status = 1
              this.state.fault = 1
              this.state.normal = 19
              // if (this.faultIndex !== '10') {
              //   this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])
              // }
              this.faultObject.material.color.set(0xDC143C)
            } else {
              this.isFault = false
              this.objectStatus.status = 0
              this.faultObject.material.color.set(this.faultCurrentColor)
              this.state.fault = 0
              this.state.normal = 20
            }
          })
        }, this.$Common.requestInterval)
      } else {
        clearInterval(this.timer)
      }
    },

    // 加载STL模型
    loadSTL(stlName) {
      console.log('加载STL模型:', stlName)
      // const THIS = this // eslint-disable-line no-unused-vars
      const loader = new STLLoader()
      loader.load(
        this.folderName + stlName,
        geometry => {
          // 确保几何体居中
          geometry.computeBoundingBox()

          // 为导弹壳体模型设置特殊材质
          let material
          if (stlName === 'daodan.STL') {
            material = new THREE.MeshPhongMaterial({
              color: 0xffffff, // 白色
              transparent: true,
              opacity: 0.10, // 更高的透明度
              side: THREE.DoubleSide, // 双面渲染
              depthWrite: false, // 禁用深度写入以避免渲染问题
              depthTest: false, // 禁用深度测试，使射线能够穿透
              shininess: 150 // 增加光泽度
            })
          } else {
            // 检查是否为需要设置为绿色的部件
            const partName = stlName.split('.')[0] // 去掉.STL后缀
            let color = DEFAULT_COLOR // 默认蓝色

            // 检查是否为需要设置为绿色的部件
            for (const greenPart of GREEN_PARTS) {
              if (partName.includes(greenPart)) {
                color = PART_COLOR // 设置为绿色
                break
              }
            }

            material = new THREE.MeshStandardMaterial({
              color: color, // 使用确定的颜色
              transparent: true,
              opacity: 0.7
            })
          }

          this.module = new THREE.Mesh(geometry, material) // 创建网格对象
          this.module.name = stlName.split('.')[0] // 去掉.STL后缀
          console.log('创建模型部件:', this.module.name)
          this.info.name = '模型正在加载...'

          // 旋转模型使底座底面与网格平行
          this.module.rotateX(-Math.PI / 2) // 绕X轴旋转，使模型水平放置
          this.module.rotateX(Math.PI / 2) // 绕X轴顺时针旋转90度

          // 将所有模型添加到模型组
          this.moduleAll.add(this.module)

          // 如果是导弹模型，需要特殊处理
          if (stlName === 'daodan.STL') {
            console.log('处理导弹壳体模型位置')

            // 设置导弹壳体的特殊属性，使其不阻挡交互
            this.module.renderOrder = -1 // 负数renderOrder确保它在其他对象之前渲染
            this.module.userData.isBackground = true // 标记为背景对象

            // 计算所有模型的包围盒
            const box = new THREE.Box3().setFromObject(this.moduleAll)
            const center = box.getCenter(new THREE.Vector3())
            const size = box.getSize(new THREE.Vector3())

            // 重置导弹模型的位置和旋转
            this.module.position.set(0, 0, 0)
            this.module.rotation.set(0, 0, 0)

            // 首先水平放置导弹模型
            this.module.rotateX(-Math.PI / 2)

            // 然后逆时针旋转90度，使导弹壳体与其他零件平行
            this.module.rotateZ(Math.PI / 2)

            // 计算导弹模型的包围盒
            const daodanBox = new THREE.Box3().setFromObject(this.module)
            const daodanSize = daodanBox.getSize(new THREE.Vector3())

            // 调整导弹模型的缩放，确保能包含所有模型
            // 增加缩放系数，使导弹壳体更大，伺服系统可以完全放在内部
            const scaleFactor = Math.max(
              size.x / daodanSize.x,
              size.y / daodanSize.y,
              size.z / daodanSize.z
            ) * 2.5 // 放大3.5倍，确保伺服系统完全位于内部

            this.module.scale.set(scaleFactor, scaleFactor, scaleFactor)

            // 将导弹模型放置在所有模型的中心
            this.module.position.copy(center)

            // 计算导弹壳体的尺寸
            const scaledDaodanLength = daodanSize.z * scaleFactor
            const scaledDaodanWidth = daodanSize.x * scaleFactor
            const scaledDaodanHeight = daodanSize.y * scaleFactor

            // 根据图片反馈调整位置
            // 向下移动导弹壳体（Y轴负方向）
            this.module.position.y -= size.y * 1.3 // 向下移动

            // 向右移动导弹壳体（Z轴正方向，由于旋转了90度）
            this.module.position.z += size.z * 3.7 // 向右移动

            // 向后移动导弹壳体（X轴正方向，由于旋转了90度）
            this.module.position.x += scaledDaodanLength * 0.6// 向后移动

            console.log('导弹壳体模型已调整位置和大小', {
              center,
              size,
              scaleFactor,
              daodanSize,
              scaledDaodanLength,
              scaledDaodanWidth,
              scaledDaodanHeight,
              adjustedPosition: this.module.position
            })

            // 延迟初始化火箭尾焰特效，确保导弹模型完全加载和定位
            setTimeout(() => {
              this.initRocketFlameEffect()
            }, 800)
          }

          // 如果加载完成底座，说明主要模型都已加载完成
          if (stlName === '底座.STL') {
            // 调整整个模型组的位置，使其居中
            this.moduleAll.position.set(0, 0, 0)

            // 计算整个模型组的包围盒，用于居中
            const box = new THREE.Box3().setFromObject(this.moduleAll)
            const center = box.getCenter(new THREE.Vector3())

            // 将模型移动到网格中央
            this.moduleAll.position.x = -center.x
            this.moduleAll.position.z = -center.z
            // 保持y轴位置，使底座底面与网格平行
            this.moduleAll.position.y = -box.min.y + 10 // 稍微抬高一点，避免穿过网格

            this.m.close()
            this.$notify.success({
              title: '提示',
              message: '模型加载完毕'
            })
            this.info.name = '待单击模型...'
            console.log('模型已加载并居中放置')

            // 打印所有模型部件名称，用于调试
            console.log('所有模型部件:')
            this.scene.traverse((object) => {
              if (object.isMesh) {
                console.log(' - ' + object.name)
              }
            })

            // 确保所有部件都应用了正确的颜色
            if (this.modelInteraction) {
              console.log('应用颜色设置...')
              this.modelInteraction.resetAllModelsColor()
            }
          }
        }
      )
    },

    // 改变显示组件重新绘制
    loadPre() { // 加载哪个零件
      // 先加载除导弹外的所有主要部件
      let daodanModel = null
      const otherModels = []

      // 遍历所有模型文件
      for (var i = 0; i < this.files.length; i++) {
        var stlName = this.files[i].split('/')[1]
        // 分开处理导弹模型和其他模型
        if (stlName === 'daodan.STL') {
          // 先记录导弹模型，稍后加载
          daodanModel = stlName
        } else if (!stlName.includes('GB╱T') && !stlName.includes('螺钉')) {
          // 只加载主要零件，减少加载时间
          this.loadSTL(stlName)
          otherModels.push(stlName)
        }
      }

      // 在所有其他模型加载完成后，最后加载导弹壳体模型
      if (daodanModel) {
        setTimeout(() => {
          console.log('加载导弹壳体模型，包含所有其他模型')
          this.loadSTL(daodanModel)
        }, 1000) // 延迟加载，确保其他模型已经加载完成
      }
    },

    // 调整导弹模型位置
    adjustDaodanPosition() {
      const daodanModel = this.scene.getObjectByName('daodan')
      if (!daodanModel) return

      // 计算所有其他模型的包围盒
      const otherModelsBox = new THREE.Box3()
      this.scene.traverse((object) => {
        if (object.isMesh && object.name !== 'daodan') {
          otherModelsBox.expandByObject(object)
        }
      })

      // 计算导弹模型的包围盒
      const daodanBox = new THREE.Box3().setFromObject(daodanModel)

      // 调整导弹模型的位置，使其包含所有其他模型
      // 并将其他模型放在导弹模型的尾部
      const otherModelsCenter = otherModelsBox.getCenter(new THREE.Vector3())
      const daodanCenter = daodanBox.getCenter(new THREE.Vector3())

      // 计算需要移动的距离
      const offsetX = otherModelsCenter.x - daodanCenter.x
      const offsetY = otherModelsCenter.y - daodanCenter.y
      const offsetZ = otherModelsCenter.z - daodanCenter.z

      // 移动导弹模型
      daodanModel.position.set(
        offsetX,
        offsetY,
        offsetZ
      )

      console.log('调整了导弹模型位置')
    },

    // 数据面板
    dataPanel(cw, ch, r, k, x, y, z) {
      // 用canvas生成图片
      var color = ['#008000', '#FF0000']
      var canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      canvas.width = cw
      canvas.height = ch
      ctx.lineWidth = 10
      ctx.fillStyle = 'rgba(255,255,255,1)'
      this.roundRect(ctx, 0, 0, cw, ch, r)
      var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0)
      gradient.addColorStop('0', 'blue')
      gradient.addColorStop('1.0', 'red')
      ctx.font = 'normal 80pt "楷体"'
      ctx.fillStyle = color[this.objectStatus.status]
      ctx.fillText(this.statusName, 700, 150)
      ctx.fillStyle = color[0]
      // ctx.fillText('0.83', 700, 300)
      ctx.fillText(this.dataShow.healthStatus, 700, 300)
      ctx.fillStyle = gradient
      ctx.fillText('当前状态：', 60, 150)
      ctx.fillText('健康值：', 60, 300)
      ctx.fillText('位移指令：', 60, 450)
      ctx.fillText('实际位移：', 60, 600)
      ctx.fillText('负载力：', 60, 750)
      ctx.fillText(this.dataShow.Xgive, 700, 450)
      ctx.fillText(this.dataShow.Xget, 700, 600)
      ctx.fillText(this.dataShow.Fget, 700, 750)
      // canvas.height = 500
      const url = canvas.toDataURL('./img/png')
      var geometry = new THREE.PlaneGeometry(cw / k, ch / k)
      var texture = new THREE.TextureLoader().load(url)
      // 将图像加载为纹理，然后将纹理赋给材质的map属性
      var material = new THREE.MeshBasicMaterial({
        map: texture,
        side: THREE.DoubleSide,
        opacity: 1,
        transparent: true
      })
      const rect = new THREE.Mesh(geometry, material)
      rect.position.set(x, z, y)
      this.scene.add(rect)
    },

    // 画圆角矩形
    roundRect(ctx, x, y, w, h, r) {
      ctx.beginPath()
      ctx.moveTo(x + r, y)
      ctx.arcTo(x + w, y, x + w, y + h, r)
      ctx.arcTo(x + w, y + h, x, y + h, r)
      ctx.arcTo(x, y + h, x, y, r)
      ctx.arcTo(x, y, x + w, y, r)
      ctx.fill()
      ctx.closePath()
    },

    // 监听函数部分 - 使用新的模型交互工具
    onMouseClick(event) {
      // 加载字典数据
      this.$axios.get('./errorDict.json').then(res => {
        this.p2hDict = res.data.pinyin2hanzi
        const obj = this.p2hDict
        this.hanziList = Object.keys(obj)
      })

      // 确保导弹壳体始终保持透明
      const daodanModel = this.scene.getObjectByName('daodan')
      if (daodanModel && daodanModel.material) {
        this.modelInteraction.updateDaodanMaterial(daodanModel)
      }

      const element = document.getElementById('model-container')
      var raycaster = new THREE.Raycaster()
      var mouse = new THREE.Vector2()

      // 将鼠标点击位置的屏幕坐标转成threejs中的标准坐标
      mouse.x = (event.offsetX / element.clientWidth) * 2 - 1
      mouse.y = -(event.offsetY / element.clientHeight) * 2 + 1

      // 通过摄像机和鼠标位置更新射线
      raycaster.setFromCamera(mouse, this.camera)

      // 计算物体和射线的焦点
      var intersects = raycaster.intersectObjects(this.scene.children, true)
        .filter(intersect => {
          // 过滤掉导弹壳体，使其不响应点击
          return intersect.object.name !== 'daodan' &&
                 !intersect.object.userData.isBackground
        })

      if (intersects.length > 0) {
        // 处理点击到的对象
        this.modelInteraction.handleModelClick(intersects[0].object)
      } else {
        // 点击到空白区域
        this.modelInteraction.handleBackgroundClick()
      }
    },

    // 检测区域大小变化
    onWindowResize() {
      const element = document.getElementById('model-container')
      this.camera.aspect = element.clientWidth / element.clientHeight
      this.camera.updateProjectionMatrix()
      this.render()
      // 设置渲染区域尺寸
      this.renderer.setSize(element.clientWidth, element.clientHeight)
      console.log('3d area changes')

      // 恢复到初始正常状态
      this.resetAllPartsStatus()

      // 清除Vuex中的诊断结果
      this.$store.dispatch('diagnosis/clearDiagnosisResult')

      // 重置信息显示
      this.info.name = '待单击模型...'

      console.log('已恢复到初始正常状态')
    },

    // 处理view-details事件
    handleViewDetails() {
      console.log('查看详情:', this.currentFaultType, this.currentFaultPart)

      // 如果当前有故障或退化状态
      if (this.currentFaultType && this.currentFaultPart) {
        // 显示详细信息对话框
        this.$alert(`
          <div class="fault-details">
            <h3>${this.faultNotificationStatusName}</h3>
            <p><strong>部件名称:</strong> ${this.faultNotificationPartName}</p>
            <p><strong>故障类型:</strong> ${this.currentFaultType}</p>
            <p><strong>诊断时间:</strong> ${this.faultNotificationTime}</p>
            <p><strong>可能原因:</strong> ${this.getPossibleCauses(this.currentFaultType)}</p>
            <p><strong>建议解决方案:</strong> ${this.getSuggestedSolutions(this.currentFaultType)}</p>
          </div>
        `, '故障详情', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          callback: action => {
            console.log(action)
          }
        })
      }
    },

    // 获取可能原因
    getPossibleCauses(faultType) {
      const causes = {
        '1_degradation_magnet': '长时间使用导致永磁体性能下降；环境温度过高；机械冲击或振动。',
        '2_degradation_brush_wear': '正常磨损；过载运行；环境中存在过多粉尘。',
        '3_degradation_commutator_oxidation': '环境湿度过高；长期不使用；电刷与换向器接触不良。',
        '4_fault_stator_short': '绝缘材料老化；过载运行；绕组温度过高；制造缺陷。',
        '5_fault_rotor_open': '机械损伤；过载运行；焊接点断裂；制造缺陷。',
        '6_degradation_bearing_wear': '正常磨损；润滑不足；轴承负载过大；轴承安装不当。',
        '7_fault_bearing_stuck': '润滑失效；异物进入；轴承严重磨损；轴承锈蚀。',
        '8_degradation_gear_wear': '正常磨损；润滑不足；齿轮负载过大；齿轮材料缺陷。',
        '9_degradation_sensor_drift': '长期使用导致性能下降；环境温度变化；电源电压波动。',
        '10_fault_sensor_loss': '传感器连接松动；传感器损坏；信号线断路；电源故障。',
        '11_fault_mosfet_breakdown': '过电压；过电流；温度过高；静电放电损伤。',
        '12_degradation_drive_distortion': '电路元件老化；电源电压不稳；信号干扰；温度变化。',
        '13_fault_mcu_crash': '软件错误；电源问题；硬件故障；外部干扰。'
      }
      return causes[faultType] || '未知原因'
    },

    // 获取建议解决方案
    getSuggestedSolutions(faultType) {
      const solutions = {
        '1_degradation_magnet': '更换永磁体；降低工作环境温度；减少机械冲击。',
        '2_degradation_brush_wear': '更换电刷；检查负载是否过大；清洁工作环境。',
        '3_degradation_commutator_oxidation': '清洁换向器表面；降低环境湿度；定期维护。',
        '4_fault_stator_short': '更换定子绕组；检查负载情况；改善散热条件。',
        '5_fault_rotor_open': '更换转子绕组；检查焊接点；降低负载。',
        '6_degradation_bearing_wear': '更换轴承；增加润滑；检查轴承安装情况。',
        '7_fault_bearing_stuck': '更换轴承；清洁轴承；检查润滑情况。',
        '8_degradation_gear_wear': '更换齿轮；增加润滑；检查负载情况。',
        '9_degradation_sensor_drift': '校准传感器；更换传感器；稳定工作环境。',
        '10_fault_sensor_loss': '检查连接；更换传感器；检查信号线路。',
        '11_fault_mosfet_breakdown': '更换MOSFET；检查电路保护措施；改善散热条件。',
        '12_degradation_drive_distortion': '更换老化元件；稳定电源电压；增加信号滤波。',
        '13_fault_mcu_crash': '更新软件；检查电源；更换MCU；增加抗干扰措施。'
      }
      return solutions[faultType] || '请联系专业维修人员'
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/styles/variables.scss";

/* 主容器现代化样式 */
.home-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);

  // 科技感粒子背景增强
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 107, 53, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba(0, 200, 150, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
    animation: particleFloat 20s ease-in-out infinite;
  }
}

/* 标题样式现代化 */
.headtxt {
  text-align: center;
  padding: 20px 0;
  position: relative;
  z-index: 10;

  .main-title {
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 2.2em;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, $techBlue, $techBlueLight);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    .title-icon {
      font-size: 1.2em;
      animation: pulse 2s infinite;
    }

    .title-subtitle {
      font-size: 0.4em;
      color: $textSecondary;
      font-weight: 400;
      margin-top: 8px;
      letter-spacing: 2px;
    }
  }
}

/* 3D模型容器现代化 */
#model-container {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 1px solid $borderPrimary;
  border-radius: 16px;
  background: rgba(26, 29, 41, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: $shadowPrimary;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(0, 212, 255, 0.02) 0%,
      transparent 50%,
      rgba(255, 107, 53, 0.02) 100%);
    pointer-events: none;
  }
}

.ctl {
  position: absolute;
  left: 50%;
  top: 20px;
  transform: translateX(-50%);
  z-index: 100;

  .el-button-group {
    .modern-button {
      background: linear-gradient(135deg, $techBlue, $techBlueDark);
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
        background: linear-gradient(135deg, $techBlueLight, $techBlue);
      }

      i {
        margin-right: 8px;
      }
    }
  }
}

.label-col {
  padding: 8px 5px;
}

#gui_container {
  position: absolute;
  top: 84%;
  left: 81%;
}

#gui {
  transform: translate(-50%, -75px);
}

#infoBox {
  position: absolute;
  padding: 20px;
  background: $bgCard;
  backdrop-filter: blur(10px);
  border: 1px solid $borderPrimary;
  border-radius: 12px;
  color: $textPrimary;
  font-size: 16px;
  min-width: 200px;
  width: 400px;
  box-shadow: $shadowPrimary;
  transition: all 0.3s ease;
  z-index: 100;

  &:hover {
    border-color: $borderHover;
    box-shadow: $shadowHover;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    color: $textPrimary;

    li {
      margin: 8px 0;
      line-height: 1.6;
      color: $textPrimary;
    }
  }
}

/* 现代化系统信息面板样式 */
#systemInfoBox {
  position: absolute;
  right: 20px;
  top: 20px;
  min-width: 320px;
  width: 380px;
  background: $bgCard;
  backdrop-filter: blur(15px);
  border: 1px solid $borderPrimary;
  border-radius: 16px;
  box-shadow: $shadowPrimary;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 100;

  &:hover {
    border-color: $borderHover;
    box-shadow: $shadowHover;
    transform: translateY(-2px);
  }

  .card-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid $borderSecondary;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 212, 255, 0.05);
    }

    .card-title {
      color: $textPrimary;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: $techBlue;
        font-size: 20px;
      }
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .status-indicator {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.online {
        background: rgba(0, 200, 150, 0.2);
        color: #00c896;
        border: 1px solid rgba(0, 200, 150, 0.3);
      }

      &.offline {
        background: rgba(255, 71, 87, 0.2);
        color: #ff4757;
        border: 1px solid rgba(255, 71, 87, 0.3);
      }
    }

    .collapse-btn {
      background: rgba(0, 212, 255, 0.1);
      border: 1px solid rgba(0, 212, 255, 0.3);
      border-radius: 6px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      color: $techBlue;

      &:hover {
        background: rgba(0, 212, 255, 0.2);
        border-color: rgba(0, 212, 255, 0.5);
        transform: scale(1.1);
      }

      i {
        font-size: 14px;
        transition: transform 0.3s ease;
      }

      &.collapsed i {
        transform: rotate(180deg);
      }
    }
  }

  .card-content {
    padding: 20px 24px;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  // 折叠状态样式
  &.collapsed {
    .card-header {
      border-bottom: none;
    }

    .card-content {
      max-height: 0;
      padding: 0 24px;
      opacity: 0;
    }
  }

  .info-grid {
    display: grid;
    gap: 16px;
  }

  .info-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 212, 255, 0.05);
      border-color: rgba(0, 212, 255, 0.2);
      transform: translateX(4px);
    }

    .info-icon {
      font-size: 20px;
      margin-right: 12px;
      width: 24px;
      text-align: center;
    }

    .info-details {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .info-label {
        color: $textSecondary;
        font-size: 14px;
        font-weight: 500;
      }

      .info-value {
        color: $textPrimary;
        font-size: 14px;
        font-weight: 600;
        background: linear-gradient(135deg, $techBlue, $techBlueLight);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }
}

/* 故障详情对话框样式 */
.fault-details {
  padding: 10px;
}

.fault-details h3 {
  color: #F56C6C;
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  text-align: center;
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 10px;
}

.fault-details p {
  margin: 10px 0;
  line-height: 1.6;
}

.fault-details strong {
  color: #303133;
  display: inline-block;
  width: 100px;
  vertical-align: top;
}

/* 自定义El-Alert样式 */
.el-message-box {
  width: 500px !important;
  max-width: 90%;
}

.el-message-box__content {
  max-height: 60vh;
  overflow-y: auto;
}

/* 动画效果 */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
    opacity: 0.8;
  }
  66% {
    transform: translateY(10px) rotate(240deg);
    opacity: 0.9;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  #systemInfoBox {
    width: 300px;
    min-width: 280px;
  }

  .headtxt .main-title {
    font-size: 1.8em;
  }
}

@media (max-width: 768px) {
  #systemInfoBox {
    position: relative;
    right: auto;
    top: auto;
    width: 100%;
    margin: 20px;

    .info-grid {
      grid-template-columns: 1fr;
    }
  }

  .ctl {
    left: 50%;
    top: 10px;
  }

  .headtxt .main-title {
    font-size: 1.5em;
    flex-direction: column;
    gap: 8px;
  }
}

/* Element UI 组件样式覆盖 */
:deep(.el-button-group) {
  .el-button {
    border: none;

    &:first-child {
      border-radius: 8px 0 0 8px;
    }

    &:last-child {
      border-radius: 0 8px 8px 0;
    }

    &:only-child {
      border-radius: 8px;
    }
  }
}

:deep(.el-dialog) {
  background: $bgCard;
  backdrop-filter: blur(10px);
  border: 1px solid $borderPrimary;
  border-radius: 16px;

  .el-dialog__header {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));
    border-bottom: 1px solid $borderSecondary;
    border-radius: 16px 16px 0 0;

    .el-dialog__title {
      color: $textPrimary;
      font-weight: 600;
    }
  }

  .el-dialog__body {
    color: $textSecondary;
  }
}
</style>

