{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\history\\index.vue?vue&type=style&index=0&id=696f786d&scoped=true&lang=css&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\history\\index.vue", "mtime": 1754206022899}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1634626957199}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1634627893377}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1634627525156}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5oaXN0b3J5LWNvbnRhaW5lciB7CiAgbWFyZ2luOiAzMHB4Owp9Cgouc3RhdGVJbWd7CiAgbWFyZ2luLXRvcDozMHB4Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8dA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/history", "sourcesContent": ["<template>\n  <div class=\"history-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-time\"></i>\n        历史数据管理\n        <span class=\"page-subtitle\">Historical Data Management</span>\n      </h2>\n    </div>\n    <el-card>\n      <div class=\"history-ctl\" style=\"margin-bottom: 25px\">\n        <el-date-picker\n          v-model=\"timeRange\"\n          :style=\"{ width: windowInnerWidth/3 + 'px' }\"\n          type=\"datetimerange\"\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          :picker-options=\"pickerOptions\"\n          range-separator=\"至\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          align=\"right\"\n          @change=\"timeRangeSelect\"\n        >\n        </el-date-picker>\n        <el-select\n          v-model=\"selectedDataType\"\n          placeholder=\"数据来源类型\"\n          style=\"margin-left:10px; width: 150px\"\n          clearable\n        >\n          <el-option label=\"全部\" value=\"all\"></el-option>\n          <el-option label=\"设备监测\" value=\"monitor\"></el-option>\n          <el-option label=\"故障诊断\" value=\"diagnosis\"></el-option>\n        </el-select>\n        <el-button type=\"primary\" style=\"margin-left:10px\" @click=\"historyData\">获取历史数据</el-button>\n        <el-button type=\"success\" icon=\"el-icon-download\" @click=\"handleDownload\">下载表单</el-button>\n      </div>\n      <div class=\"diagnosisData\">\n        <el-table\n          v-loading=\"listLoading\"\n          :data=\"tableData.slice((currentPage-1)*pageSize,currentPage*pageSize)\"\n          element-loading-text=\"Loading\"\n          height=\"290\"\n          :style=\"{height:windowInnerHeight/2.4 + 'px'}\"\n          border\n          :default-sort=\"{prop: 'time', order: 'descending'}\"\n          :row-class-name=\"tableRowClassName\"\n        >\n          <el-table-column\n            type=\"index\"\n            align=\"center\"\n            label=\"序号\"\n            :index=\"indexMethod\"\n            width=\"80\"\n            sortable\n          >\n          </el-table-column>\n          <el-table-column\n            prop=\"time\"\n            label=\"时间\"\n            width=\"180\"\n            sortable\n          >\n          </el-table-column>\n          <el-table-column\n            prop=\"dataType\"\n            label=\"数据来源\"\n            width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getTagType(scope.row.dataType)\">\n                {{ getDataTypeLabel(scope.row.dataType) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"status\"\n            label=\"故障状态\"\n            width=\"150\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.status === '正常' ? 'success' : 'danger'\" size=\"medium\">\n                {{ scope.row.status }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"healthStatus\"\n            label=\"健康状态\"\n            width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <el-progress\n                :percentage=\"parseFloat(scope.row.healthStatus) * 100\"\n                :color=\"getHealthColor(scope.row.healthStatus)\"\n                :format=\"formatHealth\"\n              ></el-progress>\n            </template>\n          </el-table-column>\n          <el-table-column\n            prop=\"detail\"\n            label=\"详细信息\"\n          >\n          </el-table-column>\n        </el-table>\n        <el-pagination\n          :current-page.sync=\"currentPage\"\n          :page-sizes=\"[10,20,50,100,500]\"\n          :page-size=\"pageSize\"\n          :total=\"total\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        >\n        </el-pagination>\n        <el-divider></el-divider>\n        <div class=\"stateImg\">\n          <h3>状态历史图表</h3>\n          <el-button type=\"primary\" @click=\"draw\">绘制状态图</el-button>\n          <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\n            <line-chart :chart-data=\"lineChartData\" @func=\"faultBlastJump\" />\n          </el-row>\n        </div>\n      </div>\n    </el-card>\n\n  </div>\n</template>\n\n<script>\nimport LineChart from './components/LineChart.vue'\n// import { getDataByTime } from '@/api/phm'\n\nexport default {\n  name: '历史数据管理', // eslint-disable-line vue/name-property-casing\n  components: {\n    LineChart\n  },\n  data() {\n    return {\n      windowInnerWidth: '',\n      windowInnerHeight: '',\n      listLoading: false,\n      dbclickType: '',\n      errorNameDict: {},\n      allTimeData: [],\n      allStatusData: [],\n      allHealthStatusData: [],\n      allDataTypes: [],\n      tableData: [],\n      total: 0,\n      pageSize: 10,\n      currentPage: 1,\n      table1: false,\n      timeRange: '',\n      selectedDataType: 'all',\n      pickerOptions: {\n        shortcuts: [{\n          text: '最近一分钟',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 60 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近十分钟',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 600 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一小时',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一天',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一周',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n            picker.$emit('pick', [start, end])\n          }\n        }, {\n          text: '最近一月',\n          onClick(picker) {\n            const end = new Date()\n            const start = new Date()\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\n            picker.$emit('pick', [start, end])\n          }\n        }]\n      },\n      // 父组件向子组件传值\n      lineChartData: {\n        title: '状态图',\n        time: [],\n        status: [],\n        startTime: '',\n        endTime: ''\n      }\n    }\n  },\n  mounted() {\n    window.addEventListener('resize', this.onWindowResize, false)\n    this.onWindowResize()\n    console.log('34123', this.$store.state.historyClickType)\n  },\n  deactivated() {\n    console.log('离开历史记录')\n  },\n  methods: {\n    faultBlastJump(type) {\n      this.dbclickType = parseInt(type)\n      this.$store.commit('changeHistoryClickType', this.dbclickType)\n      this.$router.push(`/faultBlast/index`)\n    },\n\n    // 开始画图\n    draw() {\n      console.log('draw')\n      this.lineChartData.time = this.allTimeData\n      this.lineChartData.status = this.allStatusData\n    },\n\n    // 查询历史数据\n    historyData() { // Date对象 Fri Aug 27 2021 10:33:15 GMT+0800 (中国标准时间)\n      this.$axios.get('./errorDict.json').then(res => {\n        this.errorNameDict = res.data.errorNameDict\n        console.log(this.errorNameDict)\n      })\n      this.listLoading = true\n\n      // 构建请求URL，包含时间范围和可选的数据类型过滤\n      let url = '/phm/getDataByTime/?startTime=' + this.lineChartData.startTime +\n      '&endTime=' + this.lineChartData.endTime\n\n      // 如果选择了特定数据类型（不是\"all\"），添加到URL\n      if (this.selectedDataType !== 'all') {\n        url += '&dataType=' + this.selectedDataType\n      }\n\n      this.$axios.get(url).then((res) => {\n        this.total = res.data.data.length\n        console.log(res.data)\n        console.log(res.data.data)\n        if (this.total !== 0) {\n          console.log('1111', res.data.data[0].fields, typeof (res.data.data[0].fields))\n          this.allTimeData = []\n          this.allStatusData = []\n          this.allHealthStatusData = []\n          this.allDataTypes = [] // 添加数据类型数组\n          this.tableData = [] // 清空之前的数据\n          // console.log('23452', this.total)\n          for (var i = 0; i < this.total; i++) {\n            this.allTimeData.push(res.data.data[i].fields.mod_date) // res.data.data[i].fields得一个对象，然后对象中包含mod_date,faultStatus等\n            this.allDataTypes.push(res.data.data[i].fields.type)\n\n            // 获取故障状态并正确处理\n            let statusValue = res.data.data[i].fields.faultStatus\n\n            // 处理诊断数据的特殊状态值\n            if (res.data.data[i].fields.type === 'diagnosis') {\n              // 从data字段解析出故障模式\n              const dataParts = res.data.data[i].fields.data.split('|')\n              if (dataParts.length >= 3) {\n                const faultMode = dataParts[2] // 第三部分是故障模式\n                // 如果故障模式包含数字编号，提取该编号作为状态值\n                if (faultMode) {\n                  const faultNumber = faultMode.split('_')[0]\n                  if (!isNaN(faultNumber)) {\n                    statusValue = faultNumber\n                  }\n                }\n              }\n            }\n\n            this.allStatusData.push(statusValue)\n            this.allHealthStatusData.push(res.data.data[i].fields.healthStatus)\n\n            var a = {}\n            a['time'] = this.allTimeData[i]\n            a['status'] = this.errorNameDict[statusValue] || (statusValue === '0' ? '正常' : '故障-' + statusValue)\n            a['healthStatus'] = this.allHealthStatusData[i]\n            a['dataType'] = this.allDataTypes[i]\n\n            // 添加详细信息展示\n            a['detail'] = this.getDetailInfo(res.data.data[i].fields)\n\n            this.tableData.push(a)\n          }\n        } else {\n          this.$notify({\n            title: '提示',\n            message: '所选时间段内无数据',\n            duration: 2500,\n            type: 'warning'\n          })\n          this.tableData = []\n        }\n        this.listLoading = false\n      })\n    },\n\n    // 获取详细信息的展示\n    getDetailInfo(fields) {\n      let info = ''\n\n      // 如果是设备监测数据\n      if (fields.type && fields.type.startsWith('monitor_')) {\n        const sensorType = fields.type.split('_')[1] // 提取传感器类型\n        const sensorValue = fields.data\n\n        // 根据传感器类型格式化显示\n        switch (sensorType) {\n          case 'position':\n            info = `位置传感器: ${sensorValue}mm`\n            break\n          case 'current':\n            info = `电流传感器: ${sensorValue}A`\n            break\n          case 'setpoint':\n            info = `指令位移传感器: ${sensorValue}mm`\n            break\n          default:\n            info = `${sensorType}: ${sensorValue}`\n        }\n      } else if (fields.type === 'diagnosis') {\n        try {\n          // 尝试解析诊断数据格式：data_file|model_used|fault_mode\n          const parts = fields.data.split('|')\n          if (parts.length >= 3) {\n            const dataFile = parts[0]\n            const modelUsed = parts[1]\n            const faultMode = parts[2]\n\n            // 格式化显示\n            info = `使用模型[${modelUsed}]诊断文件[${dataFile}]的结果: ${this.getFaultModeName(faultMode)}`\n          } else {\n            info = fields.data\n          }\n        } catch (e) {\n          info = fields.data\n        }\n      } else {\n        info = fields.data\n      }\n\n      return info\n    },\n\n    // 获取故障模式的中文名称\n    getFaultModeName(faultMode) {\n      const faultModeMap = {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n\n      return faultModeMap[faultMode] || faultMode\n    },\n\n    // 获取标签类型\n    getTagType(dataType) {\n      if (dataType.startsWith('monitor_')) {\n        return 'info'\n      } else if (dataType === 'diagnosis') {\n        return 'warning'\n      }\n      return ''\n    },\n\n    // 获取数据类型标签\n    getDataTypeLabel(dataType) {\n      if (dataType.startsWith('monitor_')) {\n        return '设备监测'\n      } else if (dataType === 'diagnosis') {\n        return '故障诊断'\n      }\n      return dataType\n    },\n\n    // 获取健康状态颜色\n    getHealthColor(healthStatus) {\n      const value = parseFloat(healthStatus)\n      if (value >= 0.8) {\n        return '#67C23A' // 绿色，健康状态好\n      } else if (value >= 0.6) {\n        return '#E6A23C' // 黄色，健康状态一般\n      } else {\n        return '#F56C6C' // 红色，健康状态差\n      }\n    },\n\n    // 格式化健康状态显示\n    formatHealth(percentage) {\n      return percentage + '%'\n    },\n\n    // 选择时间\n    timeRangeSelect(timeRange) {\n      console.log('start', timeRange[0])\n      console.log('end:', timeRange[1])\n      this.lineChartData.startTime = timeRange[0]\n      this.lineChartData.endTime = timeRange[1]\n    },\n    tableRowClassName({ row, rowIndex }) {\n      row.index = rowIndex\n    },\n\n    // 表格设置\n    handleSizeChange(val) {\n      console.log(`每页 ${val} 条`)\n      this.pageSize = val\n    },\n    handleCurrentChange(val) {\n      this.currentPage = val\n      console.log(`当前页: ${val}`)\n    },\n    indexMethod(index) {\n      return (this.currentPage - 1) * this.pageSize + index + 1\n    },\n\n    // 下载表格\n    handleDownload() {\n      import('@/utils/Export2Excel').then(excel => {\n        const tHeader = ['time', 'status']\n        const filterVal = ['time', 'status']\n        const data = this.formatJson(filterVal)\n        excel.export_json_to_excel({\n          header: tHeader,\n          data,\n          filename: 'table_' + this.$moment(new Date().getTime()).format('MM_DD_HH_mm_ss')\n        })\n      })\n    },\n    formatJson(filterVal) {\n      return this.tableData.map(v => filterVal.map(j => {\n        return v[j]\n      }))\n    },\n\n    // 屏幕变化\n    onWindowResize() {\n      this.windowInnerHeight = window.innerHeight\n      this.windowInnerWidth = window.innerWidth\n    }\n  }\n}\n</script>\n\n<style scoped>\n.history-container {\n  margin: 30px;\n}\n\n.stateImg{\n  margin-top:30px\n}\n</style>\n\n"]}]}