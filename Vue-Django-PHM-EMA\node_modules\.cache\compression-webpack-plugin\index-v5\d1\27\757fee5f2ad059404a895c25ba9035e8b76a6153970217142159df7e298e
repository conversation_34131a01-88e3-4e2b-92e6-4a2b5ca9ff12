
bb817475824863e659f8c9f82c9044116611dad7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.e97ef3dad7103826a76a.hot-update.js\",\"contentHash\":\"e7f17a558337a0eb6a1feeaf7882ddf6\"}","integrity":"sha512-jx50EqLjBBvceGGtL/zw0ghz7FOYMZgXmePZz8RUuhHkJGszGREWcW2IaM38lbGDKuswxirkZcTHR/O1fMDibA==","time":1754204881931,"size":13729}