
2b9df8918a83bc82d2a57c1143d2868408c77899	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"c3a4c27b75fec77055faa860da40962d\"}","integrity":"sha512-/pgQ/AWTjgNfidX/ettXd5N/FS3rtekKiMOjQqcgBMXMVh8Ft4f6ThcF1/kBGc3ItOGIlyaWxadNUeFm7/S7vA==","time":1754202942250,"size":26664}