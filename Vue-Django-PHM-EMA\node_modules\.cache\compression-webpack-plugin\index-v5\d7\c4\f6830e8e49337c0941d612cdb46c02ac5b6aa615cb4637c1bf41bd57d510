
dbc5f50f8dad8bf435435d09ca92bdc8e7d4b16d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"a18c0adeeb8379b869e3973b65e4a7d7\"}","integrity":"sha512-Ji6f9+ZillbSMpmoYnbAQR52DLfvdl83aiH6C+EAN9L8BRbbL15tS2JBnG9WDpwPNq8AzMXjZGimPOKptOiI3w==","time":1754204815193,"size":26763}