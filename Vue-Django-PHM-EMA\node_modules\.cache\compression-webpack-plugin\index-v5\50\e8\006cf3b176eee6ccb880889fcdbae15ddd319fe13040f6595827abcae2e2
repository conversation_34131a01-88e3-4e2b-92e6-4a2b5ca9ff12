
28ad9fc2bf2a55c4f0a0e061c7b4cbb76258f4c2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.38581bc8c15d7c42a11e.hot-update.js\",\"contentHash\":\"1e48bcaac41e84fc8feafdbcd106a4f3\"}","integrity":"sha512-8KXuvwxATFMuOaBf6ixJ3lSoJgzMiuDVKvOWL17MpvgNiiiC9avRWuf0xNeiQSvbVrmMGkWFqoHXlBL+yoO2NQ==","time":1754204299203,"size":26743}