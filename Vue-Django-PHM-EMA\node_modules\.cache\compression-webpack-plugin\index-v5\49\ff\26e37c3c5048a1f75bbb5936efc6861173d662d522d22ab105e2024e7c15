
d0dc2142e63ed85cf7f6405421083c8a5ef78971	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.9f89efa3e965a868d734.hot-update.js\",\"contentHash\":\"40de1bff0c23ae949dfab1132897be23\"}","integrity":"sha512-Cm4qutmTYVdl0+/wNik26U4ynVBiXeKm9g2Qzsi0hybz/1T31W8hhGDpFpZfg/SIONf4AWBhwsDF1bnRWe18xw==","time":1754202908289,"size":35763}