
b06c23ffea5bddca83379f1817d590ac7710640e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"028ab826dd37becac0d45712c1b9c75b\"}","integrity":"sha512-l9EpdyaOF6eX17WUv45M8Cb1ZrjXhuxSgYdi6sOUcp5HBJMAzFTVTL2UudbYWJA7kH//Ka0Ml7676QQJWSR8zQ==","time":1754205117404,"size":94318}