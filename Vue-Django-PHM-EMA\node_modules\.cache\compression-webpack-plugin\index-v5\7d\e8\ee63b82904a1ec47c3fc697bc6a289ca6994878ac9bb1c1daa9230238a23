
635af4cb5d7c6ae641012bfbbf783fc4491c0f2d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F5.js\",\"contentHash\":\"189b041b6e513f91bbe78f846da24425\"}","integrity":"sha512-IV8UZyGzJPBQIoor52DqGkwFoSw5GIwIK9yEyCtZKlWRXDAXh8jSZV/BH0TCVjQyA/1igiwruWkXkPlvAc23lA==","time":1754206071755,"size":134579}