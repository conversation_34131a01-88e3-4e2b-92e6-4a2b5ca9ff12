
3bd79c8f157022b9686b8801a55a97f7ca81dc47	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.ebaf2a8558ab0c9bb389.hot-update.js\",\"contentHash\":\"0a9adcb709ef051a115611de5f12fe84\"}","integrity":"sha512-8Qy6kt/PZDUWiIDNNxYpQ68jm616EtWusQECew0fc5un2bPjy0zGiM64qtGm6wPHKB9SCk2bu0/bup6pd3xYFw==","time":1754204776712,"size":26522}