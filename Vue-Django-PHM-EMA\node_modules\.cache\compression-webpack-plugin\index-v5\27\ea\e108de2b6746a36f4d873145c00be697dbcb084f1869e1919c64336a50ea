
e2324fac5d38e84e5d77c2de3a599c31dc8beb3e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.f39724c9f0994d14a52d.hot-update.js\",\"contentHash\":\"7e9537ae66c7f8158ebbacb82d4b45f1\"}","integrity":"sha512-TwoAXblKMpMym7Z4LipNpdABdBAOJYK6VaPKMKcBY+M0sIM0UXv3C00qUVSrJZK7Mhn74PeobRymaiq145/JcQ==","time":1754204408021,"size":50897}