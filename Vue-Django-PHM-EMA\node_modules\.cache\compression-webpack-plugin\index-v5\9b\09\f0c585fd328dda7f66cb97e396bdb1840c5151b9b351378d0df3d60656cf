
f75f424084f028f15c7b1c0b9330b4ab8b007384	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.ae33f4ab22605e1f005f.hot-update.js\",\"contentHash\":\"5476bc7b1886e8b7a9f94ed9a16833eb\"}","integrity":"sha512-/vLi31VuMNDpIcIHqIinO7AihZTk1dGBuO9q2a3EpSOgIIIBZnASXd5GKD6zVlROngoS3zckKnGjYlsA3cOYWg==","time":1754204839579,"size":26215}