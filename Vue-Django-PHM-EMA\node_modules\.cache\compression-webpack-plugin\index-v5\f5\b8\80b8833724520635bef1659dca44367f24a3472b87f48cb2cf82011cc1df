
16ae8e1c5be23ed9596c84a6fe1f2b7ddf375ec4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"1c920fdb6f3a83b1749881f5a0a810d2\"}","integrity":"sha512-5lkjMHNQWloccU5T80hUe2F20+Cdu1N4fYAMnnRhmQZ9zRhqGaKEPqW0Ljk/44plCY41XV5ethyjS2EIscSC0g==","time":1754204173195,"size":26693}