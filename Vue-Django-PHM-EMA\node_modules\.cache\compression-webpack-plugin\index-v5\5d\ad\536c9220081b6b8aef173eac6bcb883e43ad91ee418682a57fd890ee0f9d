
f2b46de411227822148d07271fd606cd6b041bb8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"787945bd575f90689b0fda79591bbff0\"}","integrity":"sha512-zBsjwJWIqyRJmedDodYD+qOSF9SY2UZTvHIR91Na49F94yG3SAJf0Axs3422Yk1Gh+d68zSgp0xo6HTR3rKI8w==","time":1754204839580,"size":89789}