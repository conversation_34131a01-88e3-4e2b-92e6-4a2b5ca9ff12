
1c23e1081999c09e48cc48b8cb0a772775f7d6d4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"1935e5808a73ed6cc8783d8258e1ebc6\"}","integrity":"sha512-NS0YXHv2mRWm+jc7Uti2+/q3sCohcvKmGRfcUd3/EDx4OuOzFNM14fQwN0cEifRUoDYOFy37HUANBudItm1gFQ==","time":1754205498627,"size":95987}