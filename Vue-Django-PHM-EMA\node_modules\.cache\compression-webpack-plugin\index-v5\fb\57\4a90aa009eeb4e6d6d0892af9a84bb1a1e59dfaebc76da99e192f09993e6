
d6174f0c29e316de660ba7358b4aafcbb1a9e2a0	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.65d8357937cc254a7c13.hot-update.js\",\"contentHash\":\"e27c5f28827f6a90440c667dc47c6a0a\"}","integrity":"sha512-5AduE8r/ZKHfhu96TLrkw9l4zGf22F2/ymzymI1cuo/ZzXGGm3KMjW9nKvIuzkNPZIW/sHJ/Qjfz5caherTvvA==","time":1754202879318,"size":70942}