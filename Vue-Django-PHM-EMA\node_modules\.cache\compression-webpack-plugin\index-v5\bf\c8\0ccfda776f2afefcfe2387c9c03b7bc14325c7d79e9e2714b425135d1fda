
be17f15da1c81532e08a7990c52289cd322816e2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"2.8f8f6e3d8ea28be5fc05.hot-update.js\",\"contentHash\":\"ac72920d1759f90ee9b97648b47624e1\"}","integrity":"sha512-1+8wtWsGpJFdeGFVI2b8HWNyHkLrd9Dp8XVCWVlaUCGH2TjxPVdvgpdmoWjVYZCeEmoP7FLu6Zbe2QNwzjrXhg==","time":1754206026373,"size":57132}