
dcd511963d96ee824988fcfd1886c9a328b03baf	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"0b32dbfd989a877f0a0f0b35b971f765\"}","integrity":"sha512-gfaO+olG6gAOszrVPbgJNwsoz9LjpP6Ats3CAUgoMDTJ748XG9ZAIBsHDdwxisnc9tZIvWr5ZHaXisSzWPo8JQ==","time":1754203231318,"size":147515}