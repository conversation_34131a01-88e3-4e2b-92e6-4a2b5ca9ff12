
affc7d78cf5ff5577133296957e6ea5e23354e51	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.b56796794c846a8a0494.hot-update.js\",\"contentHash\":\"b3afb279e70bcb3e91c7baa6bcf4368b\"}","integrity":"sha512-wTBAQX74aTVEhqopki2Dz6xOv4WRVxeD7I2vZkfSCQWIlTrNu8g5BTYQ6CA5Wr8yNd4cavnBVY5PY8PemYp1Gg==","time":1754204173195,"size":28631}