
b36bdcbda3390e43921fcd10a8a91548afa379fd	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"0bf5aeea5253d50df54ba6bf0af7e345\"}","integrity":"sha512-vMWOVSRZi7AWQVCMlzBzmvIKpXu+IETj4SZJ5xny4wZHlwL+wlLNxfvcOpPz5tgAGAVRFkKphN7Lg1+w6IUJcw==","time":1754202893573,"size":107162}