
008419f56d9dc73d0b625a64e5044b15eccf7eb8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"46c2bb75dcba68da97abc920af939ec9\"}","integrity":"sha512-OqqksbOOgZO7uR7LqiW4vYhKnfUIJ4rThsKyrc9p+OGbVgqoVN5k7a8JbqgzrN9e8Km+MVPhtanXPwcicqErrA==","time":1754202908290,"size":139107}