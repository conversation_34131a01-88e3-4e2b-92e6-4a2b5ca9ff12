{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\diagnosis\\index.vue?vue&type=style&index=0&lang=scss&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\diagnosis\\index.vue", "mtime": 1754204502416}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1634626957199}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1634627893377}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1634627525156}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1634627658274}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5kaWFnbm9zaXMtY29udGFpbmVyewogIG1hcmdpbjogMzBweDsKICBtaW4taGVpZ2h0OiAxMDB2aDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExZDI5IDAlLCAjMjUyYTNkIDEwMCUpOwogIHBhZGRpbmc6IDI0cHg7CgogIC8qIOmhtemdouagh+mimOagt+W8jyAqLwogIC5wYWdlLWhlYWRlciB7CiAgICBtYXJnaW4tYm90dG9tOiAzMnB4OwogICAgdGV4dC1hbGlnbjogY2VudGVyOwoKICAgIC5wYWdlLXRpdGxlIHsKICAgICAgZm9udC1zaXplOiAyZW07CiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7CiAgICAgIG1hcmdpbjogMDsKICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzAwZDRmZiwgIzMzZGRmZik7CiAgICAgIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0OwogICAgICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7CiAgICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgIGdhcDogMTJweDsKCiAgICAgIGkgewogICAgICAgIGZvbnQtc2l6ZTogMS4yZW07CiAgICAgICAgY29sb3I6ICMwMGQ0ZmY7CiAgICAgIH0KCiAgICAgIC5wYWdlLXN1YnRpdGxlIHsKICAgICAgICBmb250LXNpemU6IDAuNGVtOwogICAgICAgIGNvbG9yOiAjYjhjNWQxOwogICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7CiAgICAgICAgbWFyZ2luLXRvcDogOHB4OwogICAgICAgIGxldHRlci1zcGFjaW5nOiAxcHg7CiAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgIH0KICAgIH0KICB9CgogIC8qIOaooeWei+euoeeQhuagh+mimOagt+W8jyAqLwogIC5tb2RlbC1tYW5hZ2VtZW50LWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgICAubW9kZWwtbWFuYWdlbWVudC10aXRsZSB7CiAgICAgIGZvbnQtc2l6ZTogMThweDsKICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgICAgY29sb3I6ICMwMGQ0ZmYgIWltcG9ydGFudDsKICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICBmbGV4OiAxOwogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMDBkNGZmLCAjMzNkZGZmKTsKICAgICAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7CiAgICAgIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDsKICAgICAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0OwogICAgfQoKICAgIC51cGxvYWQtbW9kZWwtYnRuIHsKICAgICAgcGFkZGluZzogMTBweCAyMHB4ICFpbXBvcnRhbnQ7CiAgICAgIGZvbnQtc2l6ZTogMTRweCAhaW1wb3J0YW50OwogICAgICBmb250LXdlaWdodDogNTAwICFpbXBvcnRhbnQ7CiAgICAgIGJvcmRlci1yYWRpdXM6IDZweCAhaW1wb3J0YW50OwogICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgxMDMsIDE5NCwgNTgsIDAuMykgIWltcG9ydGFudDsKICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZSAhaW1wb3J0YW50OwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpICFpbXBvcnRhbnQ7CiAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDEwMywgMTk0LCA1OCwgMC40KSAhaW1wb3J0YW50OwogICAgICB9CgogICAgICBpIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDZweCAhaW1wb3J0YW50OwogICAgICB9CiAgICB9CiAgfQp9CgovKiDmqKHlnovnrqHnkIbooajmoLzmoLflvI8gKi8KLm1vZGVsLW1hbmFnZW1lbnQtdGFibGUgewogIGJhY2tncm91bmQ6IHJnYmEoNDcsIDUxLCA3MywgMC44KSAhaW1wb3J0YW50OwoKICAuZWwtdGFibGVfX2hlYWRlci13cmFwcGVyIHsKICAgIC5lbC10YWJsZV9faGVhZGVyIHsKICAgICAgdGggewogICAgICAgIGJhY2tncm91bmQ6IHJnYmEoNDcsIDUxLCA3MywgMC44KSAhaW1wb3J0YW50OwogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgICAgIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7CiAgICAgICAgZm9udC13ZWlnaHQ6IG5vcm1hbDsKICAgICAgfQogICAgfQogIH0KCiAgLmVsLXRhYmxlX19ib2R5LXdyYXBwZXIgewogICAgYmFja2dyb3VuZDogcmdiYSg0NywgNTEsIDczLCAwLjgpICFpbXBvcnRhbnQ7CgogICAgLmVsLXRhYmxlX19ib2R5IHsKICAgICAgYmFja2dyb3VuZDogcmdiYSg0NywgNTEsIDczLCAwLjgpICFpbXBvcnRhbnQ7CgogICAgICB0ciB7CiAgICAgICAgYmFja2dyb3VuZDogcmdiYSg0NywgNTEsIDczLCAwLjgpICFpbXBvcnRhbnQ7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAyMTIsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgICAgIH0KCiAgICAgICAgJi5lbC10YWJsZV9fcm93LS1zdHJpcGVkIHsKICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoNDcsIDUxLCA3MywgMC42KSAhaW1wb3J0YW50OwoKICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICB0ZCB7CiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7CiAgICAgICAgY29sb3I6ICNiOGM1ZDEgIWltcG9ydGFudDsKICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50OwogICAgICB9CiAgICB9CiAgfQoKICAuZWwtdGFibGVfX2JvcmRlci1sZWZ0LXBhdGNoLAogIC5lbC10YWJsZV9fYm9yZGVyLXJpZ2h0LXBhdGNoIHsKICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgYmFja2dyb3VuZDogcmdiYSg0NywgNTEsIDczLCAwLjgpICFpbXBvcnRhbnQ7CiAgfQoKICAuZWwtdGFibGUtLWJvcmRlciB7CiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDsKICAgIGJhY2tncm91bmQ6IHJnYmEoNDcsIDUxLCA3MywgMC44KSAhaW1wb3J0YW50OwogIH0KCiAgLmVsLXRhYmxlLS1ib3JkZXI6OmFmdGVyIHsKICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogIH0KCiAgLmVsLXRhYmxlLS1ib3JkZXIgdGQsCiAgLmVsLXRhYmxlLS1ib3JkZXIgdGggewogICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7CiAgfQoKICAuZWwtdGFibGUtLWJvcmRlciB0aC5ndXR0ZXI6bGFzdC1vZi10eXBlIHsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDsKICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgYmFja2dyb3VuZDogcmdiYSg0NywgNTEsIDczLCAwLjgpICFpbXBvcnRhbnQ7CiAgfQoKICAuZWwtdGFibGVfX2VtcHR5LWJsb2NrIHsKICAgIGJhY2tncm91bmQ6IHJnYmEoNDcsIDUxLCA3MywgMC44KSAhaW1wb3J0YW50OwogIH0KCiAgLyog56aB55So5YiX5a696LCD5pW05Yqf6IO9ICovCiAgLmVsLXRhYmxlIHRoLmlzLWxlYWYgewogICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7CiAgfQoKICAuZWwtdGFibGUgLmVsLXRhYmxlX19oZWFkZXItd3JhcHBlciAuZWwtdGFibGVfX2hlYWRlciB0aCAuY2VsbCB7CiAgICBjdXJzb3I6IGRlZmF1bHQgIWltcG9ydGFudDsKICB9CgogIC5lbC10YWJsZSAuZWwtdGFibGVfX2hlYWRlci13cmFwcGVyIC5lbC10YWJsZV9faGVhZGVyIHRoOmhvdmVyIC5jZWxsIHsKICAgIGN1cnNvcjogZGVmYXVsdCAhaW1wb3J0YW50OwogIH0KCiAgLmVsLXRhYmxlIHRoLmd1dHRlciB7CiAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7CiAgfQoKICAvKiDlm5vliJflnYfljIDliIbluIMgKi8KICAubW9kZWwtbWFuYWdlbWVudC10YWJsZSAuZWwtdGFibGVfX2hlYWRlci13cmFwcGVyIC5lbC10YWJsZV9faGVhZGVyIGNvbGdyb3VwIGNvbCB7CiAgICB3aWR0aDogMjUlICFpbXBvcnRhbnQ7CiAgfQoKICAubW9kZWwtbWFuYWdlbWVudC10YWJsZSAuZWwtdGFibGVfX2JvZHktd3JhcHBlciAuZWwtdGFibGVfX2JvZHkgY29sZ3JvdXAgY29sIHsKICAgIHdpZHRoOiAyNSUgIWltcG9ydGFudDsKICB9CgogIC5tb2RlbC1tYW5hZ2VtZW50LXRhYmxlIC5lbC10YWJsZV9faGVhZGVyIHRoLAogIC5tb2RlbC1tYW5hZ2VtZW50LXRhYmxlIC5lbC10YWJsZV9fYm9keSB0ZCB7CiAgICB3aWR0aDogMjUlICFpbXBvcnRhbnQ7CiAgICBtaW4td2lkdGg6IDI1JSAhaW1wb3J0YW50OwogICAgbWF4LXdpZHRoOiAyNSUgIWltcG9ydGFudDsKICB9Cn0KCi5ib3gtY2FyZCB7CiAgYmFja2dyb3VuZDogcmdiYSg0NywgNTEsIDczLCAwLjgpICFpbXBvcnRhbnQ7CiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpOwogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMikgIWltcG9ydGFudDsKICBib3JkZXItcmFkaXVzOiAxMnB4ICFpbXBvcnRhbnQ7CiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMykgIWltcG9ydGFudDsKICBtYXJnaW4tYm90dG9tOiAyNHB4OwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CgogICY6aG92ZXIgewogICAgYm9yZGVyLWNvbG9yOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjQpICFpbXBvcnRhbnQ7CiAgICBib3gtc2hhZG93OiAwIDhweCAzMHB4IHJnYmEoMCwgMjEyLCAyNTUsIDAuMykgIWltcG9ydGFudDsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKICB9Cn0KLnNlbGVjdENhcmQgLmVsLWZvcm0taXRlbV9fbGFiZWwgewogIGZvbnQtc2l6ZTogMTdweDsKICBmb250LXdlaWdodDogNjAwOwogIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7Cn0KCi5kaWdub3Npc1JlcG9ydCAuZWwtZGlhbG9nLmRpYVJlcG9ydHsKICBib3JkZXItcmFkaXVzOiAxNXB4OwogIHdpZHRoOiA4MCU7Cn0KCi5yZXBvcnQgLmVsLWZvcm0taXRlbV9fbGFiZWwgewogIGZvbnQtc2l6ZTogMjFweDsKICBmb250LWZhbWlseTogS2FpVGk7CiAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsKfQoucmVwb3J0IC5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgewogIHdpZHRoOiA4MiU7CiAgY29sb3I6ICNiOGM1ZDEgIWltcG9ydGFudDsKfQoKLnByb2JhYmlsaXR5LWRpc3RyaWJ1dGlvbiB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpOwogIGdhcDogMTBweCAyMHB4Owp9Ci5wcm9iLWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoucHJvYi1sYWJlbCB7CiAgd2lkdGg6IDMwMHB4OyAvKiDmoLnmja7mnIDplb/moIfnrb7osIPmlbQgKi8KICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjYjhjNWQxICFpbXBvcnRhbnQ7Cn0KCi8qIOiviuaWreaKpeWRiuW8ueeql+agt+W8j+S8mOWMliAqLwouZGlhZ25vc2lzLXJlcG9ydC1kaWFsb2cgewogIGJhY2tncm91bmQ6IHJnYmEoNDcsIDUxLCA3MywgMC44KSAhaW1wb3J0YW50OwogIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTsKICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpICFpbXBvcnRhbnQ7CiAgYm9yZGVyLXJhZGl1czogMTZweCAhaW1wb3J0YW50OwoKICAuZWwtZGlhbG9nX19ib2R5IHsKICAgIHBhZGRpbmc6IDIwcHggMzBweDsKICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7CiAgICBjb2xvcjogI2I4YzVkMSAhaW1wb3J0YW50OwogIH0KCiAgLmVsLWRpYWxvZ19faGVhZGVyIHsKICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMCwgMjEyLCAyNTUsIDAuMSksIHJnYmEoMCwgMjEyLCAyNTUsIDAuMDUpKTsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7CiAgICBib3JkZXItcmFkaXVzOiAxNnB4IDE2cHggMCAwOwoKICAgIC5lbC1kaWFsb2dfX3RpdGxlIHsKICAgICAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsKICAgICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgIH0KICB9Cn0KCi5kaWFnbm9zaXMtcmVwb3J0LWNvbnRlbnQgewogIGZvbnQtZmFtaWx5OiAiTWljcm9zb2Z0IFlhSGVpIiwgQXJpYWwsIHNhbnMtc2VyaWY7CiAgY29sb3I6ICNiOGM1ZDEgIWltcG9ydGFudDsKCiAgICAucmVwb3J0LWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7CiAgICBtYXJnaW4tYm90dG9tOiAyNXB4OwogICAgcGFkZGluZy1ib3R0b206IDIwcHg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOwogICAgZmxleC13cmFwOiB3cmFwOwoKICAgIEBtZWRpYSAobWF4LXdpZHRoOiA5NjhweCkgewogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwoKICAgICAgLmRpYWdub3Npcy1yZXN1bHQgewogICAgICAgIG1hcmdpbi1sZWZ0OiAwOwogICAgICAgIG1hcmdpbi10b3A6IDE1cHg7CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgbWF4LXdpZHRoOiBub25lOwogICAgICB9CiAgICB9CiAgfQoKICAgIC5yZXBvcnQtbWV0YSB7CiAgICBmbGV4OiAyOwogICAgbWF4LXdpZHRoOiA2NSU7CgogICAgLm1ldGEtaXRlbSB7CiAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgbGluZS1oZWlnaHQ6IDEuNTsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYWxpZ24taXRlbXM6IGJhc2VsaW5lOwoKICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgICB9CgogICAgICAubWV0YS1sYWJlbCB7CiAgICAgICAgY29sb3I6ICNiOGM1ZDEgIWltcG9ydGFudDsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7CiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgICBtaW4td2lkdGg6IDkwcHg7CgogICAgICAgIGkgewogICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgICAgICAgICBjb2xvcjogIzAwZDRmZjsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5tZXRhLXZhbHVlIHsKICAgICAgICBjb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50OwogICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLXdvcmQ7CiAgICAgICAgZmxleDogMTsKICAgICAgICBmb250LWZhbWlseTogQ29uc29sYXMsIE1vbmFjbywgbW9ub3NwYWNlOwogICAgICB9CiAgICB9CgogICAgQG1lZGlhIChtYXgtd2lkdGg6IDk2OHB4KSB7CiAgICAgIG1heC13aWR0aDogMTAwJTsKICAgIH0KICB9CgogIC5kaWFnbm9zaXMtcmVzdWx0IHsKICAgIGZsZXg6IDE7CiAgICBiYWNrZ3JvdW5kOiAjZjlmOWY5OwogICAgYm9yZGVyLXJhZGl1czogOHB4OwogICAgcGFkZGluZzogMTVweCAyMHB4OwogICAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoMCwwLDAsMC4wNSk7CiAgICBtaW4td2lkdGg6IDI1MHB4OwogICAgbWF4LXdpZHRoOiA0MDBweDsKICAgIG1hcmdpbi1sZWZ0OiAyMHB4OwoKICAgIC5yZXN1bHQtdGl0bGUgewogICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDsKICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgfQoKICAgIC5yZXN1bHQtdmFsdWUgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwoKICAgICAgLnJlc3VsdC10YWcgewogICAgICAgIGZvbnQtc2l6ZTogMThweDsKICAgICAgICBwYWRkaW5nOiAxMHB4IDIwcHg7CiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsKICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgd2hpdGUtc3BhY2U6IG5vcm1hbDsKICAgICAgICBoZWlnaHQ6IGF1dG87CiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTsKICAgICAgfQoKICAgICAgLmNvbmZpZGVuY2Utc2NvcmUgewogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzYwNjI2NjsKICAgICAgICBtYXJnaW4tdG9wOiA1cHg7CiAgICAgIH0KICAgIH0KICB9CgogIC5kaWFnbm9zaXMtc3RlcHMtc2VjdGlvbiB7CiAgICBtYXJnaW4tYm90dG9tOiAyNXB4OwoKICAgIC5zZWN0aW9uLXRpdGxlIHsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDsKICAgICAgY29sb3I6ICMzMDMxMzM7CiAgICB9CgogICAgLmRpYWdub3N0aWMtc3RlcHMgewogICAgICBwYWRkaW5nOiAwIDEwcHg7CiAgICB9CiAgfQoKICAucmVwb3J0LWFjdGlvbnMgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgICBnYXA6IDE1cHg7CiAgICBtYXJnaW4tdG9wOiAyNXB4OwoKICAgIC5lbC1idXR0b24gewogICAgICBwYWRkaW5nOiAxMHB4IDIwcHg7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8vBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/diagnosis", "sourcesContent": ["<template>\n  <div class=\"diagnosis-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-s-tools\"></i>\n        设备故障诊断\n        <span class=\"page-subtitle\">Device Fault Diagnosis</span>\n      </h2>\n    </div>\n\n    <!-- 新增FMEA诊断功能 -->\n    <el-card class=\"box-card\">\n      <div class=\"diagnosis-card\">\n        <el-row class=\"Offline\" style=\"background:#fff\">\n          <el-col :span=\"14\">\n            <el-form class=\"selectCard\">\n              <el-form-item label=\"诊断模型\">\n                <el-select v-model=\"selectedFmeaModel\" style=\"width:80%\" clearable placeholder=\"请选择模型\">\n                  <el-option\n                    v-for=\"item in fmeaModelOptions\"\n                    :key=\"item.name\"\n                    :label=\"item.name\"\n                    :value=\"item.name\"\n                  >\n                    <span style=\"float: left\">{{ item.name }}</span>\n                    <span style=\"float: right; color: #8492a6; font-size: 13px\">\n                      {{ item.creation_time }} ({{ item.size_mb }}MB)\n                    </span>\n                  </el-option>\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"诊断数据\">\n                <el-select v-model=\"selectedFmeaData\" style=\"width:80%\" clearable placeholder=\"请选择数据文件\">\n                  <el-option\n                    v-for=\"item in fmeaDataOptions\"\n                    :key=\"item\"\n                    :label=\"item\"\n                    :value=\"item\"\n                  />\n                </el-select>\n              </el-form-item>\n              <el-button type=\"primary\" @click=\"getFmeaData(); getFmeaModel()\">刷新数据</el-button>\n              <el-button type=\"primary\" style=\"margin-left:15px\" @click=\"startFmeaDiagnosis\">开始诊断</el-button>\n            </el-form>\n          </el-col>\n        </el-row>\n      </div>\n    </el-card>\n\n    <!-- 模型管理卡片 -->\n    <el-card class=\"box-card\" style=\"margin-top: 20px;\">\n      <div slot=\"header\" class=\"clearfix model-management-header\">\n        <span class=\"model-management-title\">诊断算法模型管理</span>\n        <el-button\n          size=\"medium\"\n          type=\"success\"\n          icon=\"el-icon-upload\"\n          @click=\"showUploadModelDialog\"\n          class=\"upload-model-btn\"\n        >\n          上传新模型\n        </el-button>\n      </div>\n      <el-table\n        :data=\"fmeaModelOptions\"\n        style=\"width: 100%\"\n        border\n        stripe\n        class=\"model-management-table\"\n        :resizable=\"false\"\n      >\n        <el-table-column prop=\"name\" label=\"诊断算法模型名称\" :resizable=\"false\" />\n        <el-table-column prop=\"creation_time\" label=\"创建时间\" :resizable=\"false\" />\n        <el-table-column prop=\"size_mb\" label=\"大小(MB)\" :resizable=\"false\" />\n        <el-table-column label=\"操作\" :resizable=\"false\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"danger\"\n              @click=\"handleDeleteModel(scope.row)\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n\n    <!-- 新FMEA诊断报告 -->\n    <el-dialog title=\"28SY型 FMEA 诊断报告\" :visible.sync=\"fmeaDialogVisible\" width=\"80%\" custom-class=\"diagnosis-report-dialog\">\n      <div v-if=\"fmeaReport && fmeaReport.success\" class=\"diagnosis-report-content\">\n        <!-- 基本信息区域 -->\n        <div class=\"report-header\">\n          <div class=\"report-meta\">\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-time\"></i> 诊断时间：</span>\n              <span class=\"meta-value\">{{ currentDiagnosisTime }}</span>\n            </div>\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-folder\"></i> 数据文件：</span>\n              <span class=\"meta-value\">{{ fmeaReport.diagnosis_details.data_file }}</span>\n            </div>\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-cpu\"></i> 诊断模型：</span>\n              <span class=\"meta-value\">{{ fmeaReport.diagnosis_details.model_used }}</span>\n            </div>\n          </div>\n\n          <!-- 诊断结果突出显示 -->\n          <div class=\"diagnosis-result\">\n            <div class=\"result-title\">诊断结果</div>\n            <div class=\"result-value\">\n              <el-tag\n                :type=\"getFaultTagType(fmeaReport.diagnosis_details.conclusion.predicted_fault_mode)\"\n                size=\"large\"\n                effect=\"dark\"\n                class=\"result-tag\"\n                style=\"max-width: 100%; white-space: normal; height: auto; line-height: 1.5; padding: 10px 15px; font-size: 18px;\"\n              >\n                {{ getChineseFaultName(fmeaReport.diagnosis_details.conclusion.predicted_fault_mode) }}\n              </el-tag>\n              <div v-if=\"fmeaReport.diagnosis_details.conclusion.confidence_score\" class=\"confidence-score\">\n                置信度: {{ (fmeaReport.diagnosis_details.conclusion.confidence_score * 100).toFixed(1) }}%\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 诊断步骤 -->\n        <div class=\"diagnosis-steps-section\">\n          <div class=\"section-title\">诊断流程</div>\n          <el-steps :active=\"fmeaReport.diagnosis_details.steps.length\" finish-status=\"success\" class=\"diagnostic-steps\">\n            <el-step\n              v-for=\"step in fmeaReport.diagnosis_details.steps\"\n              :key=\"step.step\"\n              :title=\"step.description\"\n              :description=\"`耗时: ${step.duration_ms} ms`\"\n            />\n          </el-steps>\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"report-actions\">\n          <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadReport\">\n            下载诊断报告\n          </el-button>\n          <el-button\n            v-if=\"fmeaReport.diagnosis_details.conclusion.predicted_fault_mode !== '0_normal'\"\n            type=\"success\"\n            icon=\"el-icon-view\"\n            @click=\"viewInConsole\"\n          >\n            在3D模型中查看\n          </el-button>\n          <el-button type=\"warning\" icon=\"el-icon-data-analysis\" @click=\"viewHealthAssessment\">\n            查看健康评估\n          </el-button>\n        </div>\n      </div>\n      <div v-else-if=\"fmeaReport\">\n        <el-alert\n          :title=\"'诊断失败: ' + fmeaReport.message\"\n          type=\"error\"\n          show-icon\n        />\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"fmeaDialogVisible = false\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 上传模型对话框 -->\n    <el-dialog title=\"上传新模型\" :visible.sync=\"uploadModelDialogVisible\" width=\"40%\">\n      <el-form :model=\"uploadModelForm\" label-width=\"100px\">\n        <el-form-item label=\"模型文件\">\n          <el-upload\n            ref=\"upload\"\n            class=\"upload-demo\"\n            action=\"#\"\n            :on-change=\"handleModelFileChange\"\n            :auto-upload=\"false\"\n            :limit=\"1\"\n            :file-list=\"modelFileList\"\n          >\n            <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传.h5格式的模型文件</div>\n          </el-upload>\n        </el-form-item>\n        <el-form-item label=\"自定义名称\">\n          <el-input v-model=\"uploadModelForm.newName\" placeholder=\"可选，留空使用原文件名\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"uploadModelDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" :disabled=\"modelFileList.length === 0\" @click=\"submitUploadModel\">上 传</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n// LineChart组件已不再使用，可以移除\n// import LineChart from './components/LineChart.vue'\n\nexport default {\n  name: '设备故障诊断', // eslint-disable-line vue/name-property-casing\n  components: {\n    // LineChart\n  },\n  data() {\n    return {\n      // FMEA诊断所需数据\n      fmeaDataOptions: [],\n      fmeaModelOptions: [],\n      selectedFmeaData: '',\n      selectedFmeaModel: '',\n      fmeaDialogVisible: false,\n      fmeaReport: null,\n\n      // 模型上传相关\n      uploadModelDialogVisible: false,\n      uploadModelForm: {\n        newName: ''\n      },\n      modelFileList: [],\n\n      // 新增属性\n      currentDiagnosisTime: '',\n\n      // 故障类型映射表\n      faultModeMap: {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n    }\n  },\n  mounted() {\n    this.getFmeaData()\n    this.getFmeaModel()\n  },\n  methods: {\n    getFmeaData() {\n      this.fmeaDataOptions = []\n      this.$axios.get('/phm/getOfflineData_28sy/').then(res => {\n        if (res.data.code === 200) {\n          this.fmeaDataOptions = res.data.data\n        }\n      })\n    },\n    getFmeaModel() {\n      this.fmeaModelOptions = []\n      this.$axios.get('/phm/getOfflineModel_28sy/').then(res => {\n        if (res.data.code === 200) {\n          this.fmeaModelOptions = res.data.data\n          // 如果只有一个模型，默认选中\n          if (this.fmeaModelOptions.length === 1) {\n            this.selectedFmeaModel = this.fmeaModelOptions[0].name\n          }\n        }\n      })\n    },\n    startFmeaDiagnosis() {\n      if (!this.selectedFmeaData) {\n        this.$notify({\n          title: '提示',\n          message: '请选择诊断数据！',\n          duration: 3500,\n          type: 'error'\n        })\n        return\n      }\n\n      const wait = this.$notify({\n        title: '提示',\n        duration: 0,\n        message: '正在进行FMEA诊断...',\n        type: 'info'\n      })\n      this.fmeaReport = null\n\n      // 构建请求URL，如果选择了模型则添加模型参数\n      let url = `/phm/offlineDiagnosis_28sy/?name=${this.selectedFmeaData}`\n      if (this.selectedFmeaModel) {\n        url += `&model=${this.selectedFmeaModel}`\n      }\n\n      this.$axios.get(url)\n        .then(res => {\n          wait.close()\n          this.fmeaReport = res.data\n          this.fmeaDialogVisible = true\n          if (res.data.success) {\n            // 设置当前诊断时间\n            this.currentDiagnosisTime = new Date().toLocaleString('zh-CN', {\n              year: 'numeric',\n              month: '2-digit',\n              day: '2-digit',\n              hour: '2-digit',\n              minute: '2-digit',\n              second: '2-digit',\n              hour12: false\n            })\n\n            this.$notify({\n              title: '成功',\n              message: 'FMEA诊断完成',\n              duration: 4500,\n              type: 'success'\n            })\n\n            // 保存诊断结果到Vuex\n            this.$store.dispatch('diagnosis/saveDiagnosisResult', res.data)\n\n            // 自动保存诊断结果到历史数据库\n            this.saveDiagnosisToHistory(res.data)\n          } else {\n            this.$notify({\n              title: '失败',\n              message: `诊断出错: ${res.data.message}`,\n              duration: 5000,\n              type: 'error'\n            })\n          }\n        }).catch(err => {\n          wait.close()\n          this.$notify({\n            title: '网络错误',\n            message: `请求诊断接口失败: ${err}`,\n            duration: 5000,\n            type: 'error'\n          })\n        })\n    },\n    getProgressBarStatus(probability) {\n      if (probability > 0.7) return 'success'\n      if (probability > 0.3) return 'warning'\n      return 'exception'\n    },\n\n    // 模型管理相关方法\n    showUploadModelDialog() {\n      this.uploadModelDialogVisible = true\n      this.uploadModelForm.newName = ''\n      this.modelFileList = []\n    },\n\n    handleModelFileChange(file, fileList) {\n      // 限制只能选择一个文件，并且必须是.h5格式\n      if (fileList.length > 1) {\n        fileList.splice(0, 1)\n      }\n\n      if (file.raw && !file.raw.name.endsWith('.h5')) {\n        this.$message.error('只能上传.h5格式的模型文件!')\n        fileList.pop()\n      }\n\n      this.modelFileList = fileList\n    },\n\n    submitUploadModel() {\n      if (this.modelFileList.length === 0) {\n        this.$message.error('请先选择模型文件')\n        return\n      }\n\n      // 获取文件对象\n      const file = this.modelFileList[0].raw\n\n      // 创建表单数据\n      const formData = new FormData()\n      formData.append('model_file', file)\n\n      if (this.uploadModelForm.newName) {\n        formData.append('new_name', this.uploadModelForm.newName)\n      }\n\n      // 显示上传中提示\n      const loading = this.$loading({\n        lock: true,\n        text: '正在上传模型...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      // 直接使用axios发送请求\n      this.$axios.post('/phm/uploadModel_28sy/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      }).then(response => {\n        loading.close()\n        this.$notify({\n          title: '成功',\n          message: '模型上传成功',\n          type: 'success',\n          duration: 3000\n        })\n        this.uploadModelDialogVisible = false\n        this.getFmeaModel() // 刷新模型列表\n      }).catch(error => {\n        loading.close()\n        this.$notify({\n          title: '失败',\n          message: `模型上传失败: ${error}`,\n          type: 'error',\n          duration: 5000\n        })\n      })\n    },\n\n    handleDeleteModel(row) {\n      this.$confirm(`确认删除模型 \"${row.name}\"?`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const formData = new FormData()\n        formData.append('model_name', row.name)\n\n        this.$axios.post('/phm/deleteModel_28sy/', formData)\n          .then(res => {\n            if (res.data.code === 200) {\n              this.$notify({\n                title: '成功',\n                message: res.data.message,\n                type: 'success',\n                duration: 3000\n              })\n              // 如果删除的是当前选中的模型，清空选择\n              if (this.selectedFmeaModel === row.name) {\n                this.selectedFmeaModel = ''\n              }\n              this.getFmeaModel() // 刷新模型列表\n            } else {\n              this.$notify({\n                title: '失败',\n                message: res.data.data,\n                type: 'error',\n                duration: 5000\n              })\n            }\n          })\n          .catch(err => {\n            this.$notify({\n              title: '错误',\n              message: `删除模型失败: ${err}`,\n              type: 'error',\n              duration: 5000\n            })\n          })\n      }).catch(() => {\n        // 取消删除操作\n      })\n    },\n\n    // 新增方法\n    getFaultTagType(faultMode) {\n      // 根据故障模式名称判断标签类型\n      if (faultMode === '0_normal') {\n        return 'success' // 正常状态为绿色\n      } else if (faultMode.includes('degradation')) {\n        return 'warning' // 退化类型为黄色\n      } else if (faultMode.includes('fault')) {\n        return 'danger' // 故障类型为红色\n      }\n      return 'info' // 默认为蓝色\n    },\n\n    getChineseFaultName(faultMode) {\n      return this.faultModeMap[faultMode] || '未知故障'\n    },\n\n    downloadReport() {\n      if (!this.fmeaReport || !this.fmeaReport.success) {\n        this.$message.error('没有可下载的诊断报告')\n        return\n      }\n\n      // 创建报告内容\n      const reportData = this.fmeaReport.diagnosis_details\n      const conclusion = reportData.conclusion\n\n      // 构建HTML内容\n      const htmlContent = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <title>28SY型 FMEA 诊断报告</title>\n          <style>\n            body { \n              font-family: \"Microsoft YaHei\", Arial, sans-serif; \n              margin: 0; \n              padding: 30px;\n              background-color: #f5f7fa;\n              color: #303133;\n            }\n            .report-container {\n              max-width: 900px;\n              margin: 0 auto;\n              background: white;\n              border-radius: 8px;\n              box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n              padding: 30px;\n            }\n            .header { \n              text-align: center; \n              margin-bottom: 30px;\n              padding-bottom: 20px;\n              border-bottom: 1px solid #EBEEF5;\n            }\n            .header h1 {\n              color: #409EFF;\n              margin-bottom: 10px;\n            }\n            .header p {\n              color: #606266;\n              font-size: 14px;\n            }\n            .section { \n              margin-bottom: 30px; \n            }\n            .section h2 {\n              color: #303133;\n              font-size: 18px;\n              margin-bottom: 15px;\n              padding-bottom: 10px;\n              border-bottom: 1px solid #EBEEF5;\n            }\n            .meta-info {\n              display: flex;\n              flex-wrap: wrap;\n              margin-bottom: 20px;\n            }\n            .meta-item {\n              flex: 1;\n              min-width: 200px;\n              padding: 10px 15px;\n              background: #f9f9f9;\n              border-radius: 4px;\n              margin: 5px;\n            }\n            .meta-label {\n              font-weight: bold;\n              margin-bottom: 5px;\n              color: #606266;\n            }\n            .meta-value {\n              color: #303133;\n            }\n            .steps { \n              display: flex; \n              justify-content: space-between; \n              margin: 20px 0;\n              flex-wrap: wrap;\n            }\n            .step { \n              text-align: center; \n              padding: 15px; \n              border-top: 3px solid #67C23A; \n              flex: 1;\n              min-width: 150px; \n              background: #f9f9f9;\n              border-radius: 4px;\n              margin: 5px;\n            }\n            .step-title {\n              font-weight: bold;\n              margin-bottom: 8px;\n            }\n            .step-duration {\n              font-size: 12px;\n              color: #909399;\n            }\n            .result { \n              margin: 20px 0; \n              padding: 20px; \n              border-radius: 8px;\n              background: #f9f9f9;\n              text-align: center;\n            }\n            .result-label {\n              font-size: 16px;\n              font-weight: bold;\n              margin-bottom: 10px;\n            }\n            .result-value {\n              display: inline-block;\n              padding: 10px 20px;\n              border-radius: 4px;\n              font-size: 18px;\n              font-weight: bold;\n            }\n            .normal { \n              color: white; \n              background-color: #67C23A;\n            }\n            .degradation { \n              color: white; \n              background-color: #E6A23C;\n            }\n            .fault { \n              color: white; \n              background-color: #F56C6C;\n            }\n            .confidence {\n              margin-top: 10px;\n              font-size: 14px;\n              color: #606266;\n            }\n            table { \n              width: 100%; \n              border-collapse: collapse; \n              margin-top: 10px;\n            }\n            th, td { \n              padding: 12px; \n              text-align: left; \n              border-bottom: 1px solid #EBEEF5;\n            }\n            th {\n              background-color: #f5f7fa;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"report-container\">\n            <div class=\"header\">\n              <h1>28SY型 FMEA 诊断报告</h1>\n              <p>诊断时间: ${this.currentDiagnosisTime}</p>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断信息</h2>\n              <div class=\"meta-info\">\n                <div class=\"meta-item\">\n                  <div class=\"meta-label\">数据文件</div>\n                  <div class=\"meta-value\">${reportData.data_file}</div>\n                </div>\n                <div class=\"meta-item\">\n                  <div class=\"meta-label\">使用模型</div>\n                  <div class=\"meta-value\">${reportData.model_used}</div>\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断流程</h2>\n              <div class=\"steps\">\n                ${reportData.steps.map((step, index) => `\n                  <div class=\"step\">\n                    <div class=\"step-title\">${index + 1}. ${step.description}</div>\n                    <div class=\"step-duration\">耗时: ${step.duration_ms} ms</div>\n                  </div>\n                `).join('')}\n              </div>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断结论</h2>\n              <div class=\"result\">\n                <div class=\"result-label\">诊断结果</div>\n                <div class=\"result-value ${conclusion.predicted_fault_mode.includes('fault') ? 'fault' : conclusion.predicted_fault_mode.includes('degradation') ? 'degradation' : 'normal'}\">\n                  ${this.getChineseFaultName(conclusion.predicted_fault_mode)}\n                </div>\n                ${conclusion.confidence_score ? `\n                <div class=\"confidence\">\n                  置信度: ${(conclusion.confidence_score * 100).toFixed(1)}%\n                </div>\n                ` : ''}\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `\n\n      // 创建Blob对象\n      const blob = new Blob([htmlContent], { type: 'text/html' })\n\n      // 创建下载链接\n      const link = document.createElement('a')\n      link.href = URL.createObjectURL(blob)\n      link.download = `28SY诊断报告_${this.currentDiagnosisTime.replace(/[: ]/g, '_')}.html`\n\n      // 触发下载\n      document.body.appendChild(link)\n      link.click()\n\n      // 清理\n      document.body.removeChild(link)\n    },\n\n    // 添加跳转到主控台页面的方法\n    viewInConsole() {\n      // 确保诊断结果已保存到Vuex\n      if (this.fmeaReport && this.fmeaReport.success) {\n        console.log('保存诊断结果到Vuex:', this.fmeaReport)\n        this.$store.dispatch('diagnosis/saveDiagnosisResult', this.fmeaReport)\n      }\n\n      // 关闭当前对话框\n      this.fmeaDialogVisible = false\n\n      // 跳转到主控台页面\n      this.$router.push('/')\n    },\n\n    // 添加保存诊断结果到历史数据的方法\n    saveDiagnosisToHistory(diagnosisResult) {\n      if (!diagnosisResult || !diagnosisResult.success) {\n        return\n      }\n\n      const conclusion = diagnosisResult.diagnosis_details.conclusion\n      const faultMode = conclusion.predicted_fault_mode\n\n      // 构造保存诊断数据的请求\n      this.$axios.post('/phm/saveDiagnosisData/', {\n        timestamp: new Date().toISOString(),\n        fault_mode: faultMode,\n        data_file: diagnosisResult.diagnosis_details.data_file,\n        model_used: diagnosisResult.diagnosis_details.model_used,\n        status: faultMode === '0_normal' ? '0' : faultMode.split('_')[0], // 使用故障模式的编号作为状态码\n        health_status: faultMode === '0_normal' ? '1' : '0' // 正常状态为1，其他为0\n      }).then(response => {\n        if (response.data.code === 200) {\n          console.log('已保存诊断结果到历史数据库')\n        } else {\n          console.error('保存诊断结果失败:', response.data.msg)\n        }\n      }).catch(error => {\n        console.error('保存诊断结果出错:', error)\n      })\n    },\n\n    viewHealthAssessment() {\n      // 确保诊断结果已保存到Vuex\n      if (this.fmeaReport && this.fmeaReport.success) {\n        console.log('保存诊断结果到Vuex:', this.fmeaReport)\n        this.$store.dispatch('diagnosis/saveDiagnosisResult', this.fmeaReport)\n      }\n\n      // 关闭当前对话框\n      this.fmeaDialogVisible = false\n\n      // 跳转到寿命预测与健康评估页面\n      this.$router.push('/life/index')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" >\n.diagnosis-container{\n  margin: 30px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);\n  padding: 24px;\n\n  /* 页面标题样式 */\n  .page-header {\n    margin-bottom: 32px;\n    text-align: center;\n\n    .page-title {\n      font-size: 2em;\n      font-weight: 700;\n      margin: 0;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n\n      i {\n        font-size: 1.2em;\n        color: #00d4ff;\n      }\n\n      .page-subtitle {\n        font-size: 0.4em;\n        color: #b8c5d1;\n        font-weight: 400;\n        margin-top: 8px;\n        letter-spacing: 1px;\n        display: block;\n      }\n    }\n  }\n\n  /* 模型管理标题样式 */\n  .model-management-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .model-management-title {\n      font-size: 18px;\n      font-weight: 600;\n      color: #00d4ff !important;\n      text-align: center;\n      flex: 1;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n    }\n\n    .upload-model-btn {\n      padding: 10px 20px !important;\n      font-size: 14px !important;\n      font-weight: 500 !important;\n      border-radius: 6px !important;\n      box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;\n      transition: all 0.3s ease !important;\n\n      &:hover {\n        transform: translateY(-1px) !important;\n        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;\n      }\n\n      i {\n        margin-right: 6px !important;\n      }\n    }\n  }\n}\n\n/* 模型管理表格样式 */\n.model-management-table {\n  background: rgba(47, 51, 73, 0.8) !important;\n\n  .el-table__header-wrapper {\n    .el-table__header {\n      th {\n        background: rgba(47, 51, 73, 0.8) !important;\n        border: 1px solid rgba(255, 255, 255, 0.1) !important;\n        color: #ffffff !important;\n        font-weight: normal;\n      }\n    }\n  }\n\n  .el-table__body-wrapper {\n    background: rgba(47, 51, 73, 0.8) !important;\n\n    .el-table__body {\n      background: rgba(47, 51, 73, 0.8) !important;\n\n      tr {\n        background: rgba(47, 51, 73, 0.8) !important;\n\n        &:hover {\n          background: rgba(0, 212, 255, 0.1) !important;\n        }\n\n        &.el-table__row--striped {\n          background: rgba(47, 51, 73, 0.6) !important;\n\n          &:hover {\n            background: rgba(0, 212, 255, 0.1) !important;\n          }\n        }\n      }\n\n      td {\n        border: 1px solid rgba(255, 255, 255, 0.1) !important;\n        color: #b8c5d1 !important;\n        background: transparent !important;\n      }\n    }\n  }\n\n  .el-table__border-left-patch,\n  .el-table__border-right-patch {\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table--border {\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table--border::after {\n    background-color: rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table--border td,\n  .el-table--border th {\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table--border th.gutter:last-of-type {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table__empty-block {\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  /* 禁用列宽调整功能 */\n  .el-table th.is-leaf {\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table .el-table__header-wrapper .el-table__header th .cell {\n    cursor: default !important;\n  }\n\n  .el-table .el-table__header-wrapper .el-table__header th:hover .cell {\n    cursor: default !important;\n  }\n\n  .el-table th.gutter {\n    display: none !important;\n  }\n\n  /* 四列均匀分布 */\n  .model-management-table .el-table__header-wrapper .el-table__header colgroup col {\n    width: 25% !important;\n  }\n\n  .model-management-table .el-table__body-wrapper .el-table__body colgroup col {\n    width: 25% !important;\n  }\n\n  .model-management-table .el-table__header th,\n  .model-management-table .el-table__body td {\n    width: 25% !important;\n    min-width: 25% !important;\n    max-width: 25% !important;\n  }\n}\n\n.box-card {\n  background: rgba(47, 51, 73, 0.8) !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(0, 212, 255, 0.2) !important;\n  border-radius: 12px !important;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\n  margin-bottom: 24px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: rgba(0, 212, 255, 0.4) !important;\n    box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3) !important;\n    transform: translateY(-2px);\n  }\n}\n.selectCard .el-form-item__label {\n  font-size: 17px;\n  font-weight: 600;\n  color: #ffffff !important;\n}\n\n.dignosisReport .el-dialog.diaReport{\n  border-radius: 15px;\n  width: 80%;\n}\n\n.report .el-form-item__label {\n  font-size: 21px;\n  font-family: KaiTi;\n  color: #ffffff !important;\n}\n.report .el-form-item__content {\n  width: 82%;\n  color: #b8c5d1 !important;\n}\n\n.probability-distribution {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 10px 20px;\n}\n.prob-item {\n  display: flex;\n  align-items: center;\n}\n.prob-label {\n  width: 300px; /* 根据最长标签调整 */\n  margin-right: 10px;\n  text-align: right;\n  font-size: 14px;\n  color: #b8c5d1 !important;\n}\n\n/* 诊断报告弹窗样式优化 */\n.diagnosis-report-dialog {\n  background: rgba(47, 51, 73, 0.8) !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(0, 212, 255, 0.2) !important;\n  border-radius: 16px !important;\n\n  .el-dialog__body {\n    padding: 20px 30px;\n    background: transparent !important;\n    color: #b8c5d1 !important;\n  }\n\n  .el-dialog__header {\n    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    border-radius: 16px 16px 0 0;\n\n    .el-dialog__title {\n      color: #ffffff !important;\n      font-weight: 600;\n    }\n  }\n}\n\n.diagnosis-report-content {\n  font-family: \"Microsoft YaHei\", Arial, sans-serif;\n  color: #b8c5d1 !important;\n\n    .report-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    margin-bottom: 25px;\n    padding-bottom: 20px;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    flex-wrap: wrap;\n\n    @media (max-width: 968px) {\n      flex-direction: column;\n\n      .diagnosis-result {\n        margin-left: 0;\n        margin-top: 15px;\n        width: 100%;\n        max-width: none;\n      }\n    }\n  }\n\n    .report-meta {\n    flex: 2;\n    max-width: 65%;\n\n    .meta-item {\n      margin-bottom: 12px;\n      font-size: 14px;\n      line-height: 1.5;\n      display: flex;\n      align-items: baseline;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .meta-label {\n        color: #b8c5d1 !important;\n        margin-right: 12px;\n        font-weight: 500;\n        min-width: 90px;\n\n        i {\n          margin-right: 5px;\n          color: #00d4ff;\n        }\n      }\n\n      .meta-value {\n        color: #ffffff !important;\n        word-break: break-word;\n        flex: 1;\n        font-family: Consolas, Monaco, monospace;\n      }\n    }\n\n    @media (max-width: 968px) {\n      max-width: 100%;\n    }\n  }\n\n  .diagnosis-result {\n    flex: 1;\n    background: #f9f9f9;\n    border-radius: 8px;\n    padding: 15px 20px;\n    box-shadow: 0 2px 6px rgba(0,0,0,0.05);\n    min-width: 250px;\n    max-width: 400px;\n    margin-left: 20px;\n\n    .result-title {\n      font-size: 16px;\n      font-weight: bold;\n      color: #303133;\n      margin-bottom: 15px;\n      text-align: center;\n    }\n\n    .result-value {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n\n      .result-tag {\n        font-size: 18px;\n        padding: 10px 20px;\n        font-weight: bold;\n        margin-bottom: 10px;\n        width: 100%;\n        text-align: center;\n        white-space: normal;\n        height: auto;\n        line-height: 1.5;\n      }\n\n      .confidence-score {\n        font-size: 14px;\n        color: #606266;\n        margin-top: 5px;\n      }\n    }\n  }\n\n  .diagnosis-steps-section {\n    margin-bottom: 25px;\n\n    .section-title {\n      font-size: 16px;\n      font-weight: bold;\n      margin-bottom: 15px;\n      color: #303133;\n    }\n\n    .diagnostic-steps {\n      padding: 0 10px;\n    }\n  }\n\n  .report-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 15px;\n    margin-top: 25px;\n\n    .el-button {\n      padding: 10px 20px;\n    }\n  }\n}\n</style>\n"]}]}