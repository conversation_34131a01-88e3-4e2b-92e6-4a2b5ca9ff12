{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\sensor-monitor\\index.vue?vue&type=style&index=0&id=23c2ed24&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\sensor-monitor\\index.vue", "mtime": 1754202095623}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1634626957199}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1634627893377}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1634627525156}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1634627658274}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAifkAvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzIjsKCi5zZW5zb3ItbW9uaXRvci1jb250YWluZXIgewogIHBhZGRpbmc6IDI0cHg7CiAgbWluLWhlaWdodDogMTAwdmg7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJGJnUHJpbWFyeSAwJSwgJGJnU2Vjb25kYXJ5IDEwMCUpOwp9CgovKiDpobXpnaLmoIfpopjmoLflvI8gKi8KLnBhZ2UtaGVhZGVyIHsKICBtYXJnaW4tYm90dG9tOiAzMnB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKCiAgLnBhZ2UtdGl0bGUgewogICAgZm9udC1zaXplOiAyZW07CiAgICBmb250LXdlaWdodDogNzAwOwogICAgbWFyZ2luOiAwOwogICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJHRlY2hCbHVlLCAkdGVjaEJsdWVMaWdodCk7CiAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDsKICAgIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDsKICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICBnYXA6IDEycHg7CgogICAgaSB7CiAgICAgIGZvbnQtc2l6ZTogMS4yZW07CiAgICAgIGNvbG9yOiAkdGVjaEJsdWU7CiAgICB9CgogICAgLnBhZ2Utc3VidGl0bGUgewogICAgICBmb250LXNpemU6IDAuNGVtOwogICAgICBjb2xvcjogJHRleHRTZWNvbmRhcnk7CiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7CiAgICAgIG1hcmdpbi10b3A6IDhweDsKICAgICAgbGV0dGVyLXNwYWNpbmc6IDFweDsKICAgICAgZGlzcGxheTogYmxvY2s7CiAgICB9CiAgfQp9CgovKiDmjqfliLbpnaLmnb/moLflvI8gKi8KLmNvbnRyb2wtcGFuZWwgewogIG1hcmdpbi1ib3R0b206IDMycHg7CgogIC5jYXJkLWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICB9CgogIC5mb3JtLWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogMDsKCiAgICAuZm9ybS1sYWJlbCB7CiAgICAgIGNvbG9yOiAkdGV4dFNlY29uZGFyeTsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBmb250LXdlaWdodDogNTAwOwogICAgICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgICAgIGRpc3BsYXk6IGJsb2NrOwogICAgfQoKICAgIC5idXR0b24tZ3JvdXAgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBnYXA6IDEycHg7CiAgICAgIGZsZXgtd3JhcDogd3JhcDsKICAgIH0KICB9Cn0KCi8qIOaVsOaNruWxleekuuWMuuWfnyAqLwouZGF0YS1kaXNwbGF5LWFyZWEgewogIG1hcmdpbi1ib3R0b206IDMycHg7Cn0KCi8qIOWbvuihqOWuueWZqOagt+W8jyAqLwouY2hhcnQtY29udGFpbmVyIHsKICAuY2hhcnQtaW5mbyB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGdhcDogMTZweDsKCiAgICAuc2FtcGxpbmctcmF0ZSB7CiAgICAgIGNvbG9yOiAkdGV4dFNlY29uZGFyeTsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBwYWRkaW5nOiA0cHggMTJweDsKICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAyMTIsIDI1NSwgMC4xKTsKICAgICAgYm9yZGVyLXJhZGl1czogMTJweDsKICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsKICAgIH0KICB9Cn0KCi5jaGFydC13cmFwcGVyIHsKICBoZWlnaHQ6IDQ1MHB4OwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKCiAgJjo6YmVmb3JlIHsKICAgIGNvbnRlbnQ6ICcnOwogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiAwOwogICAgbGVmdDogMDsKICAgIHJpZ2h0OiAwOwogICAgYm90dG9tOiAwOwogICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLAogICAgICByZ2JhKDAsIDIxMiwgMjU1LCAwLjAyKSAwJSwKICAgICAgdHJhbnNwYXJlbnQgNTAlLAogICAgICByZ2JhKDI1NSwgMTA3LCA1MywgMC4wMikgMTAwJSk7CiAgICBwb2ludGVyLWV2ZW50czogbm9uZTsKICAgIGJvcmRlci1yYWRpdXM6IDhweDsKICB9Cn0KCi8qIOeOsOS7o+WMlumAieaLqeWZqOagt+W8jyAqLwoubW9kZXJuLXNlbGVjdCB7CiAgOmRlZXAoLmVsLWlucHV0X19pbm5lcikgewogICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KSAhaW1wb3J0YW50OwogICAgYm9yZGVyOiAxcHggc29saWQgJGJvcmRlclNlY29uZGFyeSAhaW1wb3J0YW50OwogICAgYm9yZGVyLXJhZGl1czogOHB4ICFpbXBvcnRhbnQ7CiAgICBjb2xvcjogJHRleHRQcmltYXJ5ICFpbXBvcnRhbnQ7CiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlICFpbXBvcnRhbnQ7CgogICAgJjpmb2N1cyB7CiAgICAgIGJvcmRlci1jb2xvcjogJHRlY2hCbHVlICFpbXBvcnRhbnQ7CiAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDAsIDIxMiwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7CiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCkgIWltcG9ydGFudDsKICAgIH0KICB9Cn0KCi8qIOWTjeW6lOW8j+iuvuiuoSAqLwpAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7CiAgLmRhdGEtZGlzcGxheS1hcmVhIHsKICAgIC5lbC1jb2wgewogICAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgfQogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7CiAgLnNlbnNvci1tb25pdG9yLWNvbnRhaW5lciB7CiAgICBwYWRkaW5nOiAxNnB4OwogIH0KCiAgLnBhZ2UtaGVhZGVyIC5wYWdlLXRpdGxlIHsKICAgIGZvbnQtc2l6ZTogMS41ZW07CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgZ2FwOiA4cHg7CiAgfQoKICAuY29udHJvbC1wYW5lbCAuY2FyZC1jb250ZW50IHsKICAgIC5lbC1yb3cgLmVsLWNvbCB7CiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7CiAgICB9CiAgfQoKICAuY2hhcnQtd3JhcHBlciB7CiAgICBoZWlnaHQ6IDMwMHB4OwogIH0KCiAgLmJ1dHRvbi1ncm91cCB7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkcA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/sensor-monitor", "sourcesContent": ["<template>\n  <div class=\"sensor-monitor-container modern-theme\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-monitor\"></i>\n        设备状态监测\n        <span class=\"page-subtitle\">Real-time Device Status Monitoring</span>\n      </h2>\n    </div>\n\n    <!-- 控制面板 -->\n    <div class=\"control-panel modern-card\">\n      <div class=\"card-header\">\n        <h3 class=\"card-title\">监测控制台</h3>\n        <div class=\"status-indicator\" :class=\"isCollecting ? 'online' : 'offline'\">\n          {{ isCollecting ? '采集中' : '待机' }}\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"6\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">传感器类型</label>\n              <el-select\n                v-model=\"selectedSensorType\"\n                :disabled=\"isCollecting\"\n                placeholder=\"请选择传感器类型\"\n                style=\"width: 100%\"\n                class=\"modern-select\"\n              >\n                <el-option\n                  v-for=\"item in sensorTypes\"\n                  :key=\"item\"\n                  :label=\"getSensorTypeLabel(item)\"\n                  :value=\"item\"\n                >\n                </el-option>\n              </el-select>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">采样频率</label>\n              <el-select\n                v-model=\"selectedSamplingRate\"\n                :disabled=\"isCollecting\"\n                placeholder=\"请选择采样频率\"\n                style=\"width: 100%\"\n                class=\"modern-select\"\n              >\n                <el-option\n                  v-for=\"item in samplingRates\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">操作控制</label>\n              <div class=\"button-group\">\n                <el-button\n                  type=\"primary\"\n                  :disabled=\"isCollecting || !selectedSensorType\"\n                  @click=\"startCollection\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-video-play\"></i>\n                  开始采集\n                </el-button>\n                <el-button\n                  type=\"danger\"\n                  :disabled=\"!isCollecting\"\n                  @click=\"stopCollection\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-video-pause\"></i>\n                  停止采集\n                </el-button>\n                <el-button\n                  type=\"success\"\n                  :disabled=\"!hasData\"\n                  @click=\"downloadData\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-download\"></i>\n                  下载数据\n                </el-button>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n    </div>\n\n    <!-- 数据展示区域 -->\n    <div class=\"data-display-area\">\n      <el-row :gutter=\"24\">\n        <!-- 实时数据指标 -->\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">当前值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ currentValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n            <div class=\"metric-chart\">\n              <!-- 迷你图表占位 -->\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">平均值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ averageValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">最大值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ maxValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">数据点数</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ chartData.values.length }}</span>\n              <span class=\"unit\">个</span>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 图表展示区域 -->\n    <div class=\"chart-container modern-card\">\n      <div class=\"card-header\">\n        <h3 class=\"card-title\">\n          <i class=\"el-icon-data-line\"></i>\n          {{ getSensorTypeLabel(selectedSensorType) }} 实时数据\n        </h3>\n        <div class=\"chart-info\">\n          <span class=\"sampling-rate\">采样频率: {{ getSelectedSamplingRateLabel() }}</span>\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"chart-wrapper\">\n          <sensor-chart\n            ref=\"sensorChart\"\n            :chart-data=\"chartData\"\n            :sensor-type=\"selectedSensorType\"\n          ></sensor-chart>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport SensorChart from './components/SensorChart'\n\nexport default {\n  name: 'DeviceStatusMonitor',\n  components: {\n    SensorChart\n  },\n  data() {\n    return {\n      sensorTypes: [],\n      selectedSensorType: '',\n      isCollecting: false,\n      chartData: {\n        timestamps: [],\n        values: []\n      },\n      dataFetchTimer: null,\n      maxDataPoints: 60, // 最多显示60个数据点\n      hasData: false,\n      samplingRates: [\n        { value: 50, label: '20Hz (50ms)' },\n        { value: 100, label: '10Hz (100ms)' },\n        { value: 200, label: '5Hz (200ms)' },\n        { value: 500, label: '2Hz (500ms)' },\n        { value: 1000, label: '1Hz (1000ms)' }\n      ],\n      selectedSamplingRate: 100 // 默认10Hz (100ms)\n    }\n  },\n  computed: {\n    // 当前值\n    currentValue() {\n      if (this.chartData.values.length === 0) return '--'\n      return this.chartData.values[this.chartData.values.length - 1].toFixed(3)\n    },\n\n    // 平均值\n    averageValue() {\n      if (this.chartData.values.length === 0) return '--'\n      const sum = this.chartData.values.reduce((a, b) => a + b, 0)\n      return (sum / this.chartData.values.length).toFixed(3)\n    },\n\n    // 最大值\n    maxValue() {\n      if (this.chartData.values.length === 0) return '--'\n      return Math.max(...this.chartData.values).toFixed(3)\n    }\n  },\n  created() {\n    this.fetchSensorTypes()\n  },\n  beforeDestroy() {\n    this.clearTimer()\n  },\n  methods: {\n    // 获取传感器类型列表\n    fetchSensorTypes() {\n      this.$axios.get('/phm/get_sensor_types/').then(response => {\n        if (response.data.code === 200) {\n          this.sensorTypes = response.data.data.sensor_types\n          if (this.sensorTypes.length > 0) {\n            this.selectedSensorType = this.sensorTypes[0]\n          }\n        } else {\n          this.$message.error('获取传感器类型失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('获取传感器类型出错: ' + error.message)\n      })\n    },\n\n    // 开始数据采集\n    startCollection() {\n      if (!this.selectedSensorType) {\n        this.$message.warning('请先选择传感器类型')\n        return\n      }\n\n      // 重置图表数据\n      this.chartData = {\n        timestamps: [],\n        values: []\n      }\n\n      // 发送开始采集请求\n      this.$axios.post('/phm/start_data_collection/', {\n        sensor_type: this.selectedSensorType,\n        sampling_rate: this.selectedSamplingRate\n      }).then(response => {\n        if (response.data.code === 200) {\n          this.isCollecting = true\n          this.$message.success('开始采集数据')\n          this.startDataFetching()\n        } else {\n          this.$message.error('开始采集失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('开始采集出错: ' + error.message)\n      })\n    },\n\n    // 停止数据采集\n    stopCollection() {\n      this.$axios.post('/phm/stop_data_collection/').then(response => {\n        if (response.data.code === 200) {\n          this.isCollecting = false\n          this.$message.success('停止采集数据')\n          this.clearTimer()\n          this.hasData = this.chartData.values.length > 0\n        } else {\n          this.$message.error('停止采集失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('停止采集出错: ' + error.message)\n      })\n    },\n\n    // 开始定时获取数据\n    startDataFetching() {\n      this.clearTimer()\n      this.dataFetchTimer = setInterval(() => {\n        this.fetchSensorData()\n      }, this.selectedSamplingRate) // 使用选定的采样间隔\n    },\n\n    // 获取传感器数据\n    fetchSensorData() {\n      this.$axios.get('/phm/get_simulated_data/').then(response => {\n        if (response.data.code === 200) {\n          const data = response.data.data\n\n          if (data.status === 'collecting') {\n            // 添加新数据点\n            this.chartData.timestamps.push(...data.timestamps)\n            this.chartData.values.push(...data.values)\n\n            // 限制数据点数量\n            if (this.chartData.timestamps.length > this.maxDataPoints) {\n              const excess = this.chartData.timestamps.length - this.maxDataPoints\n              this.chartData.timestamps.splice(0, excess)\n              this.chartData.values.splice(0, excess)\n            }\n\n            // 更新图表\n            if (this.$refs.sensorChart) {\n              this.$refs.sensorChart.updateChart()\n            }\n\n            this.hasData = true\n\n            // 自动保存数据到历史记录\n            this.saveDataToHistory(data.timestamps, data.values)\n          } else if (data.status === 'not_collecting') {\n            // 如果服务器端停止了采集，客户端也应停止\n            if (this.isCollecting) {\n              this.isCollecting = false\n              this.clearTimer()\n              this.$message.info('服务器已停止数据采集')\n            }\n          }\n        } else {\n          this.$message.error('获取数据失败: ' + response.data.msg)\n          this.clearTimer()\n          this.isCollecting = false\n        }\n      }).catch(error => {\n        this.$message.error('获取数据出错: ' + error.message)\n        this.clearTimer()\n        this.isCollecting = false\n      })\n    },\n\n    // 保存数据到历史记录\n    saveDataToHistory(timestamps, values) {\n      if (!timestamps || !values || timestamps.length === 0 || values.length === 0) {\n        return\n      }\n\n      // 每次只发送最新的数据点\n      const latestIndex = timestamps.length - 1\n\n      // 构造保存数据的请求\n      this.$axios.post('/phm/saveMonitorData/', {\n        timestamp: timestamps[latestIndex],\n        value: values[latestIndex],\n        sensor_type: this.selectedSensorType,\n        status: '0', // 默认状态为正常\n        health_status: '1' // 默认健康状态为正常\n      }).then(response => {\n        if (response.data.code !== 200) {\n          console.error('保存监测数据失败:', response.data.msg)\n        }\n      }).catch(error => {\n        console.error('保存监测数据出错:', error)\n      })\n    },\n\n    // 清除定时器\n    clearTimer() {\n      if (this.dataFetchTimer) {\n        clearInterval(this.dataFetchTimer)\n        this.dataFetchTimer = null\n      }\n    },\n\n    // 下载数据\n    downloadData() {\n      if (!this.hasData) {\n        this.$message.warning('没有可下载的数据')\n        return\n      }\n\n      this.$axios.post('/phm/download_sensor_data/', {\n        sensor_type: this.selectedSensorType,\n        values: this.chartData.values,\n        timestamps: this.chartData.timestamps\n      }, {\n        responseType: 'blob' // 指定响应类型为二进制数据\n      }).then(response => {\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n        link.setAttribute('download', `${this.selectedSensorType}_data_${new Date().toISOString().replace(/[:.]/g, '_')}.xlsx`)\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n      }).catch(error => {\n        this.$message.error('下载数据出错: ' + error.message)\n      })\n    },\n\n    // 获取传感器类型的显示标签\n    getSensorTypeLabel(type) {\n      const labels = {\n        'position': '位置传感器',\n        'current': '电流传感器',\n        'setpoint': '指令位移传感器'\n      }\n      return labels[type] || type\n    },\n\n    // 获取当前选择的采样频率标签\n    getSelectedSamplingRateLabel() {\n      const rate = this.samplingRates.find(item => item.value === this.selectedSamplingRate)\n      return rate ? rate.label : `${this.selectedSamplingRate}ms`\n    },\n\n    // 获取传感器单位\n    getUnit(type) {\n      const units = {\n        'position': 'mm',\n        'current': 'A',\n        'setpoint': 'mm'\n      }\n      return units[type] || ''\n    },\n\n    // 清除定时器\n    clearTimer() {\n      if (this.dataFetchTimer) {\n        clearInterval(this.dataFetchTimer)\n        this.dataFetchTimer = null\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n.sensor-monitor-container {\n  padding: 24px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);\n}\n\n/* 页面标题样式 */\n.page-header {\n  margin-bottom: 32px;\n  text-align: center;\n\n  .page-title {\n    font-size: 2em;\n    font-weight: 700;\n    margin: 0;\n    background: linear-gradient(135deg, $techBlue, $techBlueLight);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12px;\n\n    i {\n      font-size: 1.2em;\n      color: $techBlue;\n    }\n\n    .page-subtitle {\n      font-size: 0.4em;\n      color: $textSecondary;\n      font-weight: 400;\n      margin-top: 8px;\n      letter-spacing: 1px;\n      display: block;\n    }\n  }\n}\n\n/* 控制面板样式 */\n.control-panel {\n  margin-bottom: 32px;\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  .form-item {\n    margin-bottom: 0;\n\n    .form-label {\n      color: $textSecondary;\n      font-size: 14px;\n      font-weight: 500;\n      margin-bottom: 8px;\n      display: block;\n    }\n\n    .button-group {\n      display: flex;\n      gap: 12px;\n      flex-wrap: wrap;\n    }\n  }\n}\n\n/* 数据展示区域 */\n.data-display-area {\n  margin-bottom: 32px;\n}\n\n/* 图表容器样式 */\n.chart-container {\n  .chart-info {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n\n    .sampling-rate {\n      color: $textSecondary;\n      font-size: 14px;\n      padding: 4px 12px;\n      background: rgba(0, 212, 255, 0.1);\n      border-radius: 12px;\n      border: 1px solid rgba(0, 212, 255, 0.2);\n    }\n  }\n}\n\n.chart-wrapper {\n  height: 450px;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg,\n      rgba(0, 212, 255, 0.02) 0%,\n      transparent 50%,\n      rgba(255, 107, 53, 0.02) 100%);\n    pointer-events: none;\n    border-radius: 8px;\n  }\n}\n\n/* 现代化选择器样式 */\n.modern-select {\n  :deep(.el-input__inner) {\n    background: rgba(255, 255, 255, 0.05) !important;\n    border: 1px solid $borderSecondary !important;\n    border-radius: 8px !important;\n    color: $textPrimary !important;\n    transition: all 0.3s ease !important;\n\n    &:focus {\n      border-color: $techBlue !important;\n      box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1) !important;\n      background: rgba(255, 255, 255, 0.08) !important;\n    }\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .data-display-area {\n    .el-col {\n      margin-bottom: 16px;\n    }\n  }\n}\n\n@media (max-width: 768px) {\n  .sensor-monitor-container {\n    padding: 16px;\n  }\n\n  .page-header .page-title {\n    font-size: 1.5em;\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .control-panel .card-content {\n    .el-row .el-col {\n      margin-bottom: 16px;\n    }\n  }\n\n  .chart-wrapper {\n    height: 300px;\n  }\n\n  .button-group {\n    justify-content: center;\n  }\n}\n</style>\n"]}]}