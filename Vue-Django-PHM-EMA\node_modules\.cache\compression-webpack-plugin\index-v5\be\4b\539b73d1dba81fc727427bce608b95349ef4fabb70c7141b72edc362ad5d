
834ba280611447cfb74eb23b878623eef44d286d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"app.1d3b79c5bfce5223ddad.hot-update.js\",\"contentHash\":\"6ea3e4becf0a61ac4deaf7674cd45bb2\"}","integrity":"sha512-fL169KR0jf+y6sBCxje0HVOx6S4xj/LcqS5DWNtdPvStwwX1p1csvqmeS3EfDdPEgGAsNSnN+gxCVq7YILvFQw==","time":1754201792378,"size":51667}