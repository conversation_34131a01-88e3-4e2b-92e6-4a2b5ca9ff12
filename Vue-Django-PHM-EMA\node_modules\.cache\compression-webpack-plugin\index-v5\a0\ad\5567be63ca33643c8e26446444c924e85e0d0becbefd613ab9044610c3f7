
71fee8f206cf4659005ceba4b840a8496c92c10f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"70d231b35f1ba7dbda02791b26c5526a\"}","integrity":"sha512-Qp3zYaFioWzm7jM83/xqeoAuRlI1Y2H15NKDFxkyAFWVxDM5y/TuxPfg49+CnhWWA9Hq9zOIdhAzTXRvQ7GDmQ==","time":1754203186514,"size":23474}