
136933cd5c1ef0e4706f0cf090aedd06acdf08cf	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"32005fe7f5cf6be05ee5318bb43a57f6\"}","integrity":"sha512-U/uuDQSUpfWqOcn+ODtoYQWS7rm3TksYpQlfEeeI9y2kRcqCDIy+iZwUKEvxWOl1XBjtzXWute84qzuIb6KG+w==","time":1754204764921,"size":82951}