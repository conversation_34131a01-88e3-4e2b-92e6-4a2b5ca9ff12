
88cd31e7f55c74ee54211d3646f865352ace1989	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"2.8f8f6e3d8ea28be5fc05.hot-update.js\",\"contentHash\":\"ac72920d1759f90ee9b97648b47624e1\"}","integrity":"sha512-QaaFtJFV47r7vMR/j0UcKGPze9nyUZyUBzNRHrgV1sWiiKv5/q8e76EP0bx8wMInFKuGi4GbKhD4f2wNMkZ+qw==","time":1754206025948,"size":68418}