
80a1024edd2d9b9ea8d4467d6498d8e4ef322438	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"f737d701bf1a8651e1aaa0fa0ebeae4a\"}","integrity":"sha512-VoeUzQ/5Ra6zeiCOHwfx1WjBR3SszMqbkYbNZ9lHKz0bt0SVyWgvcrijPrgAUC6OEIhePDR1uaQ+d2v26NbATA==","time":1754204194488,"size":26734}