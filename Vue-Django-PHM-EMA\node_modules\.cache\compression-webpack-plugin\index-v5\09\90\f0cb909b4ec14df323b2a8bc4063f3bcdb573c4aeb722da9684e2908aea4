
6f0b43242ab75f3072417b8c11ec36df3e297a36	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.75d6c297b947e982c763.hot-update.js\",\"contentHash\":\"fb69c8156b36b92530da50d0873d3435\"}","integrity":"sha512-OUYUG7Zqq0O4VqGEo3Zu1VvepOgQt5N80/vwpydVT5oce/hxsfYGfB6LJmqXRRy86v0dIg8MmQM2LTVZSqx8Kw==","time":1754203664264,"size":87308}