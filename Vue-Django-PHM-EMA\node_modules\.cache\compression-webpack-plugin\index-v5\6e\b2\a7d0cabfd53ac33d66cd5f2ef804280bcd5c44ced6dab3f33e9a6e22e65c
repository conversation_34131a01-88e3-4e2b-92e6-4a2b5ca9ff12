
203dd59515753fe5d52db8d9d73b8552aeee09c7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"0da117fa796d5016cda3ae095b2da399\"}","integrity":"sha512-xSj8mMZ9NTXVuPWULN1ceu2EfE7hdcOzJs4GbrUW5BnDAhP7Zh4sKGW9URdSzOsm+JVBhHVrcf9DzJjz43Np/w==","time":1754205765820,"size":26685}