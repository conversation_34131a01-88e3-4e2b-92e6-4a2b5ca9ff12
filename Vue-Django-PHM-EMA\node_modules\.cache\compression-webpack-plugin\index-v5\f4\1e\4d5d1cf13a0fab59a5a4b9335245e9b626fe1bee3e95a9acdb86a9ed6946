
3c5763af278bacee59a9688b1d10fd8b77536391	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"94284de82159798c694869f337bedddb\"}","integrity":"sha512-FgfhBzGKHIFTGAFTV+Lr6yQo/ntvEj7EC24Ocj0cPpL+7aFJsN564RdM90MvzMiOQK5e+HOTcw9cySqozdyucQ==","time":1754202879318,"size":26728}