
ca6537b766e7e555cea93d6a8336cdcab95a33ee	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.e5d64167039ce224d5c8.hot-update.js\",\"contentHash\":\"c994e3a97a3fe353e524bbe220249674\"}","integrity":"sha512-IXweEhHkkVLomwJZLhINhFEA8WLUd2FUSbefl83I6fAJBN7F6cf2qDr9WNZSdNFhxWfrTkZvnYjnVXifiBZu3g==","time":1754204006980,"size":24057}