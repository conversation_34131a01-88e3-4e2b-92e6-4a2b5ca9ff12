
5c83eab9bbba646b950b803f3763ced0a5a2aa28	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.9f89efa3e965a868d734.hot-update.js\",\"contentHash\":\"40de1bff0c23ae949dfab1132897be23\"}","integrity":"sha512-BKj76NI7Ao8a6ArasSsBa7HNAa5FV1XsOgmJ/MiNFhibwXRrmnnwj5D53Zzltlx3zRimKF2AxS9jgBW7lIOceg==","time":1754202908655,"size":30329}