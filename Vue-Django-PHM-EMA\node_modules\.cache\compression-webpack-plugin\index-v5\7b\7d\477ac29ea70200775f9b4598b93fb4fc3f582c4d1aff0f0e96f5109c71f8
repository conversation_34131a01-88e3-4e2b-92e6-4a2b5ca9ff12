
cbcf090f98e505968c1cca7aa4ec8d74dcd492ce	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"26a4fabd9b7f92d8a3cb433244a53329\"}","integrity":"sha512-dRUgrUvxQvHCbR9iI+tLh7YiiHCniuWJJFoO4ncQ3A5sQCwJqiMS1HMUou7Jl5C/g7R4/guf0n0XztrSN/W4gw==","time":1754204482801,"size":26685}