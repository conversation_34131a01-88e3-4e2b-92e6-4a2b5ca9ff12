
b92dfa1132c698f67307c0078aaeda2b6a8e0421	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"9179af5130ef201bd50543b657200f01\"}","integrity":"sha512-+L9nQekVXsWji2s5zrg6KaVgv2z9heTmFrqajQox+wvG0aX9RAiZZDeuIEG/YQg7A4kjNBehDwAfk3rV7Aww2w==","time":1754203462942,"size":147805}