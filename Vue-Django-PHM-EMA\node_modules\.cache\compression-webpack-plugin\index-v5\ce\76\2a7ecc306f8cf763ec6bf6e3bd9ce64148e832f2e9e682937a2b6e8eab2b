
fdd12c45a32c85741640b0a8db10784ebaac5ec2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"487188cfb4c33e2f0d732d2cc2a23aef\"}","integrity":"sha512-3Y9B/VZwnT7yRR7yoY2RIbri2MXiUFa8CpwF92OdMbtNQQ7/SACoHbdq2TPtj6QXxxPCgIR5KtV5RMPUJIjZbA==","time":1754202098575,"size":23556}