
2ebfc70b96085971a0a9ac66141616af038624ad	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.e97ef3dad7103826a76a.hot-update.js\",\"contentHash\":\"e7f17a558337a0eb6a1feeaf7882ddf6\"}","integrity":"sha512-Q6II1Xlj884EyVqicbOpFi8cit8B9l6J37heVLLtvCx8mKFVIhNLeRKEiFyynnGH9Cnf+CVmdMghD6dG+S3Omw==","time":1754204881559,"size":15520}