
8e517ddd77dafffac6bea7b863c1b1123cb44b61	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"f6066f8a40b0c6b94b3ed8a5380d92b5\"}","integrity":"sha512-HStNzzFTiaoPsf53/gXVtdZTJgmWNvSySK94wttrtrwfou3mb5ywjQB2tC7VsKZ2VCafKXv6+JZIc9NnfeyFDA==","time":1754202922250,"size":26743}