
10e686b91a2199009c182280bc35d8fe25e80548	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"5c20c28c94768c574dcfff6aa039efdf\"}","integrity":"sha512-vB6wIiUBUKkwdGB5s7DSG9Zcx8Eh3BLIkroFOhib3hV/eg/cvazqtT4QUeIcFVbp24pJO807r8H3sXcMLcKNtQ==","time":1754204881937,"size":89993}