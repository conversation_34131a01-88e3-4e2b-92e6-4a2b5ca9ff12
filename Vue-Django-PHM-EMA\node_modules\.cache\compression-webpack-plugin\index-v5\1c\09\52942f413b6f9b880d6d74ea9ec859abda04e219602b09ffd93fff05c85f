
c196724be0b7ffa7f869ed1557e78a1c84ae8810	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"42b2ca505c43f3981c7619b0bc42c800\"}","integrity":"sha512-6pUN3ZnLRKU1l48L67uRybGz5JL2xP0IiBLm39gzdeI1KPvjdttAXx3RUQG02koS+ih/ZZcJLY6gOo7+vtjVag==","time":1754204173621,"size":116345}