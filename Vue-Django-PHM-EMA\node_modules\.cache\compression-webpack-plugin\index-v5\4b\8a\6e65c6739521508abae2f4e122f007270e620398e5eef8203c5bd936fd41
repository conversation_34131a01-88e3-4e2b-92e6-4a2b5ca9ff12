
d7a2cc0c8f95ed92e86f3b47898e8ce847655543	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.17be21ec1c9f242bc38e.hot-update.js\",\"contentHash\":\"7739fa95d37cc2a790c74179fd2e71f5\"}","integrity":"sha512-8yEgE921Lh6CYH7ONjnnQSOBYjhkELJIx8AES5NGhedOgFSIyyzfQgxyErDzSW2YSdL2IPZn29vyg38XJYGHlw==","time":1754204504771,"size":26798}