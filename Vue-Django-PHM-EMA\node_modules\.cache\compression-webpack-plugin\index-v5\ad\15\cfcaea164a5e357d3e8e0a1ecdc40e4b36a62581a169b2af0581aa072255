
6b4b3d97a1de0f93650c4a47747e3964158e43cc	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.b51bac6574f99c3443f0.hot-update.js\",\"contentHash\":\"667da60c2b32e865a8dc6ac001ea8ae0\"}","integrity":"sha512-2/7Uy9jrhF5bH9dNpJJ8DOR6vxZQhtUqCkX+UpRjkZ+xZUIR852TMB96nazDOHCl65UTkNj3HghvJc/AjxWeew==","time":1754203859465,"size":23472}