
bd7ca95082add8190ac5ae05e8520bf921e9a762	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.00a632a0ccef64f9fd63.hot-update.js\",\"contentHash\":\"3d9cd83e88566134a61debd0b470302c\"}","integrity":"sha512-0mDJ6Smr3aS72zGdfNugGlsIKurZIf138w6T9bSC7in6ZiskYSQXSrQhFdqWfJ4+QrbGq6vA/Y+TQMeHah0SHQ==","time":1754202893182,"size":35648}