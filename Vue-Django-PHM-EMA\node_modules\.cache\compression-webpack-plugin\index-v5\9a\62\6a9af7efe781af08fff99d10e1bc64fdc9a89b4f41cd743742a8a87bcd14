
73c2430b7087741da5d58daedd3505fb0d5e4c7d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"2a94c40b521071165ded313d4c66a536\"}","integrity":"sha512-83ezDrPUJtqiaEuEf2xezdQgd6Dmd8HfOs5t+o3Pm3QYToxLwoYdIcP6IHj+eGrEpiiONANHn+dLWQ+jveFWRA==","time":1754201014569,"size":379112}