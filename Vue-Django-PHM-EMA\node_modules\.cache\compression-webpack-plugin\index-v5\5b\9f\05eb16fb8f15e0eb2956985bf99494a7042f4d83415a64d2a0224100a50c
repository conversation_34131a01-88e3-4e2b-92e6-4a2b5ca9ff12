
0409be21c8ddd079ac5052dc9531cbc764aaeb17	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"a9704f1f2a02ab9dbfd1e7324acbf42c\"}","integrity":"sha512-bGJTyh299rRiYvxYkxUYqYsE5DDDNTpsbAK/QDt323g9baTiG5c71grsvQfc4jeaHfRzRlGIGNOO1R7QURlzfQ==","time":1754200970247,"size":23540}