
9b12e7175dcfcc004371822537e95f7250f48660	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.323f5cc6d70518bc133c.hot-update.js\",\"contentHash\":\"298be5304ff12c3f87a4fb6626015bd2\"}","integrity":"sha512-8tMIl+iqillcWbZx32xs3fugbMrSzvDVVs+GFBtyptJEb0ezR8miooeGFAKHua4Hv2NrBA3HoQEqBdGVNzXkpQ==","time":1754202961627,"size":20664}