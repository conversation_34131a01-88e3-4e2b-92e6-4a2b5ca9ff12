
f820796d8ddca2d7439e5ae2d14c9b907617b251	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"ae1e00b2983f8bfd44ba037719071c7d\"}","integrity":"sha512-UErjCFPV/w2rbJng8V0BJipRuXQxLx2lOedeBaJn3InD0ukk3wxNGtPWXPZvR9TcwhouZCvGNOHIWEvJaw17ow==","time":1754204153627,"size":26687}