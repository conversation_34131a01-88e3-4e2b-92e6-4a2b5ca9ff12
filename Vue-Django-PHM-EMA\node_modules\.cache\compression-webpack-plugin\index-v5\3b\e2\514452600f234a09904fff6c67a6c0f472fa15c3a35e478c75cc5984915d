
0137a00eb9d5a316740cb103ba1dff2a39001e8d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"ae1e00b2983f8bfd44ba037719071c7d\"}","integrity":"sha512-5DegOHlGoKnS6+Jz9bgeU9ZVnvAYYv5oJ95oaoPT5gx9ShX22hBr3dj0FuAF1yykbRUDb81r0YKQmxOkAYxHHQ==","time":1754204154006,"size":23449}