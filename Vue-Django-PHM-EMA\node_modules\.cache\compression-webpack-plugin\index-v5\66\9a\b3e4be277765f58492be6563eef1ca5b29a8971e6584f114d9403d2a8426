
920d0d0b3d0c9ab529ecbfde54e9272da85100a7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"82bb08815217f8658998a404aa24e7e5\"}","integrity":"sha512-dVDvJOcaKQW0PffwVdh5mc/GfQO5y8g1natkfx/HDctUPTP7KHRcApJgyrdqvmD77hvFErUhnn+j6vUYMkXeKg==","time":1754202879318,"size":138893}