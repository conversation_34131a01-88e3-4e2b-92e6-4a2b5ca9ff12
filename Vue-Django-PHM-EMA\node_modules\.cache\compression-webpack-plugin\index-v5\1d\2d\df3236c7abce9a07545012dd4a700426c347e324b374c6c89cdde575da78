
de8c2803a846fce111c19fd7394858bc6d30e2b0	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"82bb08815217f8658998a404aa24e7e5\"}","integrity":"sha512-HEsmA3dusl0fq8bILhrAf4AJ1NcxiU4Lzr0HZdWaAmSt2ARR36xTdYuKDMVaEod9oltV64LsdAPus7KgU7ebTw==","time":1754202879737,"size":106946}