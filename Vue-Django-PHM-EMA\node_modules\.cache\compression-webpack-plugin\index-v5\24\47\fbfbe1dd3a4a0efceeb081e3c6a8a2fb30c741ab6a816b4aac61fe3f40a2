
775d3f10fe61e331cdfa374e379a1edcaed47496	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"1918c0e4c960f805e07f189b7f84f4ae\"}","integrity":"sha512-muWXbfrJBvIOEkr2DZafNIDJ7FiYG04w6+DJYmUtE2Z/GnP0D7gUq9GXN2E7KDRfRes/x5mkMe+NwxFX6Gc3nw==","time":1754203211969,"size":23505}