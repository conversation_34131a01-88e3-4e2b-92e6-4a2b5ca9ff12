
c3d72571aaca15ee5b29cfd133144921387f2aa8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"9.38581bc8c15d7c42a11e.hot-update.js\",\"contentHash\":\"1e48bcaac41e84fc8feafdbcd106a4f3\"}","integrity":"sha512-Yoesc+MtPlZim7Jwj1I6blhAY6NRCP0ml1mAEDkw1NuZ8uRxcU1aFt6tY5xPHfN2BKzeBSP+h+iCd2xbbGzCtw==","time":1754204298849,"size":30832}