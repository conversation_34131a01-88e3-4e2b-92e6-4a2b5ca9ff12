
7f863bffc28fbbc1460bc3b711015c252c085e39	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"5.56cc3b7105f6ffde936d.hot-update.js\",\"contentHash\":\"35dcf0d14faee293810c9de36f4527dc\"}","integrity":"sha512-L+rUVuAumU19TPFL1tF2n3f5xnJQM12eIw4h1OdpMjw3I1ic2g+mWbaxYVc8GCgABYw+UpoXhbk2NRiyxQqF0Q==","time":1754206056525,"size":57469}