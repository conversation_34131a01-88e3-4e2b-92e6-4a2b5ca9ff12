<template>
  <div class="sensor-monitor-container modern-theme">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-monitor"></i>
        设备状态监测
        <span class="page-subtitle">Real-time Device Status Monitoring</span>
      </h2>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel modern-card">
      <div class="card-header">
        <h3 class="card-title">监测控制台</h3>
        <div class="status-indicator" :class="isCollecting ? 'online' : 'offline'">
          {{ isCollecting ? '采集中' : '待机' }}
        </div>
      </div>
      <div class="card-content">
        <el-row :gutter="24">
          <el-col :span="6">
            <div class="form-item">
              <label class="form-label">传感器类型</label>
              <el-select
                v-model="selectedSensorType"
                :disabled="isCollecting"
                placeholder="请选择传感器类型"
                style="width: 100%"
                class="modern-select"
              >
                <el-option
                  v-for="item in sensorTypes"
                  :key="item"
                  :label="getSensorTypeLabel(item)"
                  :value="item"
                >
                </el-option>
              </el-select>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="form-item">
              <label class="form-label">采样频率</label>
              <el-select
                v-model="selectedSamplingRate"
                :disabled="isCollecting"
                placeholder="请选择采样频率"
                style="width: 100%"
                class="modern-select"
              >
                <el-option
                  v-for="item in samplingRates"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="form-item">
              <label class="form-label">操作控制</label>
              <div class="button-group">
                <el-button
                  type="primary"
                  :disabled="isCollecting || !selectedSensorType"
                  @click="startCollection"
                  class="modern-button"
                >
                  <i class="el-icon-video-play"></i>
                  开始采集
                </el-button>
                <el-button
                  type="danger"
                  :disabled="!isCollecting"
                  @click="stopCollection"
                  class="modern-button"
                >
                  <i class="el-icon-video-pause"></i>
                  停止采集
                </el-button>
                <el-button
                  type="success"
                  :disabled="!hasData"
                  @click="downloadData"
                  class="modern-button"
                >
                  <i class="el-icon-download"></i>
                  下载数据
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="data-display-area">
      <el-row :gutter="24">
        <!-- 实时数据指标 -->
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-header">
              <h4 class="metric-title">当前值</h4>
            </div>
            <div class="metric-value">
              <span class="value">{{ currentValue }}</span>
              <span class="unit">{{ getUnit(selectedSensorType) }}</span>
            </div>
            <div class="metric-chart">
              <!-- 迷你图表占位 -->
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-header">
              <h4 class="metric-title">平均值</h4>
            </div>
            <div class="metric-value">
              <span class="value">{{ averageValue }}</span>
              <span class="unit">{{ getUnit(selectedSensorType) }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-header">
              <h4 class="metric-title">最大值</h4>
            </div>
            <div class="metric-value">
              <span class="value">{{ maxValue }}</span>
              <span class="unit">{{ getUnit(selectedSensorType) }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-header">
              <h4 class="metric-title">数据点数</h4>
            </div>
            <div class="metric-value">
              <span class="value">{{ chartData.values.length }}</span>
              <span class="unit">个</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表展示区域 -->
    <div class="chart-container modern-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="el-icon-data-line"></i>
          {{ getSensorTypeLabel(selectedSensorType) }} 实时数据
        </h3>
        <div class="chart-info">
          <span class="sampling-rate">采样频率: {{ getSelectedSamplingRateLabel() }}</span>
        </div>
      </div>
      <div class="card-content">
        <div class="chart-wrapper">
          <sensor-chart
            ref="sensorChart"
            :chart-data="chartData"
            :sensor-type="selectedSensorType"
          ></sensor-chart>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SensorChart from './components/SensorChart'

export default {
  name: 'DeviceStatusMonitor',
  components: {
    SensorChart
  },
  data() {
    return {
      sensorTypes: [],
      selectedSensorType: '',
      isCollecting: false,
      chartData: {
        timestamps: [],
        values: []
      },
      dataFetchTimer: null,
      maxDataPoints: 60, // 最多显示60个数据点
      hasData: false,
      samplingRates: [
        { value: 50, label: '20Hz (50ms)' },
        { value: 100, label: '10Hz (100ms)' },
        { value: 200, label: '5Hz (200ms)' },
        { value: 500, label: '2Hz (500ms)' },
        { value: 1000, label: '1Hz (1000ms)' }
      ],
      selectedSamplingRate: 100 // 默认10Hz (100ms)
    }
  },
  computed: {
    // 当前值
    currentValue() {
      if (this.chartData.values.length === 0) return '--'
      return this.chartData.values[this.chartData.values.length - 1].toFixed(3)
    },

    // 平均值
    averageValue() {
      if (this.chartData.values.length === 0) return '--'
      const sum = this.chartData.values.reduce((a, b) => a + b, 0)
      return (sum / this.chartData.values.length).toFixed(3)
    },

    // 最大值
    maxValue() {
      if (this.chartData.values.length === 0) return '--'
      return Math.max(...this.chartData.values).toFixed(3)
    }
  },
  created() {
    this.fetchSensorTypes()
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    // 获取传感器类型列表
    fetchSensorTypes() {
      this.$axios.get('/phm/get_sensor_types/').then(response => {
        if (response.data.code === 200) {
          this.sensorTypes = response.data.data.sensor_types
          if (this.sensorTypes.length > 0) {
            this.selectedSensorType = this.sensorTypes[0]
          }
        } else {
          this.$message.error('获取传感器类型失败: ' + response.data.msg)
        }
      }).catch(error => {
        this.$message.error('获取传感器类型出错: ' + error.message)
      })
    },

    // 开始数据采集
    startCollection() {
      if (!this.selectedSensorType) {
        this.$message.warning('请先选择传感器类型')
        return
      }

      // 重置图表数据
      this.chartData = {
        timestamps: [],
        values: []
      }

      // 发送开始采集请求
      this.$axios.post('/phm/start_data_collection/', {
        sensor_type: this.selectedSensorType,
        sampling_rate: this.selectedSamplingRate
      }).then(response => {
        if (response.data.code === 200) {
          this.isCollecting = true
          this.$message.success('开始采集数据')
          this.startDataFetching()
        } else {
          this.$message.error('开始采集失败: ' + response.data.msg)
        }
      }).catch(error => {
        this.$message.error('开始采集出错: ' + error.message)
      })
    },

    // 停止数据采集
    stopCollection() {
      this.$axios.post('/phm/stop_data_collection/').then(response => {
        if (response.data.code === 200) {
          this.isCollecting = false
          this.$message.success('停止采集数据')
          this.clearTimer()
          this.hasData = this.chartData.values.length > 0
        } else {
          this.$message.error('停止采集失败: ' + response.data.msg)
        }
      }).catch(error => {
        this.$message.error('停止采集出错: ' + error.message)
      })
    },

    // 开始定时获取数据
    startDataFetching() {
      this.clearTimer()
      this.dataFetchTimer = setInterval(() => {
        this.fetchSensorData()
      }, this.selectedSamplingRate) // 使用选定的采样间隔
    },

    // 获取传感器数据
    fetchSensorData() {
      this.$axios.get('/phm/get_simulated_data/').then(response => {
        if (response.data.code === 200) {
          const data = response.data.data

          if (data.status === 'collecting') {
            // 添加新数据点
            this.chartData.timestamps.push(...data.timestamps)
            this.chartData.values.push(...data.values)

            // 限制数据点数量
            if (this.chartData.timestamps.length > this.maxDataPoints) {
              const excess = this.chartData.timestamps.length - this.maxDataPoints
              this.chartData.timestamps.splice(0, excess)
              this.chartData.values.splice(0, excess)
            }

            // 更新图表
            if (this.$refs.sensorChart) {
              this.$refs.sensorChart.updateChart()
            }

            this.hasData = true

            // 自动保存数据到历史记录
            this.saveDataToHistory(data.timestamps, data.values)
          } else if (data.status === 'not_collecting') {
            // 如果服务器端停止了采集，客户端也应停止
            if (this.isCollecting) {
              this.isCollecting = false
              this.clearTimer()
              this.$message.info('服务器已停止数据采集')
            }
          }
        } else {
          this.$message.error('获取数据失败: ' + response.data.msg)
          this.clearTimer()
          this.isCollecting = false
        }
      }).catch(error => {
        this.$message.error('获取数据出错: ' + error.message)
        this.clearTimer()
        this.isCollecting = false
      })
    },

    // 保存数据到历史记录
    saveDataToHistory(timestamps, values) {
      if (!timestamps || !values || timestamps.length === 0 || values.length === 0) {
        return
      }

      // 每次只发送最新的数据点
      const latestIndex = timestamps.length - 1

      // 构造保存数据的请求
      this.$axios.post('/phm/saveMonitorData/', {
        timestamp: timestamps[latestIndex],
        value: values[latestIndex],
        sensor_type: this.selectedSensorType,
        status: '0', // 默认状态为正常
        health_status: '1' // 默认健康状态为正常
      }).then(response => {
        if (response.data.code !== 200) {
          console.error('保存监测数据失败:', response.data.msg)
        }
      }).catch(error => {
        console.error('保存监测数据出错:', error)
      })
    },

    // 清除定时器
    clearTimer() {
      if (this.dataFetchTimer) {
        clearInterval(this.dataFetchTimer)
        this.dataFetchTimer = null
      }
    },

    // 下载数据
    downloadData() {
      if (!this.hasData) {
        this.$message.warning('没有可下载的数据')
        return
      }

      this.$axios.post('/phm/download_sensor_data/', {
        sensor_type: this.selectedSensorType,
        values: this.chartData.values,
        timestamps: this.chartData.timestamps
      }, {
        responseType: 'blob' // 指定响应类型为二进制数据
      }).then(response => {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `${this.selectedSensorType}_data_${new Date().toISOString().replace(/[:.]/g, '_')}.xlsx`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      }).catch(error => {
        this.$message.error('下载数据出错: ' + error.message)
      })
    },

    // 获取传感器类型的显示标签
    getSensorTypeLabel(type) {
      const labels = {
        'position': '位置传感器',
        'current': '电流传感器',
        'setpoint': '指令位移传感器'
      }
      return labels[type] || type
    },

    // 获取当前选择的采样频率标签
    getSelectedSamplingRateLabel() {
      const rate = this.samplingRates.find(item => item.value === this.selectedSamplingRate)
      return rate ? rate.label : `${this.selectedSamplingRate}ms`
    },

    // 获取传感器单位
    getUnit(type) {
      const units = {
        'position': 'mm',
        'current': 'A',
        'setpoint': 'mm'
      }
      return units[type] || ''
    },

    // 清除定时器
    clearTimer() {
      if (this.dataFetchTimer) {
        clearInterval(this.dataFetchTimer)
        this.dataFetchTimer = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.sensor-monitor-container {
  padding: 24px;
  min-height: 100vh;
  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 32px;
  text-align: center;

  .page-title {
    font-size: 2em;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, $techBlue, $techBlueLight);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    i {
      font-size: 1.2em;
      color: $techBlue;
    }

    .page-subtitle {
      font-size: 0.4em;
      color: $textSecondary;
      font-weight: 400;
      margin-top: 8px;
      letter-spacing: 1px;
      display: block;
    }
  }
}

/* 控制面板样式 */
.control-panel {
  margin-bottom: 32px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .form-item {
    margin-bottom: 0;

    .form-label {
      color: $textSecondary;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
    }

    .button-group {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }
}

/* 数据展示区域 */
.data-display-area {
  margin-bottom: 32px;
}

/* 图表容器样式 */
.chart-container {
  .chart-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .sampling-rate {
      color: $textSecondary;
      font-size: 14px;
      padding: 4px 12px;
      background: rgba(0, 212, 255, 0.1);
      border-radius: 12px;
      border: 1px solid rgba(0, 212, 255, 0.2);
    }
  }
}

.chart-wrapper {
  height: 450px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(0, 212, 255, 0.02) 0%,
      transparent 50%,
      rgba(255, 107, 53, 0.02) 100%);
    pointer-events: none;
    border-radius: 8px;
  }
}

/* 现代化选择器样式 */
.modern-select {
  :deep(.el-input__inner) {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid $borderSecondary !important;
    border-radius: 8px !important;
    color: $textPrimary !important;
    transition: all 0.3s ease !important;

    &:focus {
      border-color: $techBlue !important;
      box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1) !important;
      background: rgba(255, 255, 255, 0.08) !important;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .data-display-area {
    .el-col {
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 768px) {
  .sensor-monitor-container {
    padding: 16px;
  }

  .page-header .page-title {
    font-size: 1.5em;
    flex-direction: column;
    gap: 8px;
  }

  .control-panel .card-content {
    .el-row .el-col {
      margin-bottom: 16px;
    }
  }

  .chart-wrapper {
    height: 300px;
  }

  .button-group {
    justify-content: center;
  }
}
</style>
