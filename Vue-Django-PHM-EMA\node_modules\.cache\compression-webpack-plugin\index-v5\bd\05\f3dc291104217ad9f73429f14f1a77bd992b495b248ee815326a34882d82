
c50363535876861a66bffe5efed8eb1730d32963	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"4.f8903a9b3919588f49e3.hot-update.js\",\"contentHash\":\"0420ae18b2f11686f366732ac6dbd79c\"}","integrity":"sha512-DL5LQ6DozbILXY0ClSrVMLExC22lUW8wApSFIvSmAn+rPV77PZJvMr3IWvCrnO14V5FVlNitrW6PNfJuouE/Jw==","time":1754205498625,"size":15128}