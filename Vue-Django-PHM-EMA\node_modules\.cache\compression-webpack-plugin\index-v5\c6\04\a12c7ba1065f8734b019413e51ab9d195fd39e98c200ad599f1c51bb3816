
ea81d4a19f7a9275e583743834dbdbd8a061602f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.1e1279005abe8bb80e8b.hot-update.js\",\"contentHash\":\"5f2897d997d40781fd91b3e31310abec\"}","integrity":"sha512-uF7EOPX6DfjVOnBjwHE/3xZKKPI9Pn+yuViiW/WiqpsgWvX496vHuhub6PC1Qnm75BjMz8SW1hD6inXK/igfRw==","time":1754203679418,"size":23117}