
8315fb7b29c0ec3b8730551098720077d5e9f4aa	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"028ab826dd37becac0d45712c1b9c75b\"}","integrity":"sha512-E2qH+2RqT/ky+31s8DQMzfW0+KB8uhA3zpMkBPi3cSpdY/K1I9fOgMhgoCDkUJVnLOb9wGJpe+1E3Z6RUpDQrw==","time":1754205116989,"size":113844}