
b59b3019bea84cec828d17cbb914fce84821c9f7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"75e1613c0417da75a09d3da509806572\"}","integrity":"sha512-HXgYUbf5LpCE4RAQcHWmeUbok7GQdAjKj4o/e1DEa22eVLiVMHGCWHxMFhN1P82BFS6i6Tzz0C/tawpwcFd2xg==","time":1754205174360,"size":23540}