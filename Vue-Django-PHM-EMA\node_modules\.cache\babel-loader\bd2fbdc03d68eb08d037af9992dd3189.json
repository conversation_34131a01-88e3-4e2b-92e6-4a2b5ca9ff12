{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\home\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\home\\index.vue", "mtime": 1754201109728}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2GA,OAAA,KAAA,KAAA,MAAA,OAAA;AACA,SAAA,aAAA,QAAA,8CAAA;AACA,SAAA,SAAA,QAAA,yCAAA;AACA,OAAA,GAAA,MAAA,uCAAA;AACA,OAAA,oBAAA,IACA,aADA,EAEA,UAFA,EAGA,WAHA,QAIA,8BAJA;AAKA,OAAA,iBAAA,MAAA,2BAAA;AACA,OAAA,iBAAA,MAAA,oCAAA;AACA,SACA,sBADA,EAEA,gBAFA,EAGA,kBAHA,EAIA,aAJA,QAKA,4BALA;AAOA,eAAA;AACA,EAAA,IAAA,EAAA,KADA;AACA;AACA,EAAA,UAAA,EAAA;AACA,IAAA,iBAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,YAAA,EAAA;AAJA,OADA;AAOA,MAAA,UAAA,EAAA,KAPA;AAQA,MAAA,MAAA,EAAA,QARA;AASA;AACA,MAAA,MAAA,EAAA,IAVA;AAWA,MAAA,SAAA,EAAA,IAXA;AAYA,MAAA,UAAA,EAAA,OAZA;AAaA,MAAA,OAAA,EAAA,EAbA;AAcA,MAAA,UAAA,EAAA,aAdA;AAeA,MAAA,MAAA,EAAA,IAfA;AAgBA,MAAA,KAAA,EAAA,IAhBA;AAiBA,MAAA,QAAA,EAAA,IAjBA;AAkBA,MAAA,QAAA,EAAA,IAlBA;AAmBA,MAAA,QAAA,EAAA,IAnBA;AAoBA,MAAA,SAAA,EAAA,IApBA;AAqBA,MAAA,KAAA,EAAA,IArBA;AAsBA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OAtBA;AAyBA,MAAA,OAAA,EAAA,KAzBA;AA0BA,MAAA,KAAA,EAAA;AACA,QAAA,GAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OA1BA;AA+BA,MAAA,YAAA,EAAA;AACA,QAAA,MAAA,EAAA;AADA,OA/BA;AAkCA,MAAA,QAAA,EAAA,MAlCA;AAmCA,MAAA,MAAA,EAAA,EAnCA;AAoCA,MAAA,iBAAA,EAAA,EApCA;AAqCA,MAAA,iBAAA,EAAA,EArCA;AAsCA,MAAA,OAAA,EAAA,IAtCA;AAuCA,MAAA,SAAA,EAAA,EAvCA;AAwCA,MAAA,YAAA,EAAA,IAxCA;AAyCA,MAAA,WAAA,EAAA,IAzCA;AA0CA,MAAA,UAAA,EAAA,EA1CA;AA2CA,MAAA,eAAA,EAAA,CAAA,MAAA,EAAA,KAAA,EAAA,KAAA,CA3CA;AA4CA,MAAA,aAAA,EAAA,EA5CA;AA6CA,MAAA,UAAA,EAAA,EA7CA;AA8CA;AACA,MAAA,gBAAA,EAAA,IA/CA;AAiDA;AACA,MAAA,iBAAA,EAAA,IAlDA;AAoDA;AACA,MAAA,wBAAA,EAAA,KArDA;AAsDA,MAAA,qBAAA,EAAA,OAtDA;AAsDA;AACA,MAAA,yBAAA,EAAA,EAvDA;AAwDA,MAAA,2BAAA,EAAA,EAxDA;AAyDA,MAAA,qBAAA,EAAA,EAzDA;AA2DA;AACA,MAAA,gBAAA,EAAA,IA5DA;AA6DA,MAAA,gBAAA,EAAA,IA7DA;AA+DA;AACA,MAAA,mBAAA,EAAA;AAhEA,KAAA;AAkEA,GAxEA;AAyEA,EAAA,OAzEA,qBAyEA;AACA,SAAA,IAAA;AACA,QAAA,OAAA,GAAA,QAAA,CAAA,cAAA,CAAA,iBAAA,CAAA;AACA,SAAA,KAAA,GAAA,OAAA,CAAA,OAAA,CAAA,oBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA;AACA,SAAA,CAAA,GAAA,KAAA,OAAA,CAAA,OAAA,CAAA;AACA,MAAA,KAAA,EAAA,IADA;AAEA,MAAA,OAAA,EAAA,mBAFA;AAGA,MAAA,QAAA,EAAA,CAHA;AAIA,MAAA,SAAA,EAAA;AAJA,KAAA,CAAA,CAJA,CAUA;;AACA,SAAA,OAAA;AACA,IAAA,OAAA,CAAA,gBAAA,CAAA,OAAA,EAAA,KAAA,YAAA,EAAA,KAAA;AACA,IAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,KAAA,cAAA,EAAA,KAAA;AACA,GAvFA;AAwFA,EAAA,SAxFA,uBAwFA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EADA,CAEA;;AACA,SAAA,oBAAA;AACA,GA5FA;AA6FA,EAAA,WA7FA,yBA6FA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,UAAA;AACA,GA/FA;AAgGA,EAAA,aAhGA,2BAgGA;AACA,IAAA,aAAA,CAAA,KAAA,KAAA,CAAA,CADA,CAGA;;AACA,QAAA,KAAA,iBAAA,EAAA;AACA,WAAA,iBAAA,CAAA,OAAA;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,KAPA,CASA;;;AACA,QAAA,OAAA,GAAA,QAAA,CAAA,cAAA,CAAA,iBAAA,CAAA;;AACA,QAAA,OAAA,EAAA;AACA,MAAA,OAAA,CAAA,mBAAA,CAAA,OAAA,EAAA,KAAA,YAAA,EAAA,KAAA;AACA;;AACA,IAAA,MAAA,CAAA,mBAAA,CAAA,QAAA,EAAA,KAAA,cAAA,EAAA,KAAA;AACA,GA/GA;AAgHA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,gBAFA,8BAEA;AACA,WAAA,mBAAA,GAAA,CAAA,KAAA,mBAAA;AACA,KAJA;AAMA;AACA,IAAA,IAPA,kBAOA;AACA,WAAA,WAAA;AACA,WAAA,MAAA,GAFA,CAGA;;AACA,WAAA,WAAA;AACA,WAAA,YAAA;AACA,WAAA,YAAA;AACA,WAAA,cAAA;AACA,WAAA,MAAA,GARA,CAUA;;AACA,WAAA,SAAA,GAAA,IAAA,KAAA,CAAA,KAAA,EAAA,CAXA,CAYA;;AACA,WAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AACA,WAAA,KAAA,CAAA,GAAA,CAAA,KAAA,SAAA,EAdA,CAgBA;;AACA,WAAA,gBAAA,GAAA,IAAA,oBAAA,CACA,KAAA,KADA,EAEA,KAAA,IAFA,EAGA,KAAA,uBAHA,CAAA,CAjBA,CAuBA;;AACA,WAAA,gBAAA,CAAA,mBAAA,GAxBA,CA0BA;;AACA,WAAA,oBAAA;AACA,KAnCA;AAqCA;AACA,IAAA,qBAtCA,mCAsCA;AACA,UAAA,WAAA,GAAA,KAAA,KAAA,CAAA,eAAA,CAAA,QAAA,CAAA;;AACA,UAAA,WAAA,EAAA;AACA,YAAA;AACA,eAAA,iBAAA,GAAA,IAAA,iBAAA,CAAA,KAAA,KAAA,EAAA,WAAA,EAAA;AACA,YAAA,SAAA,EAAA,CADA;AACA;AACA,YAAA,KAAA,EAAA,CAFA;AAEA;AACA,YAAA,UAAA,EAAA,CAHA,CAGA;;AAHA,WAAA,CAAA;AAKA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA;AACA,SAPA,CAOA,OAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,cAAA,EAAA,KAAA;AACA;AACA;AACA,KApDA;AAsDA;AACA,IAAA,oBAvDA,kCAuDA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,gCAAA,CAAA;;AACA,UAAA,KAAA,MAAA,CAAA,OAAA,CAAA,gCAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,kCAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,MAAA;AACA,aAAA,qBAAA,CAAA,MAAA;AACA,aAAA,MAAA,CAAA,QAAA,CAAA,kCAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,UAAA;AACA,OANA,MAMA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,YAAA;AACA;AACA,KAlEA;AAoEA;AACA,IAAA,uBArEA,mCAqEA,UArEA,EAqEA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,UAAA;;AAEA,UAAA,UAAA,CAAA,IAAA,KAAA,OAAA,IAAA,UAAA,CAAA,IAAA,KAAA,aAAA,EAAA;AACA;AACA,aAAA,KAAA,CAAA,KAAA,GAAA,CAAA;AACA,aAAA,KAAA,CAAA,MAAA,GAAA,KAAA,KAAA,CAAA,GAAA,GAAA,KAAA,KAAA,CAAA,KAAA;AACA,aAAA,OAAA,GAAA,UAAA,CAAA,IAAA,KAAA,OAAA,CAJA,CAMA;;AACA,YAAA,eAAA,GAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,eAAA,EARA,CAUA;;AACA,aAAA,UAAA,GAAA,UAAA,CAAA,IAAA,CAAA,UAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA,UAAA,EAZA,CAcA;;AACA,aAAA,qBAAA,CAAA;AACA,UAAA,IAAA,EAAA,UAAA,CAAA,IADA;AAEA,UAAA,QAAA,EAAA,eAFA;AAGA,UAAA,UAAA,EAAA,UAAA,CAAA,IAAA,CAAA,UAHA;AAIA,UAAA,aAAA,EAAA,UAAA,CAAA,IAAA,CAAA;AAJA,SAAA;AAOA,QAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,OAvBA,MAuBA;AACA;AACA,aAAA,KAAA,CAAA,KAAA,GAAA,CAAA;AACA,aAAA,KAAA,CAAA,MAAA,GAAA,KAAA,KAAA,CAAA,GAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA,aAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA;AACA;AACA,KAvGA;AAyGA;AACA,IAAA,qBA1GA,iCA0GA,gBA1GA,EA0GA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,gBAAA;AACA,WAAA,qBAAA,GAAA,gBAAA,CAAA,IAAA;AACA,WAAA,yBAAA,GAAA,gBAAA,CAAA,QAAA;AACA,WAAA,2BAAA,GAAA,gBAAA,CAAA,UAAA;AACA,WAAA,qBAAA,GAAA,gBAAA,CAAA,aAAA;AACA,WAAA,wBAAA,GAAA,IAAA,CANA,CAQA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,KAAA,CAAA,wBAAA;AACA,OAFA;AAGA,KAtHA;AAwHA;AACA,IAAA,4BAzHA,0CAyHA;AACA,WAAA,wBAAA,GAAA,KAAA;AACA,KA3HA;AA6HA;AACA,IAAA,qBA9HA,iCA8HA,MA9HA,EA8HA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,MAAA,EADA,CAGA;;AACA,UAAA,MAAA,IAAA,MAAA,CAAA,OAAA,EAAA;AACA,YAAA,gBAAA,GAAA,MAAA,CAAA,iBAAA;AACA,YAAA,UAAA,GAAA,gBAAA,CAAA,UAAA;AACA,YAAA,SAAA,GAAA,UAAA,CAAA,oBAAA;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,SAAA,EALA,CAOA;;AACA,YAAA,SAAA,KAAA,UAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,kBAAA;AACA,eAAA,mBAAA;AACA;AACA,SAZA,CAcA;;;AACA,YAAA,QAAA,GAAA,sBAAA,CAAA,SAAA,CAAA;;AACA,YAAA,CAAA,QAAA,EAAA;AACA,UAAA,OAAA,CAAA,IAAA,CAAA,WAAA,EAAA,SAAA;AACA;AACA;;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA,EArBA,CAuBA;;AACA,YAAA,UAAA,GAAA,aAAA,CAAA,SAAA,CAAA;AACA,YAAA,UAAA,GAAA,gBAAA,CAAA,SAAA,CAAA;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EA3BA,CA6BA;;AACA,YAAA,aAAA,GAAA,IAAA,IAAA,GAAA,cAAA,CAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,SADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,GAAA,EAAA,SAHA;AAIA,UAAA,IAAA,EAAA,SAJA;AAKA,UAAA,MAAA,EAAA,SALA;AAMA,UAAA,MAAA,EAAA,SANA;AAOA,UAAA,MAAA,EAAA;AAPA,SAAA,CAAA,CA9BA,CAwCA;;AACA,aAAA,gBAAA,GAAA,SAAA;AACA,aAAA,gBAAA,GAAA,QAAA,CA1CA,CA4CA;;AACA,YAAA,UAAA,KAAA,OAAA,EAAA;AACA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,QAAA;AACA,eAAA,gBAAA,CAAA,WAAA,CAAA,QAAA,EAAA;AACA,YAAA,UAAA,EAAA,UADA;AAEA,YAAA,aAAA,EAAA;AAFA,WAAA;AAIA,SAPA,MAOA,IAAA,UAAA,KAAA,aAAA,EAAA;AACA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,QAAA;AACA,eAAA,gBAAA,CAAA,iBAAA,CAAA,QAAA,EAAA;AACA,YAAA,UAAA,EAAA,UADA;AAEA,YAAA,aAAA,EAAA;AAFA,WAAA;AAIA;AACA,OA5DA,MA4DA;AACA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,OAAA,EAAA,MAAA,GAAA,MAAA,CAAA,OAAA,GAAA,MAAA;AACA;AACA,KAlMA;AAoMA;AACA,IAAA,mBArMA,iCAqMA;AACA,UAAA,KAAA,gBAAA,EAAA;AACA,aAAA,gBAAA,CAAA,WAAA,CAAA,KAAA,gBAAA;AACA,aAAA,gBAAA,GAAA,IAAA;AACA,aAAA,gBAAA,GAAA,IAAA,CAHA,CAKA;;AACA,aAAA,KAAA,CAAA,KAAA,GAAA,CAAA;AACA,aAAA,KAAA,CAAA,MAAA,GAAA,KAAA,KAAA,CAAA,GAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA,aAAA,UAAA,GAAA,EAAA;AACA;AACA,KAjNA;AAmNA;AACA,IAAA,kBApNA,gCAoNA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EADA,CAGA;;AACA,UAAA,mBAAA,GAAA;AACA,QAAA,OAAA,EAAA,IADA;AAEA,QAAA,iBAAA,EAAA;AACA,UAAA,UAAA,EAAA;AACA,YAAA,oBAAA,EAAA;AADA;AADA;AAFA,OAAA,CAJA,CAaA;;AACA,WAAA,qBAAA,CAAA,mBAAA;AACA,KAnOA;AAqOA;AACA,IAAA,WAtOA,yBAsOA;AACA,WAAA,KAAA,GAAA,IAAA,KAAA,CAAA,KAAA,EAAA;AACA,KAxOA;AA0OA;AACA,IAAA,YA3OA,0BA2OA;AACA,UAAA,OAAA,GAAA,QAAA,CAAA,cAAA,CAAA,iBAAA,CAAA;AACA,UAAA,CAAA,GAAA,OAAA,CAAA,WAAA,GAAA,OAAA,CAAA,YAAA,CAFA,CAEA;;AACA,WAAA,MAAA,GAAA,IAAA,KAAA,CAAA,iBAAA,CAAA,EAAA,EAAA,CAAA,EAAA,GAAA,EAAA,KAAA,CAAA,CAHA,CAGA;AACA;;AACA,WAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,GAAA,EAAA,GAAA;AACA,WAAA,MAAA,CAAA,MAAA,CAAA,IAAA,KAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACA,WAAA,KAAA,CAAA,GAAA,CAAA,KAAA,MAAA;AACA,KAnPA;AAqPA;AACA,IAAA,YAtPA,0BAsPA;AACA,UAAA,OAAA,GAAA,QAAA,CAAA,cAAA,CAAA,iBAAA,CAAA;AACA,WAAA,QAAA,GAAA,IAAA,KAAA,CAAA,aAAA,CAAA;AAAA,QAAA,SAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAAA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,OAAA,CAAA,WAAA,EAAA,OAAA,CAAA,YAAA,EAHA,CAGA;;AACA,WAAA,QAAA,CAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,SAAA,CAAA,IAAA,GAAA,KAAA,CAAA,gBAAA;AACA,WAAA,QAAA,CAAA,aAAA,CAAA,QAAA,EAAA,GAAA;AACA,MAAA,OAAA,CAAA,WAAA,CAAA,KAAA,QAAA,CAAA,UAAA,EAPA,CAOA;AACA,KA9PA;AAgQA;AACA,IAAA,MAjQA,oBAiQA;AACA;AACA,UAAA,WAAA,GAAA,KAAA,KAAA,CAAA,eAAA,CAAA,QAAA,CAAA;;AACA,UAAA,WAAA,IAAA,WAAA,CAAA,QAAA,IAAA,KAAA,gBAAA,EAAA;AACA,aAAA,gBAAA,CAAA,oBAAA,CAAA,WAAA;AACA,OALA,CAOA;;;AACA,UAAA,KAAA,iBAAA,EAAA;AACA,aAAA,iBAAA,CAAA,MAAA;AACA;;AAEA,WAAA,QAAA,CAAA,MAAA,CAAA,KAAA,KAAA,EAAA,KAAA,MAAA;AACA,MAAA,qBAAA,CAAA,KAAA,MAAA,CAAA;AACA,KA/QA;AAiRA;AACA,IAAA,MAlRA,oBAkRA;AACA,UAAA,SAAA,GAAA,IAAA,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,EAAA,EAAA,QAAA,EAAA,QAAA,CAAA,CADA,CACA;;AACA,WAAA,KAAA,CAAA,GAAA,CAAA,SAAA;AACA,UAAA,IAAA,GAAA,IAAA,KAAA,CAAA,UAAA,CAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,GAAA,CAAA,IAAA;AACA,KAvRA;AAyRA;AACA,IAAA,OA1RA,qBA0RA;AACA,WAAA,KAAA,GAAA;AACA;AACA,QAAA,aAAA,EAAA,CAFA,CAEA;;AAFA,OAAA;AAIA,UAAA,GAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA;AAAA,QAAA,SAAA,EAAA;AAAA,OAAA,CAAA;AACA,MAAA,GAAA,CAAA,UAAA,CAAA,EAAA,GAAA,KAAA;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,eAAA,EAAA,WAAA,CAAA,GAAA,CAAA,UAAA;AACA,MAAA,GAAA,CAAA,GAAA,CAAA,KAAA,KAAA,EAAA,eAAA,EAAA,CAAA,EAAA,IAAA;AACA,KAnSA;AAqSA;AACA,IAAA,WAtSA,yBAsSA;AACA;AACA,UAAA,YAAA,GAAA,IAAA,KAAA,CAAA,YAAA,CAAA,QAAA,CAAA;AACA,WAAA,KAAA,CAAA,GAAA,CAAA,YAAA;AACA,UAAA,KAAA,GAAA,IAAA,KAAA,CAAA,UAAA,CAAA,QAAA,CAAA,CAJA,CAKA;;AACA,MAAA,KAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,EAAA,GAAA,EAAA,CAAA,GAAA;AACA,WAAA,KAAA,CAAA,GAAA,CAAA,KAAA;AACA,KA9SA;AAgTA;AACA,IAAA,cAjTA,4BAiTA;AACA,WAAA,QAAA,GAAA,IAAA,aAAA,CAAA,KAAA,MAAA,EAAA,KAAA,QAAA,CAAA,UAAA,CAAA;AACA,KAnTA;AAqTA;AACA,IAAA,OAtTA,qBAsTA;AAAA;;AACA,UAAA,KAAA,MAAA,KAAA,QAAA,EAAA;AACA,aAAA,MAAA,GAAA,QAAA;AACA,aAAA,WAAA,GAAA,KAAA,KAAA,CAAA,eAAA,CAAA,KAAA,eAAA,CAAA,CAAA,CAAA,CAAA;;AACA,YAAA,KAAA,iBAAA,KAAA,QAAA,EAAA;AACA,eAAA,WAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,QAAA;AACA,SAFA,MAEA;AACA,eAAA,WAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,iBAAA;AACA;AACA,OARA,MAQA;AACA,aAAA,MAAA,GAAA,QAAA,CADA,CAEA;;AACA,aAAA,WAAA,GAAA,KAAA,KAAA,CAAA,eAAA,CAAA,KAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AACA,aAAA,iBAAA,GAAA,KAAA,WAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,yBAAA,EAAA,KAAA,iBAAA;AACA,aAAA,MAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,aAAA,CADA,CAEA;AACA,SAHA;AAIA;;AACA,WAAA,UAAA,GAAA,CAAA,KAAA,UAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,UAAA;AACA,WAAA,OAAA;AACA,KA7UA;AA+UA;AACA,IAAA,OAhVA,qBAgVA;AAAA;;AACA,UAAA,KAAA,UAAA,EAAA;AACA;AACA;AACA,aAAA,KAAA,GAAA,WAAA,CAAA,YAAA;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,eAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAJA,CAKA;;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,IAAA,EAAA,GAAA,EAAA,EAAA,EAAA,CAAA,EAAA,CAAA,GAAA,EAAA,CAAA,EAAA,GAAA;;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,WAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,YAAA;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,MAAA,CAAA,UAAA;;AACA,gBAAA,MAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA,cAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,cAAA,MAAA,CAAA,YAAA,CAAA,MAAA,GAAA,CAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,KAAA,GAAA,CAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,MAAA,GAAA,EAAA,CAJA,CAKA;AACA;AACA;;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,QAAA;AACA,aATA,MASA;AACA,cAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,cAAA,MAAA,CAAA,YAAA,CAAA,MAAA,GAAA,CAAA;;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,MAAA,CAAA,iBAAA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,KAAA,GAAA,CAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,MAAA,GAAA,EAAA;AACA;AACA,WA3BA;AA4BA,SA7BA,EA6BA,KAAA,OAAA,CAAA,eA7BA,CAAA;AA8BA,OAjCA,MAiCA;AACA,QAAA,aAAA,CAAA,KAAA,KAAA,CAAA;AACA;AACA,KArXA;AAuXA;AACA,IAAA,OAxXA,mBAwXA,OAxXA,EAwXA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,OAAA,EADA,CAEA;;AACA,UAAA,MAAA,GAAA,IAAA,SAAA,EAAA;AACA,MAAA,MAAA,CAAA,IAAA,CACA,KAAA,UAAA,GAAA,OADA,EAEA,UAAA,QAAA,EAAA;AACA;AACA,QAAA,QAAA,CAAA,kBAAA,GAFA,CAIA;;AACA,YAAA,QAAA;;AACA,YAAA,OAAA,KAAA,YAAA,EAAA;AACA,UAAA,QAAA,GAAA,IAAA,KAAA,CAAA,iBAAA,CAAA;AACA,YAAA,KAAA,EAAA,QADA;AACA;AACA,YAAA,WAAA,EAAA,IAFA;AAGA,YAAA,OAAA,EAAA,IAHA;AAGA;AACA,YAAA,IAAA,EAAA,KAAA,CAAA,UAJA;AAIA;AACA,YAAA,UAAA,EAAA,KALA;AAKA;AACA,YAAA,SAAA,EAAA,KANA;AAMA;AACA,YAAA,SAAA,EAAA,GAPA,CAOA;;AAPA,WAAA,CAAA;AASA,SAVA,MAUA;AACA;AACA,cAAA,QAAA,GAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAFA,CAEA;;AACA,cAAA,KAAA,GAAA,aAAA,CAHA,CAGA;AAEA;;AALA,qDAMA,WANA;AAAA;;AAAA;AAMA,gEAAA;AAAA,kBAAA,SAAA;;AACA,kBAAA,QAAA,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA;AACA,gBAAA,KAAA,GAAA,UAAA,CADA,CACA;;AACA;AACA;AACA;AAXA;AAAA;AAAA;AAAA;AAAA;;AAaA,UAAA,QAAA,GAAA,IAAA,KAAA,CAAA,oBAAA,CAAA;AACA,YAAA,KAAA,EAAA,KADA;AACA;AACA,YAAA,WAAA,EAAA,IAFA;AAGA,YAAA,OAAA,EAAA;AAHA,WAAA,CAAA;AAKA;;AAEA,QAAA,MAAA,CAAA,MAAA,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CApCA,CAoCA;;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,IAAA,GAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CArCA,CAqCA;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,IAAA,GAAA,WAAA,CAvCA,CAyCA;;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,EAAA,GAAA,CAAA,EA1CA,CA0CA;;;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,EAAA,GAAA,CAAA,EA3CA,CA2CA;AAEA;;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,GAAA,CAAA,MAAA,CAAA,MAAA,EA9CA,CAgDA;;;AACA,YAAA,OAAA,KAAA,YAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EADA,CAGA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,WAAA,GAAA,CAAA,CAAA,CAJA,CAIA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CALA,CAKA;AAEA;;AACA,cAAA,GAAA,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,aAAA,CAAA,MAAA,CAAA,SAAA,CAAA;AACA,cAAA,MAAA,GAAA,GAAA,CAAA,SAAA,CAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAAA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAVA,CAYA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAdA,CAgBA;;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,EAAA,GAAA,CAAA,EAjBA,CAmBA;;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,EAAA,GAAA,CAAA,EApBA,CAsBA;;;AACA,cAAA,SAAA,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA;AACA,cAAA,UAAA,GAAA,SAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAxBA,CA0BA;AACA;;AACA,cAAA,WAAA,GAAA,IAAA,CAAA,GAAA,CACA,IAAA,CAAA,CAAA,GAAA,UAAA,CAAA,CADA,EAEA,IAAA,CAAA,CAAA,GAAA,UAAA,CAAA,CAFA,EAGA,IAAA,CAAA,CAAA,GAAA,UAAA,CAAA,CAHA,IAIA,GAJA,CA5BA,CAgCA;;AAEA,UAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,WAAA,EAAA,WAAA,EAAA,WAAA,EAlCA,CAoCA;;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,MAAA,EArCA,CAuCA;;;AACA,cAAA,kBAAA,GAAA,UAAA,CAAA,CAAA,GAAA,WAAA;AACA,cAAA,iBAAA,GAAA,UAAA,CAAA,CAAA,GAAA,WAAA;AACA,cAAA,kBAAA,GAAA,UAAA,CAAA,CAAA,GAAA,WAAA,CA1CA,CA4CA;AACA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,GAAA,GAAA,CA9CA,CA8CA;AAEA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,GAAA,GAAA,CAjDA,CAiDA;AAEA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA,IAAA,kBAAA,GAAA,GAAA,CApDA,CAoDA;;AAEA,UAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA;AACA,YAAA,MAAA,EAAA,MADA;AAEA,YAAA,IAAA,EAAA,IAFA;AAGA,YAAA,WAAA,EAAA,WAHA;AAIA,YAAA,UAAA,EAAA,UAJA;AAKA,YAAA,kBAAA,EAAA,kBALA;AAMA,YAAA,iBAAA,EAAA,iBANA;AAOA,YAAA,kBAAA,EAAA,kBAPA;AAQA,YAAA,gBAAA,EAAA,MAAA,CAAA,MAAA,CAAA;AARA,WAAA,EAtDA,CAiEA;;AACA,UAAA,UAAA,CAAA,YAAA;AACA,YAAA,MAAA,CAAA,qBAAA;AACA,WAFA,EAEA,GAFA,CAAA;AAGA,SAtHA,CAwHA;;;AACA,YAAA,OAAA,KAAA,QAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAFA,CAIA;;;AACA,cAAA,IAAA,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,aAAA,CAAA,MAAA,CAAA,SAAA,CAAA;;AACA,cAAA,OAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CANA,CAQA;;;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,QAAA,CAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,QAAA,CAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAVA,CAWA;;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,QAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GAAA,EAAA,CAZA,CAYA;;AAEA,UAAA,MAAA,CAAA,CAAA,CAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;;AAIA,UAAA,MAAA,CAAA,IAAA,CAAA,IAAA,GAAA,UAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EApBA,CAsBA;;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,SAAA;;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,UAAA,MAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,QAAA,MAAA,CAAA,IAAA;AACA;AACA,WAJA,EAxBA,CA8BA;;;AACA,cAAA,MAAA,CAAA,gBAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,WAAA;;AACA,YAAA,MAAA,CAAA,gBAAA,CAAA,mBAAA;AACA;AACA;AACA,OA/JA;AAiKA,KA7hBA;AA+hBA;AACA,IAAA,OAhiBA,qBAgiBA;AAAA;;AAAA;AACA;AACA,UAAA,WAAA,GAAA,IAAA;AACA,UAAA,WAAA,GAAA,EAAA,CAHA,CAKA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,OAAA,GAAA,KAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CADA,CAEA;;AACA,YAAA,OAAA,KAAA,YAAA,EAAA;AACA;AACA,UAAA,WAAA,GAAA,OAAA;AACA,SAHA,MAGA,IAAA,CAAA,OAAA,CAAA,QAAA,CAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA;AACA,eAAA,OAAA,CAAA,OAAA;AACA,UAAA,WAAA,CAAA,IAAA,CAAA,OAAA;AACA;AACA,OAjBA,CAmBA;;;AACA,UAAA,WAAA,EAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,mBAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,WAAA;AACA,SAHA,EAGA,IAHA,CAAA,CADA,CAIA;AACA;AACA,KA1jBA;AA4jBA;AACA,IAAA,oBA7jBA,kCA6jBA;AACA,UAAA,WAAA,GAAA,KAAA,KAAA,CAAA,eAAA,CAAA,QAAA,CAAA;AACA,UAAA,CAAA,WAAA,EAAA,OAFA,CAIA;;AACA,UAAA,cAAA,GAAA,IAAA,KAAA,CAAA,IAAA,EAAA;AACA,WAAA,KAAA,CAAA,QAAA,CAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,MAAA,IAAA,MAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,UAAA,cAAA,CAAA,cAAA,CAAA,MAAA;AACA;AACA,OAJA,EANA,CAYA;;AACA,UAAA,SAAA,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,aAAA,CAAA,WAAA,CAAA,CAbA,CAeA;AACA;;AACA,UAAA,iBAAA,GAAA,cAAA,CAAA,SAAA,CAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAAA;AACA,UAAA,YAAA,GAAA,SAAA,CAAA,SAAA,CAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAlBA,CAoBA;;AACA,UAAA,OAAA,GAAA,iBAAA,CAAA,CAAA,GAAA,YAAA,CAAA,CAAA;AACA,UAAA,OAAA,GAAA,iBAAA,CAAA,CAAA,GAAA,YAAA,CAAA,CAAA;AACA,UAAA,OAAA,GAAA,iBAAA,CAAA,CAAA,GAAA,YAAA,CAAA,CAAA,CAvBA,CAyBA;;AACA,MAAA,WAAA,CAAA,QAAA,CAAA,GAAA,CACA,OADA,EAEA,OAFA,EAGA,OAHA;AAMA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,KA9lBA;AAgmBA;AACA,IAAA,SAjmBA,qBAimBA,EAjmBA,EAimBA,EAjmBA,EAimBA,CAjmBA,EAimBA,CAjmBA,EAimBA,CAjmBA,EAimBA,CAjmBA,EAimBA,CAjmBA,EAimBA;AACA;AACA,UAAA,KAAA,GAAA,CAAA,SAAA,EAAA,SAAA,CAAA;AACA,UAAA,MAAA,GAAA,QAAA,CAAA,aAAA,CAAA,QAAA,CAAA;AACA,UAAA,GAAA,GAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA;AACA,MAAA,MAAA,CAAA,KAAA,GAAA,EAAA;AACA,MAAA,MAAA,CAAA,MAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,SAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,SAAA,GAAA,qBAAA;AACA,WAAA,SAAA,CAAA,GAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AACA,UAAA,QAAA,GAAA,GAAA,CAAA,oBAAA,CAAA,CAAA,EAAA,CAAA,EAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA;AACA,MAAA,QAAA,CAAA,YAAA,CAAA,GAAA,EAAA,MAAA;AACA,MAAA,QAAA,CAAA,YAAA,CAAA,KAAA,EAAA,KAAA;AACA,MAAA,GAAA,CAAA,IAAA,GAAA,kBAAA;AACA,MAAA,GAAA,CAAA,SAAA,GAAA,KAAA,CAAA,KAAA,YAAA,CAAA,MAAA,CAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,KAAA,UAAA,EAAA,GAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,SAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAhBA,CAiBA;;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,KAAA,QAAA,CAAA,YAAA,EAAA,GAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,SAAA,GAAA,QAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,OAAA,EAAA,EAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,OAAA,EAAA,EAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,OAAA,EAAA,EAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,KAAA,QAAA,CAAA,KAAA,EAAA,GAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,KAAA,QAAA,CAAA,IAAA,EAAA,GAAA,EAAA,GAAA;AACA,MAAA,GAAA,CAAA,QAAA,CAAA,KAAA,QAAA,CAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EA3BA,CA4BA;;AACA,UAAA,GAAA,GAAA,MAAA,CAAA,SAAA,CAAA,WAAA,CAAA;AACA,UAAA,QAAA,GAAA,IAAA,KAAA,CAAA,aAAA,CAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,CAAA,CAAA;AACA,UAAA,OAAA,GAAA,IAAA,KAAA,CAAA,aAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CA/BA,CAgCA;;AACA,UAAA,QAAA,GAAA,IAAA,KAAA,CAAA,iBAAA,CAAA;AACA,QAAA,GAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,KAAA,CAAA,UAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,WAAA,EAAA;AAJA,OAAA,CAAA;AAMA,UAAA,IAAA,GAAA,IAAA,KAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA;AACA,MAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AACA,WAAA,KAAA,CAAA,GAAA,CAAA,IAAA;AACA,KA3oBA;AA6oBA;AACA,IAAA,SA9oBA,qBA8oBA,GA9oBA,EA8oBA,CA9oBA,EA8oBA,CA9oBA,EA8oBA,CA9oBA,EA8oBA,CA9oBA,EA8oBA,CA9oBA,EA8oBA;AACA,MAAA,GAAA,CAAA,SAAA;AACA,MAAA,GAAA,CAAA,MAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA;AACA,MAAA,GAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CAAA;AACA,MAAA,GAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CAAA;AACA,MAAA,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AACA,MAAA,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AACA,MAAA,GAAA,CAAA,IAAA;AACA,MAAA,GAAA,CAAA,SAAA;AACA,KAvpBA;AAypBA;AACA,IAAA,YA1pBA,wBA0pBA,KA1pBA,EA0pBA;AAAA;;AACA;AACA,WAAA,MAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,YAAA;AACA,YAAA,GAAA,GAAA,MAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,OAJA,EAFA,CAQA;;AACA,UAAA,WAAA,GAAA,KAAA,KAAA,CAAA,eAAA,CAAA,QAAA,CAAA;;AACA,UAAA,WAAA,IAAA,WAAA,CAAA,QAAA,EAAA;AACA,aAAA,gBAAA,CAAA,oBAAA,CAAA,WAAA;AACA;;AAEA,UAAA,OAAA,GAAA,QAAA,CAAA,cAAA,CAAA,iBAAA,CAAA;AACA,UAAA,SAAA,GAAA,IAAA,KAAA,CAAA,SAAA,EAAA;AACA,UAAA,KAAA,GAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAhBA,CAkBA;;AACA,MAAA,KAAA,CAAA,CAAA,GAAA,KAAA,CAAA,OAAA,GAAA,OAAA,CAAA,WAAA,GAAA,CAAA,GAAA,CAAA;AACA,MAAA,KAAA,CAAA,CAAA,GAAA,EAAA,KAAA,CAAA,OAAA,GAAA,OAAA,CAAA,YAAA,IAAA,CAAA,GAAA,CAAA,CApBA,CAsBA;;AACA,MAAA,SAAA,CAAA,aAAA,CAAA,KAAA,EAAA,KAAA,MAAA,EAvBA,CAyBA;;AACA,UAAA,UAAA,GAAA,SAAA,CAAA,gBAAA,CAAA,KAAA,KAAA,CAAA,QAAA,EAAA,IAAA,EACA,MADA,CACA,UAAA,SAAA,EAAA;AACA;AACA,eAAA,SAAA,CAAA,MAAA,CAAA,IAAA,KAAA,QAAA,IACA,CAAA,SAAA,CAAA,MAAA,CAAA,QAAA,CAAA,YADA;AAEA,OALA,CAAA;;AAOA,UAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,gBAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,MAAA;AACA,OAHA,MAGA;AACA;AACA,aAAA,gBAAA,CAAA,qBAAA;AACA;AACA,KAlsBA;AAosBA;AACA,IAAA,cArsBA,4BAqsBA;AACA,UAAA,OAAA,GAAA,QAAA,CAAA,cAAA,CAAA,iBAAA,CAAA;AACA,WAAA,MAAA,CAAA,MAAA,GAAA,OAAA,CAAA,WAAA,GAAA,OAAA,CAAA,YAAA;AACA,WAAA,MAAA,CAAA,sBAAA;AACA,WAAA,MAAA,GAJA,CAKA;;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,OAAA,CAAA,WAAA,EAAA,OAAA,CAAA,YAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,iBAAA,EAPA,CASA;;AACA,WAAA,mBAAA,GAVA,CAYA;;AACA,WAAA,MAAA,CAAA,QAAA,CAAA,gCAAA,EAbA,CAeA;;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,UAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA;AACA,KAxtBA;AA0tBA;AACA,IAAA,iBA3tBA,+BA2tBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA,gBAAA,EAAA,KAAA,gBAAA,EADA,CAGA;;AACA,UAAA,KAAA,gBAAA,IAAA,KAAA,gBAAA,EAAA;AACA;AACA,aAAA,MAAA,sEAEA,KAAA,2BAFA,8EAGA,KAAA,yBAHA,6EAIA,KAAA,gBAJA,6EAKA,KAAA,qBALA,6EAMA,KAAA,iBAAA,CAAA,KAAA,gBAAA,CANA,yFAOA,KAAA,qBAAA,CAAA,KAAA,gBAAA,CAPA,uCASA,MATA,EASA;AACA,UAAA,wBAAA,EAAA,IADA;AAEA,UAAA,iBAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,kBAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,MAAA;AACA;AALA,SATA;AAgBA;AACA,KAlvBA;AAovBA;AACA,IAAA,iBArvBA,6BAqvBA,SArvBA,EAqvBA;AACA,UAAA,MAAA,GAAA;AACA,gCAAA,gCADA;AAEA,oCAAA,sBAFA;AAGA,8CAAA,0BAHA;AAIA,gCAAA,0BAJA;AAKA,8BAAA,uBALA;AAMA,sCAAA,0BANA;AAOA,iCAAA,wBAPA;AAQA,mCAAA,0BARA;AASA,sCAAA,2BATA;AAUA,gCAAA,2BAVA;AAWA,qCAAA,sBAXA;AAYA,2CAAA,0BAZA;AAaA,8BAAA;AAbA,OAAA;AAeA,aAAA,MAAA,CAAA,SAAA,CAAA,IAAA,MAAA;AACA,KAtwBA;AAwwBA;AACA,IAAA,qBAzwBA,iCAywBA,SAzwBA,EAywBA;AACA,UAAA,SAAA,GAAA;AACA,gCAAA,wBADA;AAEA,oCAAA,uBAFA;AAGA,8CAAA,sBAHA;AAIA,gCAAA,uBAJA;AAKA,8BAAA,oBALA;AAMA,sCAAA,qBANA;AAOA,iCAAA,mBAPA;AAQA,mCAAA,mBARA;AASA,sCAAA,qBATA;AAUA,gCAAA,oBAVA;AAWA,qCAAA,2BAXA;AAYA,2CAAA,uBAZA;AAaA,8BAAA;AAbA,OAAA;AAeA,aAAA,SAAA,CAAA,SAAA,CAAA,IAAA,WAAA;AACA;AA1xBA;AAhHA,CAAA", "sourcesContent": ["<template>\n  <div class=\"home-container modern-theme\">\n    <div class=\"headtxt\">\n      <h3 class=\"main-title\">\n        <span class=\"title-icon\">⚡</span>\n        伺服系统 PHM 软件平台\n        <div class=\"title-subtitle\">Prognostics and Health Management System</div>\n      </h3>\n    </div>\n    <div id=\"model-container\" class=\"modern-card\">\n      <div class=\"ctl\">\n        <el-button-group>\n          <el-button class=\"modern-button\" @click=\"onWindowResize\">\n            <i class=\"el-icon-refresh\"></i>\n            刷新3D视图\n          </el-button>\n        </el-button-group>\n      </div>\n      <div v-show=\"infoShow\" id=\"infoBox\">\n        <ul style=\"font-size:20px; line-height:31px\">\n          <li>组件名称: {{ info.name }}</li>\n        </ul>\n      </div>\n      <!-- 现代化伺服系统信息面板 -->\n      <div id=\"systemInfoBox\" class=\"modern-card\" :class=\"{ 'collapsed': systemInfoCollapsed }\">\n        <div class=\"card-header\" @click=\"toggleSystemInfo\">\n          <h4 class=\"card-title\">\n            <i class=\"el-icon-cpu\"></i>\n            伺服系统信息\n          </h4>\n          <div class=\"header-controls\">\n            <div class=\"status-indicator online\">在线</div>\n            <button class=\"collapse-btn\" :class=\"{ 'collapsed': systemInfoCollapsed }\">\n              <i :class=\"systemInfoCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n            </button>\n          </div>\n        </div>\n        <div class=\"card-content\" v-show=\"!systemInfoCollapsed\">\n          <div class=\"info-grid\">\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🔧</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">型号</span>\n                <span class=\"info-value\">XXXX-EMA</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">⚡</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">额定功率</span>\n                <span class=\"info-value\">200W</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🔋</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">额定电压</span>\n                <span class=\"info-value\">24V DC</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🔄</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">额定转速</span>\n                <span class=\"info-value\">3000RPM</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🎛️</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">控制方式</span>\n                <span class=\"info-value\">闭环位置控制</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">📅</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">生产日期</span>\n                <span class=\"info-value\">2023-05-15</span>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <div class=\"info-icon\">🏷️</div>\n              <div class=\"info-details\">\n                <span class=\"info-label\">序列号</span>\n                <span class=\"info-value\">SN20230515001</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 故障提示浮窗组件 -->\n    <fault-notification\n      :visible.sync=\"faultNotificationVisible\"\n      :type=\"faultNotificationType\"\n      :part-name=\"faultNotificationPartName\"\n      :status-name=\"faultNotificationStatusName\"\n      :diagnosis-time=\"faultNotificationTime\"\n      @close=\"handleFaultNotificationClose\"\n      @view-details=\"handleViewDetails\"\n    />\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'\nimport { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'\nimport dat from 'three/examples/js/libs/dat.gui.min.js'\nimport ModelInteractionUtil, {\n  DEFAULT_COLOR,\n  PART_COLOR,\n  GREEN_PARTS\n} from '@/utils/ModelInteractionUtil'\nimport RocketFlameEffect from '@/utils/RocketFlameEffect'\nimport FaultNotification from '@/components/FaultNotification.vue'\nimport {\n  getPartNameByFaultType,\n  getFaultTypeName,\n  getPartChineseName,\n  getStatusType\n} from '@/utils/faultToPartMapping'\n\nexport default {\n  name: '主控台', // eslint-disable-line vue/name-property-casing\n  components: {\n    FaultNotification\n  },\n  data() {\n    return {\n      dataShow: {\n        Xgive: '',\n        Xget: '',\n        Fget: '',\n        healthStatus: ''\n      },\n      dataPermit: false,\n      btntxt: '开始接收数据',\n      // mesh: null,\n      module: null,\n      moduleAll: null,\n      folderName: './3D/',\n      stlName: '',\n      materialCo: DEFAULT_COLOR,\n      camera: null,\n      scene: null,\n      renderer: null,\n      controls: null,\n      infoShow: true,\n      stateShow: true,\n      files: null,\n      info: {\n        name: ''\n      },\n      isFault: false,\n      state: {\n        all: 20,\n        normal: 20,\n        fault: 0\n      },\n      objectStatus: {\n        status: 0\n      },\n      drawFunc: '整体模型',\n      select: '',\n      clickCurrentColor: '',\n      faultCurrentColor: '',\n      p2hDict: null,\n      hanziList: [],\n      selectObject: null,\n      faultObject: null,\n      faultIndex: '',\n      faultObjectName: ['电机_R', '传感器', '控制器'],\n      errorNameDict: {},\n      statusName: '',\n      // 新增模型交互工具实例\n      modelInteraction: null,\n\n      // 火箭尾焰特效相关\n      rocketFlameEffect: null,\n\n      // 故障提示浮窗相关数据\n      faultNotificationVisible: false,\n      faultNotificationType: 'fault', // 'fault' 或 'degradation'\n      faultNotificationPartName: '',\n      faultNotificationStatusName: '',\n      faultNotificationTime: '',\n\n      // 当前故障/退化状态\n      currentFaultType: null,\n      currentFaultPart: null,\n\n      // 系统信息面板折叠状态\n      systemInfoCollapsed: false\n    }\n  },\n  mounted() {\n    this.init()\n    const element = document.getElementById('model-container')\n    this.files = require.context('../../../public/3D', false, /.STL$/).keys()\n    this.m = this.$notify.warning({\n      title: '提示',\n      message: '模型正在加载，请稍后进行操作...',\n      duration: 0,\n      showClose: false\n    })\n    // this.onWindowResize()\n    this.loadPre()\n    element.addEventListener('click', this.onMouseClick, false)\n    window.addEventListener('resize', this.onWindowResize, false)\n  },\n  activated() {\n    console.log('进入主控台页面了')\n    // 在页面激活时检查诊断结果\n    this.checkDiagnosisResult()\n  },\n  deactivated() {\n    console.log('离开主控台页面了')\n  },\n  beforeDestroy() {\n    clearInterval(this.timer)\n\n    // 清理火箭尾焰特效\n    if (this.rocketFlameEffect) {\n      this.rocketFlameEffect.destroy()\n      this.rocketFlameEffect = null\n    }\n\n    // 移除事件监听器\n    const element = document.getElementById('model-container')\n    if (element) {\n      element.removeEventListener('click', this.onMouseClick, false)\n    }\n    window.removeEventListener('resize', this.onWindowResize, false)\n  },\n  methods: {\n    // 切换系统信息面板折叠状态\n    toggleSystemInfo() {\n      this.systemInfoCollapsed = !this.systemInfoCollapsed\n    },\n\n    // 整体初始化\n    init() {\n      this.createScene()\n      this.helper()\n      // this.initGui()\n      this.createLight()\n      this.createCamera()\n      this.createRender()\n      this.createControls()\n      this.render()\n\n      // 创建一个组来包含所有模型，并设置整体位置\n      this.moduleAll = new THREE.Group()\n      // 将模型放置在网格中央\n      this.moduleAll.position.set(0, 0, 0)\n      this.scene.add(this.moduleAll)\n\n      // 初始化模型交互工具，添加状态变化回调\n      this.modelInteraction = new ModelInteractionUtil(\n        this.scene,\n        this.info,\n        this.handleModelStatusChange\n      )\n\n      // 应用新的颜色设置\n      this.modelInteraction.resetAllModelsColor()\n\n      // 检查Vuex中是否有未处理的诊断结果\n      this.checkDiagnosisResult()\n    },\n\n    // 初始化火箭尾焰特效\n    initRocketFlameEffect() {\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel) {\n        try {\n          this.rocketFlameEffect = new RocketFlameEffect(this.scene, daodanModel, {\n            intensity: 1, // 固定强度值\n            speed: 3, // 固定速度值\n            turbulence: 1 // 固定湍流值\n          })\n          console.log('火箭尾焰特效已初始化')\n        } catch (error) {\n          console.error('初始化火箭尾焰特效失败:', error)\n        }\n      }\n    },\n\n    // 检查Vuex中是否有未处理的诊断结果\n    checkDiagnosisResult() {\n      console.log('检查诊断结果', this.$store.getters['diagnosis/hasUnprocessedResult'])\n      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        const result = this.$store.getters['diagnosis/currentDiagnosisResult']\n        console.log('发现未处理的诊断结果:', result)\n        this.handleDiagnosisResult(result)\n        this.$store.dispatch('diagnosis/processDiagnosisResult')\n        console.log('诊断结果处理完成')\n      } else {\n        console.log('没有未处理的诊断结果')\n      }\n    },\n\n    // 处理模型状态变化\n    handleModelStatusChange(statusData) {\n      console.log('模型状态变化:', statusData)\n\n      if (statusData.type === 'fault' || statusData.type === 'degradation') {\n        // 更新状态计数\n        this.state.fault = 1\n        this.state.normal = this.state.all - this.state.fault\n        this.isFault = statusData.type === 'fault'\n\n        // 获取部件中文名称\n        const partChineseName = getPartChineseName(statusData.object.name)\n        console.log('部件中文名称:', partChineseName)\n\n        // 更新状态名称\n        this.statusName = statusData.info.statusName\n        console.log('状态名称:', this.statusName)\n\n        // 显示故障提示浮窗\n        this.showFaultNotification({\n          type: statusData.type,\n          partName: partChineseName,\n          statusName: statusData.info.statusName,\n          diagnosisTime: statusData.info.diagnosisTime\n        })\n\n        console.log('已显示故障提示浮窗')\n      } else {\n        // 重置状态\n        this.state.fault = 0\n        this.state.normal = this.state.all\n        this.isFault = false\n        this.statusName = ''\n        console.log('已重置状态')\n      }\n    },\n\n    // 显示故障提示浮窗\n    showFaultNotification(notificationData) {\n      console.log('显示故障提示浮窗:', notificationData)\n      this.faultNotificationType = notificationData.type\n      this.faultNotificationPartName = notificationData.partName\n      this.faultNotificationStatusName = notificationData.statusName\n      this.faultNotificationTime = notificationData.diagnosisTime\n      this.faultNotificationVisible = true\n\n      // 确保浮窗在DOM更新后显示\n      this.$nextTick(() => {\n        console.log('浮窗显示状态:', this.faultNotificationVisible)\n      })\n    },\n\n    // 处理故障提示浮窗关闭\n    handleFaultNotificationClose() {\n      this.faultNotificationVisible = false\n    },\n\n    // 处理故障诊断结果\n    handleDiagnosisResult(result) {\n      console.log('处理诊断结果:', result)\n\n      // 如果诊断成功\n      if (result && result.success) {\n        const diagnosisDetails = result.diagnosis_details\n        const conclusion = diagnosisDetails.conclusion\n        const faultType = conclusion.predicted_fault_mode\n\n        console.log('诊断结论:', faultType)\n\n        // 如果是正常状态，重置所有部件状态\n        if (faultType === '0_normal') {\n          console.log('诊断结果为正常状态，重置所有部件')\n          this.resetAllPartsStatus()\n          return\n        }\n\n        // 获取对应的部件名称\n        const partName = getPartNameByFaultType(faultType)\n        if (!partName) {\n          console.warn('未找到对应的部件:', faultType)\n          return\n        }\n\n        console.log('对应的部件名称:', partName)\n\n        // 获取状态类型和状态名称\n        const statusType = getStatusType(faultType)\n        const statusName = getFaultTypeName(faultType)\n\n        console.log('状态类型:', statusType, '状态名称:', statusName)\n\n        // 获取当前时间作为诊断时间\n        const diagnosisTime = new Date().toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit',\n          hour12: false\n        })\n\n        // 保存当前故障信息\n        this.currentFaultType = faultType\n        this.currentFaultPart = partName\n\n        // 根据状态类型标记部件\n        if (statusType === 'fault') {\n          // 故障状态，标记为红色\n          console.log('标记部件为故障状态:', partName)\n          this.modelInteraction.markAsFault(partName, {\n            statusName,\n            diagnosisTime\n          })\n        } else if (statusType === 'degradation') {\n          // 退化状态，标记为褐色\n          console.log('标记部件为退化状态:', partName)\n          this.modelInteraction.markAsDegradation(partName, {\n            statusName,\n            diagnosisTime\n          })\n        }\n      } else {\n        // 诊断失败\n        console.error('诊断失败:', result ? result.message : '未知错误')\n      }\n    },\n\n    // 重置所有部件状态\n    resetAllPartsStatus() {\n      if (this.currentFaultPart) {\n        this.modelInteraction.resetStatus(this.currentFaultPart)\n        this.currentFaultPart = null\n        this.currentFaultType = null\n\n        // 重置状态计数\n        this.state.fault = 0\n        this.state.normal = this.state.all\n        this.isFault = false\n        this.statusName = ''\n      }\n    },\n\n    // 测试故障诊断\n    testFaultDiagnosis() {\n      console.log('测试故障诊断')\n\n      // 创建一个模拟的诊断结果\n      const mockDiagnosisResult = {\n        success: true,\n        diagnosis_details: {\n          conclusion: {\n            predicted_fault_mode: '4_fault_stator_short'\n          }\n        }\n      }\n\n      // 处理诊断结果\n      this.handleDiagnosisResult(mockDiagnosisResult)\n    },\n\n    // 三大件 创建场景\n    createScene() {\n      this.scene = new THREE.Scene()\n    },\n\n    // 创建相机\n    createCamera() {\n      const element = document.getElementById('model-container')\n      const k = element.clientWidth / element.clientHeight // 实际3d显示窗口宽高比\n      this.camera = new THREE.PerspectiveCamera(45, k, 0.1, 10000) // 透视相机 (for,aspect,near,far) 视场 长宽比 多近开始渲染 最远看到的距离\n      // 调整相机位置，以便更好地观察模型\n      this.camera.position.set(0, 400, 600)\n      this.camera.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(this.camera)\n    },\n\n    // 创建渲染器\n    createRender() {\n      const element = document.getElementById('model-container')\n      this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })\n      this.renderer.setSize(element.clientWidth, element.clientHeight) // 设置渲染区域尺寸\n      this.renderer.shadowMap.enabled = true\n      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap\n      this.renderer.setClearColor(0x050505, 0.6)\n      element.appendChild(this.renderer.domElement) // 渲染div到canvas\n    },\n\n    // 渲染\n    render() {\n      // 确保导弹壳体始终保持透明\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel && daodanModel.material && this.modelInteraction) {\n        this.modelInteraction.updateDaodanMaterial(daodanModel)\n      }\n\n      // 更新火箭尾焰特效\n      if (this.rocketFlameEffect) {\n        this.rocketFlameEffect.update()\n      }\n\n      this.renderer.render(this.scene, this.camera)\n      requestAnimationFrame(this.render)\n    },\n\n    // 底部网格和三轴指示器\n    helper() {\n      var gridplane = new THREE.GridHelper(1200, 60, 0xFF7F50, 0x4A4A4A) // 一格20\n      this.scene.add(gridplane)\n      const axes = new THREE.AxesHelper(200)\n      this.scene.add(axes)\n    },\n\n    // 控制模型组件\n    initGui() {\n      this.paras = {\n        // rotationSpeed: 0.005 // 中文也行\n        rotationSpeed: 0 // 中文也行\n      }\n      var gui = new dat.GUI({ autoPlace: false })\n      gui.domElement.id = 'gui'\n      document.getElementById('gui_container').appendChild(gui.domElement)\n      gui.add(this.paras, 'rotationSpeed', 0, 0.05)\n    },\n\n    // 创建光源\n    createLight() {\n      // 环境光 没有特定的光源，该光源不会影响阴影的产生，被应用到全局范围内的所有对象,使用该光源是为了弱化阴影或者添加一些颜色\n      const ambientLight = new THREE.AmbientLight(0x999999)\n      this.scene.add(ambientLight)\n      const point = new THREE.PointLight(0xffffff)\n      // const point = new THREE.SpotLight(0xffffff)\n      point.position.set(-300, 600, -400)\n      this.scene.add(point)\n    },\n\n    // 轨道控制、旋转缩放\n    createControls() {\n      this.controls = new OrbitControls(this.camera, this.renderer.domElement)\n    },\n\n    // 接收数据控制\n    dataGet() {\n      if (this.btntxt === '停止接收数据') {\n        this.btntxt = '开始接收数据'\n        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        if (this.faultCurrentColor === 14423100) {\n          this.faultObject.material.color.set(0x7CFC00)\n        } else {\n          this.faultObject.material.color.set(this.faultCurrentColor)\n        }\n      } else {\n        this.btntxt = '停止接收数据'\n        // 目前认为故障组件只有EMA总装一种，提前指定故障组件 应该在getData()中按照faultIndex再确定\n        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        this.faultCurrentColor = this.faultObject.material.color.getHex()\n        console.log('inital faultObjectColor', this.faultCurrentColor)\n        this.$axios.get('./errorDict.json').then(res => {\n          this.errorNameDict = res.data.errorNameDict\n          // console.log('字典', res.data.errorNameDict)\n        })\n      }\n      this.dataPermit = !this.dataPermit\n      console.log(this.dataPermit)\n      this.getData()\n    },\n\n    // 接收数据\n    getData() {\n      if (this.dataPermit) {\n        /* this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        this.faultCurrentColor = this.faultObject.material.color.getHex() */\n        this.timer = setInterval(() => {\n          this.$axios.get('/phm/getData/').then((res) => {\n            // console.log('111222', res.data.data, typeof (res.data.data)) 类型是object\n            this.dataShow.Xget = res.data.data.x_get\n            this.dataShow.Xgive = res.data.data.x_give\n            this.dataShow.Fget = res.data.data.f_get\n            // cw, ch, r, k, x, y, z\n            this.dataPanel(1600, 840, 50, 6, -380, 0, 210)\n            this.faultIndex = res.data.data.faultStatus\n            this.dataShow.healthStatus = res.data.data.healthStatus\n            this.statusName = this.errorNameDict[this.faultIndex]\n            console.log('faultIndex', this.faultIndex)\n            if (this.faultIndex !== '0') {\n              this.isFault = true\n              this.objectStatus.status = 1\n              this.state.fault = 1\n              this.state.normal = 19\n              // if (this.faultIndex !== '10') {\n              //   this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n              // }\n              this.faultObject.material.color.set(0xDC143C)\n            } else {\n              this.isFault = false\n              this.objectStatus.status = 0\n              this.faultObject.material.color.set(this.faultCurrentColor)\n              this.state.fault = 0\n              this.state.normal = 20\n            }\n          })\n        }, this.$Common.requestInterval)\n      } else {\n        clearInterval(this.timer)\n      }\n    },\n\n    // 加载STL模型\n    loadSTL(stlName) {\n      console.log('加载STL模型:', stlName)\n      // const THIS = this // eslint-disable-line no-unused-vars\n      const loader = new STLLoader()\n      loader.load(\n        this.folderName + stlName,\n        geometry => {\n          // 确保几何体居中\n          geometry.computeBoundingBox()\n\n          // 为导弹壳体模型设置特殊材质\n          let material\n          if (stlName === 'daodan.STL') {\n            material = new THREE.MeshPhongMaterial({\n              color: 0xffffff, // 白色\n              transparent: true,\n              opacity: 0.10, // 更高的透明度\n              side: THREE.DoubleSide, // 双面渲染\n              depthWrite: false, // 禁用深度写入以避免渲染问题\n              depthTest: false, // 禁用深度测试，使射线能够穿透\n              shininess: 150 // 增加光泽度\n            })\n          } else {\n            // 检查是否为需要设置为绿色的部件\n            const partName = stlName.split('.')[0] // 去掉.STL后缀\n            let color = DEFAULT_COLOR // 默认蓝色\n\n            // 检查是否为需要设置为绿色的部件\n            for (const greenPart of GREEN_PARTS) {\n              if (partName.includes(greenPart)) {\n                color = PART_COLOR // 设置为绿色\n                break\n              }\n            }\n\n            material = new THREE.MeshStandardMaterial({\n              color: color, // 使用确定的颜色\n              transparent: true,\n              opacity: 0.7\n            })\n          }\n\n          this.module = new THREE.Mesh(geometry, material) // 创建网格对象\n          this.module.name = stlName.split('.')[0] // 去掉.STL后缀\n          console.log('创建模型部件:', this.module.name)\n          this.info.name = '模型正在加载...'\n\n          // 旋转模型使底座底面与网格平行\n          this.module.rotateX(-Math.PI / 2) // 绕X轴旋转，使模型水平放置\n          this.module.rotateX(Math.PI / 2) // 绕X轴顺时针旋转90度\n\n          // 将所有模型添加到模型组\n          this.moduleAll.add(this.module)\n\n          // 如果是导弹模型，需要特殊处理\n          if (stlName === 'daodan.STL') {\n            console.log('处理导弹壳体模型位置')\n\n            // 设置导弹壳体的特殊属性，使其不阻挡交互\n            this.module.renderOrder = -1 // 负数renderOrder确保它在其他对象之前渲染\n            this.module.userData.isBackground = true // 标记为背景对象\n\n            // 计算所有模型的包围盒\n            const box = new THREE.Box3().setFromObject(this.moduleAll)\n            const center = box.getCenter(new THREE.Vector3())\n            const size = box.getSize(new THREE.Vector3())\n\n            // 重置导弹模型的位置和旋转\n            this.module.position.set(0, 0, 0)\n            this.module.rotation.set(0, 0, 0)\n\n            // 首先水平放置导弹模型\n            this.module.rotateX(-Math.PI / 2)\n\n            // 然后逆时针旋转90度，使导弹壳体与其他零件平行\n            this.module.rotateZ(Math.PI / 2)\n\n            // 计算导弹模型的包围盒\n            const daodanBox = new THREE.Box3().setFromObject(this.module)\n            const daodanSize = daodanBox.getSize(new THREE.Vector3())\n\n            // 调整导弹模型的缩放，确保能包含所有模型\n            // 增加缩放系数，使导弹壳体更大，伺服系统可以完全放在内部\n            const scaleFactor = Math.max(\n              size.x / daodanSize.x,\n              size.y / daodanSize.y,\n              size.z / daodanSize.z\n            ) * 2.5 // 放大3.5倍，确保伺服系统完全位于内部\n\n            this.module.scale.set(scaleFactor, scaleFactor, scaleFactor)\n\n            // 将导弹模型放置在所有模型的中心\n            this.module.position.copy(center)\n\n            // 计算导弹壳体的尺寸\n            const scaledDaodanLength = daodanSize.z * scaleFactor\n            const scaledDaodanWidth = daodanSize.x * scaleFactor\n            const scaledDaodanHeight = daodanSize.y * scaleFactor\n\n            // 根据图片反馈调整位置\n            // 向下移动导弹壳体（Y轴负方向）\n            this.module.position.y -= size.y * 1.3 // 向下移动\n\n            // 向右移动导弹壳体（Z轴正方向，由于旋转了90度）\n            this.module.position.z += size.z * 3.7 // 向右移动\n\n            // 向后移动导弹壳体（X轴正方向，由于旋转了90度）\n            this.module.position.x += scaledDaodanLength * 0.6// 向后移动\n\n            console.log('导弹壳体模型已调整位置和大小', {\n              center,\n              size,\n              scaleFactor,\n              daodanSize,\n              scaledDaodanLength,\n              scaledDaodanWidth,\n              scaledDaodanHeight,\n              adjustedPosition: this.module.position\n            })\n\n            // 延迟初始化火箭尾焰特效，确保导弹模型完全加载和定位\n            setTimeout(() => {\n              this.initRocketFlameEffect()\n            }, 800)\n          }\n\n          // 如果加载完成底座，说明主要模型都已加载完成\n          if (stlName === '底座.STL') {\n            // 调整整个模型组的位置，使其居中\n            this.moduleAll.position.set(0, 0, 0)\n\n            // 计算整个模型组的包围盒，用于居中\n            const box = new THREE.Box3().setFromObject(this.moduleAll)\n            const center = box.getCenter(new THREE.Vector3())\n\n            // 将模型移动到网格中央\n            this.moduleAll.position.x = -center.x\n            this.moduleAll.position.z = -center.z\n            // 保持y轴位置，使底座底面与网格平行\n            this.moduleAll.position.y = -box.min.y + 10 // 稍微抬高一点，避免穿过网格\n\n            this.m.close()\n            this.$notify.success({\n              title: '提示',\n              message: '模型加载完毕'\n            })\n            this.info.name = '待单击模型...'\n            console.log('模型已加载并居中放置')\n\n            // 打印所有模型部件名称，用于调试\n            console.log('所有模型部件:')\n            this.scene.traverse((object) => {\n              if (object.isMesh) {\n                console.log(' - ' + object.name)\n              }\n            })\n\n            // 确保所有部件都应用了正确的颜色\n            if (this.modelInteraction) {\n              console.log('应用颜色设置...')\n              this.modelInteraction.resetAllModelsColor()\n            }\n          }\n        }\n      )\n    },\n\n    // 改变显示组件重新绘制\n    loadPre() { // 加载哪个零件\n      // 先加载除导弹外的所有主要部件\n      let daodanModel = null\n      const otherModels = []\n\n      // 遍历所有模型文件\n      for (var i = 0; i < this.files.length; i++) {\n        var stlName = this.files[i].split('/')[1]\n        // 分开处理导弹模型和其他模型\n        if (stlName === 'daodan.STL') {\n          // 先记录导弹模型，稍后加载\n          daodanModel = stlName\n        } else if (!stlName.includes('GB╱T') && !stlName.includes('螺钉')) {\n          // 只加载主要零件，减少加载时间\n          this.loadSTL(stlName)\n          otherModels.push(stlName)\n        }\n      }\n\n      // 在所有其他模型加载完成后，最后加载导弹壳体模型\n      if (daodanModel) {\n        setTimeout(() => {\n          console.log('加载导弹壳体模型，包含所有其他模型')\n          this.loadSTL(daodanModel)\n        }, 1000) // 延迟加载，确保其他模型已经加载完成\n      }\n    },\n\n    // 调整导弹模型位置\n    adjustDaodanPosition() {\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (!daodanModel) return\n\n      // 计算所有其他模型的包围盒\n      const otherModelsBox = new THREE.Box3()\n      this.scene.traverse((object) => {\n        if (object.isMesh && object.name !== 'daodan') {\n          otherModelsBox.expandByObject(object)\n        }\n      })\n\n      // 计算导弹模型的包围盒\n      const daodanBox = new THREE.Box3().setFromObject(daodanModel)\n\n      // 调整导弹模型的位置，使其包含所有其他模型\n      // 并将其他模型放在导弹模型的尾部\n      const otherModelsCenter = otherModelsBox.getCenter(new THREE.Vector3())\n      const daodanCenter = daodanBox.getCenter(new THREE.Vector3())\n\n      // 计算需要移动的距离\n      const offsetX = otherModelsCenter.x - daodanCenter.x\n      const offsetY = otherModelsCenter.y - daodanCenter.y\n      const offsetZ = otherModelsCenter.z - daodanCenter.z\n\n      // 移动导弹模型\n      daodanModel.position.set(\n        offsetX,\n        offsetY,\n        offsetZ\n      )\n\n      console.log('调整了导弹模型位置')\n    },\n\n    // 数据面板\n    dataPanel(cw, ch, r, k, x, y, z) {\n      // 用canvas生成图片\n      var color = ['#008000', '#FF0000']\n      var canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      canvas.width = cw\n      canvas.height = ch\n      ctx.lineWidth = 10\n      ctx.fillStyle = 'rgba(255,255,255,1)'\n      this.roundRect(ctx, 0, 0, cw, ch, r)\n      var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0)\n      gradient.addColorStop('0', 'blue')\n      gradient.addColorStop('1.0', 'red')\n      ctx.font = 'normal 80pt \"楷体\"'\n      ctx.fillStyle = color[this.objectStatus.status]\n      ctx.fillText(this.statusName, 700, 150)\n      ctx.fillStyle = color[0]\n      // ctx.fillText('0.83', 700, 300)\n      ctx.fillText(this.dataShow.healthStatus, 700, 300)\n      ctx.fillStyle = gradient\n      ctx.fillText('当前状态：', 60, 150)\n      ctx.fillText('健康值：', 60, 300)\n      ctx.fillText('位移指令：', 60, 450)\n      ctx.fillText('实际位移：', 60, 600)\n      ctx.fillText('负载力：', 60, 750)\n      ctx.fillText(this.dataShow.Xgive, 700, 450)\n      ctx.fillText(this.dataShow.Xget, 700, 600)\n      ctx.fillText(this.dataShow.Fget, 700, 750)\n      // canvas.height = 500\n      const url = canvas.toDataURL('./img/png')\n      var geometry = new THREE.PlaneGeometry(cw / k, ch / k)\n      var texture = new THREE.TextureLoader().load(url)\n      // 将图像加载为纹理，然后将纹理赋给材质的map属性\n      var material = new THREE.MeshBasicMaterial({\n        map: texture,\n        side: THREE.DoubleSide,\n        opacity: 1,\n        transparent: true\n      })\n      const rect = new THREE.Mesh(geometry, material)\n      rect.position.set(x, z, y)\n      this.scene.add(rect)\n    },\n\n    // 画圆角矩形\n    roundRect(ctx, x, y, w, h, r) {\n      ctx.beginPath()\n      ctx.moveTo(x + r, y)\n      ctx.arcTo(x + w, y, x + w, y + h, r)\n      ctx.arcTo(x + w, y + h, x, y + h, r)\n      ctx.arcTo(x, y + h, x, y, r)\n      ctx.arcTo(x, y, x + w, y, r)\n      ctx.fill()\n      ctx.closePath()\n    },\n\n    // 监听函数部分 - 使用新的模型交互工具\n    onMouseClick(event) {\n      // 加载字典数据\n      this.$axios.get('./errorDict.json').then(res => {\n        this.p2hDict = res.data.pinyin2hanzi\n        const obj = this.p2hDict\n        this.hanziList = Object.keys(obj)\n      })\n\n      // 确保导弹壳体始终保持透明\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel && daodanModel.material) {\n        this.modelInteraction.updateDaodanMaterial(daodanModel)\n      }\n\n      const element = document.getElementById('model-container')\n      var raycaster = new THREE.Raycaster()\n      var mouse = new THREE.Vector2()\n\n      // 将鼠标点击位置的屏幕坐标转成threejs中的标准坐标\n      mouse.x = (event.offsetX / element.clientWidth) * 2 - 1\n      mouse.y = -(event.offsetY / element.clientHeight) * 2 + 1\n\n      // 通过摄像机和鼠标位置更新射线\n      raycaster.setFromCamera(mouse, this.camera)\n\n      // 计算物体和射线的焦点\n      var intersects = raycaster.intersectObjects(this.scene.children, true)\n        .filter(intersect => {\n          // 过滤掉导弹壳体，使其不响应点击\n          return intersect.object.name !== 'daodan' &&\n                 !intersect.object.userData.isBackground\n        })\n\n      if (intersects.length > 0) {\n        // 处理点击到的对象\n        this.modelInteraction.handleModelClick(intersects[0].object)\n      } else {\n        // 点击到空白区域\n        this.modelInteraction.handleBackgroundClick()\n      }\n    },\n\n    // 检测区域大小变化\n    onWindowResize() {\n      const element = document.getElementById('model-container')\n      this.camera.aspect = element.clientWidth / element.clientHeight\n      this.camera.updateProjectionMatrix()\n      this.render()\n      // 设置渲染区域尺寸\n      this.renderer.setSize(element.clientWidth, element.clientHeight)\n      console.log('3d area changes')\n\n      // 恢复到初始正常状态\n      this.resetAllPartsStatus()\n\n      // 清除Vuex中的诊断结果\n      this.$store.dispatch('diagnosis/clearDiagnosisResult')\n\n      // 重置信息显示\n      this.info.name = '待单击模型...'\n\n      console.log('已恢复到初始正常状态')\n    },\n\n    // 处理view-details事件\n    handleViewDetails() {\n      console.log('查看详情:', this.currentFaultType, this.currentFaultPart)\n\n      // 如果当前有故障或退化状态\n      if (this.currentFaultType && this.currentFaultPart) {\n        // 显示详细信息对话框\n        this.$alert(`\n          <div class=\"fault-details\">\n            <h3>${this.faultNotificationStatusName}</h3>\n            <p><strong>部件名称:</strong> ${this.faultNotificationPartName}</p>\n            <p><strong>故障类型:</strong> ${this.currentFaultType}</p>\n            <p><strong>诊断时间:</strong> ${this.faultNotificationTime}</p>\n            <p><strong>可能原因:</strong> ${this.getPossibleCauses(this.currentFaultType)}</p>\n            <p><strong>建议解决方案:</strong> ${this.getSuggestedSolutions(this.currentFaultType)}</p>\n          </div>\n        `, '故障详情', {\n          dangerouslyUseHTMLString: true,\n          confirmButtonText: '确定',\n          callback: action => {\n            console.log(action)\n          }\n        })\n      }\n    },\n\n    // 获取可能原因\n    getPossibleCauses(faultType) {\n      const causes = {\n        '1_degradation_magnet': '长时间使用导致永磁体性能下降；环境温度过高；机械冲击或振动。',\n        '2_degradation_brush_wear': '正常磨损；过载运行；环境中存在过多粉尘。',\n        '3_degradation_commutator_oxidation': '环境湿度过高；长期不使用；电刷与换向器接触不良。',\n        '4_fault_stator_short': '绝缘材料老化；过载运行；绕组温度过高；制造缺陷。',\n        '5_fault_rotor_open': '机械损伤；过载运行；焊接点断裂；制造缺陷。',\n        '6_degradation_bearing_wear': '正常磨损；润滑不足；轴承负载过大；轴承安装不当。',\n        '7_fault_bearing_stuck': '润滑失效；异物进入；轴承严重磨损；轴承锈蚀。',\n        '8_degradation_gear_wear': '正常磨损；润滑不足；齿轮负载过大；齿轮材料缺陷。',\n        '9_degradation_sensor_drift': '长期使用导致性能下降；环境温度变化；电源电压波动。',\n        '10_fault_sensor_loss': '传感器连接松动；传感器损坏；信号线断路；电源故障。',\n        '11_fault_mosfet_breakdown': '过电压；过电流；温度过高；静电放电损伤。',\n        '12_degradation_drive_distortion': '电路元件老化；电源电压不稳；信号干扰；温度变化。',\n        '13_fault_mcu_crash': '软件错误；电源问题；硬件故障；外部干扰。'\n      }\n      return causes[faultType] || '未知原因'\n    },\n\n    // 获取建议解决方案\n    getSuggestedSolutions(faultType) {\n      const solutions = {\n        '1_degradation_magnet': '更换永磁体；降低工作环境温度；减少机械冲击。',\n        '2_degradation_brush_wear': '更换电刷；检查负载是否过大；清洁工作环境。',\n        '3_degradation_commutator_oxidation': '清洁换向器表面；降低环境湿度；定期维护。',\n        '4_fault_stator_short': '更换定子绕组；检查负载情况；改善散热条件。',\n        '5_fault_rotor_open': '更换转子绕组；检查焊接点；降低负载。',\n        '6_degradation_bearing_wear': '更换轴承；增加润滑；检查轴承安装情况。',\n        '7_fault_bearing_stuck': '更换轴承；清洁轴承；检查润滑情况。',\n        '8_degradation_gear_wear': '更换齿轮；增加润滑；检查负载情况。',\n        '9_degradation_sensor_drift': '校准传感器；更换传感器；稳定工作环境。',\n        '10_fault_sensor_loss': '检查连接；更换传感器；检查信号线路。',\n        '11_fault_mosfet_breakdown': '更换MOSFET；检查电路保护措施；改善散热条件。',\n        '12_degradation_drive_distortion': '更换老化元件；稳定电源电压；增加信号滤波。',\n        '13_fault_mcu_crash': '更新软件；检查电源；更换MCU；增加抗干扰措施。'\n      }\n      return solutions[faultType] || '请联系专业维修人员'\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n/* 主容器现代化样式 */\n.home-container {\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);\n\n  // 科技感粒子背景增强\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-image:\n      radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, rgba(255, 107, 53, 0.08) 0%, transparent 50%),\n      radial-gradient(circle at 50% 50%, rgba(0, 200, 150, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n    animation: particleFloat 20s ease-in-out infinite;\n  }\n}\n\n/* 标题样式现代化 */\n.headtxt {\n  text-align: center;\n  padding: 20px 0;\n  position: relative;\n  z-index: 10;\n\n  .main-title {\n    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;\n    font-size: 2.2em;\n    font-weight: 700;\n    margin: 0;\n    background: linear-gradient(135deg, $techBlue, $techBlueLight);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12px;\n\n    .title-icon {\n      font-size: 1.2em;\n      animation: pulse 2s infinite;\n    }\n\n    .title-subtitle {\n      font-size: 0.4em;\n      color: $textSecondary;\n      font-weight: 400;\n      margin-top: 8px;\n      letter-spacing: 2px;\n    }\n  }\n}\n\n/* 3D模型容器现代化 */\n#model-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border: 1px solid $borderPrimary;\n  border-radius: 16px;\n  background: rgba(26, 29, 41, 0.3);\n  backdrop-filter: blur(10px);\n  box-shadow: $shadowPrimary;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg,\n      rgba(0, 212, 255, 0.02) 0%,\n      transparent 50%,\n      rgba(255, 107, 53, 0.02) 100%);\n    pointer-events: none;\n  }\n}\n\n.ctl {\n  position: absolute;\n  left: 50%;\n  top: 20px;\n  transform: translateX(-50%);\n  z-index: 100;\n\n  .el-button-group {\n    .modern-button {\n      background: linear-gradient(135deg, $techBlue, $techBlueDark);\n      border: none;\n      color: white;\n      padding: 12px 24px;\n      border-radius: 8px;\n      font-weight: 500;\n      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n      box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);\n\n      &:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);\n        background: linear-gradient(135deg, $techBlueLight, $techBlue);\n      }\n\n      i {\n        margin-right: 8px;\n      }\n    }\n  }\n}\n\n.label-col {\n  padding: 8px 5px;\n}\n\n#gui_container {\n  position: absolute;\n  top: 84%;\n  left: 81%;\n}\n\n#gui {\n  transform: translate(-50%, -75px);\n}\n\n#infoBox {\n  position: absolute;\n  padding: 20px;\n  background: $bgCard;\n  backdrop-filter: blur(10px);\n  border: 1px solid $borderPrimary;\n  border-radius: 12px;\n  color: $textPrimary;\n  font-size: 16px;\n  min-width: 200px;\n  width: 400px;\n  box-shadow: $shadowPrimary;\n  transition: all 0.3s ease;\n  z-index: 100;\n\n  &:hover {\n    border-color: $borderHover;\n    box-shadow: $shadowHover;\n  }\n\n  ul {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n    color: $textPrimary;\n\n    li {\n      margin: 8px 0;\n      line-height: 1.6;\n      color: $textPrimary;\n    }\n  }\n}\n\n/* 现代化系统信息面板样式 */\n#systemInfoBox {\n  position: absolute;\n  right: 20px;\n  top: 20px;\n  min-width: 320px;\n  width: 380px;\n  background: $bgCard;\n  backdrop-filter: blur(15px);\n  border: 1px solid $borderPrimary;\n  border-radius: 16px;\n  box-shadow: $shadowPrimary;\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  z-index: 100;\n\n  &:hover {\n    border-color: $borderHover;\n    box-shadow: $shadowHover;\n    transform: translateY(-2px);\n  }\n\n  .card-header {\n    padding: 20px 24px 16px;\n    border-bottom: 1px solid $borderSecondary;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      background: rgba(0, 212, 255, 0.05);\n    }\n\n    .card-title {\n      color: $textPrimary;\n      font-size: 18px;\n      font-weight: 600;\n      margin: 0;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      i {\n        color: $techBlue;\n        font-size: 20px;\n      }\n    }\n\n    .header-controls {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .status-indicator {\n      padding: 4px 12px;\n      border-radius: 12px;\n      font-size: 12px;\n      font-weight: 600;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n\n      &.online {\n        background: rgba(0, 200, 150, 0.2);\n        color: #00c896;\n        border: 1px solid rgba(0, 200, 150, 0.3);\n      }\n\n      &.offline {\n        background: rgba(255, 71, 87, 0.2);\n        color: #ff4757;\n        border: 1px solid rgba(255, 71, 87, 0.3);\n      }\n    }\n\n    .collapse-btn {\n      background: rgba(0, 212, 255, 0.1);\n      border: 1px solid rgba(0, 212, 255, 0.3);\n      border-radius: 6px;\n      width: 32px;\n      height: 32px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      color: $techBlue;\n\n      &:hover {\n        background: rgba(0, 212, 255, 0.2);\n        border-color: rgba(0, 212, 255, 0.5);\n        transform: scale(1.1);\n      }\n\n      i {\n        font-size: 14px;\n        transition: transform 0.3s ease;\n      }\n\n      &.collapsed i {\n        transform: rotate(180deg);\n      }\n    }\n  }\n\n  .card-content {\n    padding: 20px 24px;\n    transition: all 0.3s ease;\n    overflow: hidden;\n  }\n\n  // 折叠状态样式\n  &.collapsed {\n    .card-header {\n      border-bottom: none;\n    }\n\n    .card-content {\n      max-height: 0;\n      padding: 0 24px;\n      opacity: 0;\n    }\n  }\n\n  .info-grid {\n    display: grid;\n    gap: 16px;\n  }\n\n  .info-item {\n    display: flex;\n    align-items: center;\n    padding: 12px 16px;\n    background: rgba(255, 255, 255, 0.03);\n    border-radius: 8px;\n    border: 1px solid rgba(255, 255, 255, 0.05);\n    transition: all 0.3s ease;\n\n    &:hover {\n      background: rgba(0, 212, 255, 0.05);\n      border-color: rgba(0, 212, 255, 0.2);\n      transform: translateX(4px);\n    }\n\n    .info-icon {\n      font-size: 20px;\n      margin-right: 12px;\n      width: 24px;\n      text-align: center;\n    }\n\n    .info-details {\n      flex: 1;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .info-label {\n        color: $textSecondary;\n        font-size: 14px;\n        font-weight: 500;\n      }\n\n      .info-value {\n        color: $textPrimary;\n        font-size: 14px;\n        font-weight: 600;\n        background: linear-gradient(135deg, $techBlue, $techBlueLight);\n        -webkit-background-clip: text;\n        -webkit-text-fill-color: transparent;\n        background-clip: text;\n      }\n    }\n  }\n}\n\n/* 故障详情对话框样式 */\n.fault-details {\n  padding: 10px;\n}\n\n.fault-details h3 {\n  color: #F56C6C;\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n  text-align: center;\n  border-bottom: 1px solid #EBEEF5;\n  padding-bottom: 10px;\n}\n\n.fault-details p {\n  margin: 10px 0;\n  line-height: 1.6;\n}\n\n.fault-details strong {\n  color: #303133;\n  display: inline-block;\n  width: 100px;\n  vertical-align: top;\n}\n\n/* 自定义El-Alert样式 */\n.el-message-box {\n  width: 500px !important;\n  max-width: 90%;\n}\n\n.el-message-box__content {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n/* 动画效果 */\n@keyframes particleFloat {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n    opacity: 1;\n  }\n  33% {\n    transform: translateY(-20px) rotate(120deg);\n    opacity: 0.8;\n  }\n  66% {\n    transform: translateY(10px) rotate(240deg);\n    opacity: 0.9;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.05);\n  }\n}\n\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  #systemInfoBox {\n    width: 300px;\n    min-width: 280px;\n  }\n\n  .headtxt .main-title {\n    font-size: 1.8em;\n  }\n}\n\n@media (max-width: 768px) {\n  #systemInfoBox {\n    position: relative;\n    right: auto;\n    top: auto;\n    width: 100%;\n    margin: 20px;\n\n    .info-grid {\n      grid-template-columns: 1fr;\n    }\n  }\n\n  .ctl {\n    left: 50%;\n    top: 10px;\n  }\n\n  .headtxt .main-title {\n    font-size: 1.5em;\n    flex-direction: column;\n    gap: 8px;\n  }\n}\n\n/* Element UI 组件样式覆盖 */\n:deep(.el-button-group) {\n  .el-button {\n    border: none;\n\n    &:first-child {\n      border-radius: 8px 0 0 8px;\n    }\n\n    &:last-child {\n      border-radius: 0 8px 8px 0;\n    }\n\n    &:only-child {\n      border-radius: 8px;\n    }\n  }\n}\n\n:deep(.el-dialog) {\n  background: $bgCard;\n  backdrop-filter: blur(10px);\n  border: 1px solid $borderPrimary;\n  border-radius: 16px;\n\n  .el-dialog__header {\n    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));\n    border-bottom: 1px solid $borderSecondary;\n    border-radius: 16px 16px 0 0;\n\n    .el-dialog__title {\n      color: $textPrimary;\n      font-weight: 600;\n    }\n  }\n\n  .el-dialog__body {\n    color: $textSecondary;\n  }\n}\n</style>\n\n"], "sourceRoot": "src/views/home"}]}