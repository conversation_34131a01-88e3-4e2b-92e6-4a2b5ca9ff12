
2fc67a0277bb899b9d5f1044b55c1c7287cb4cb8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"787945bd575f90689b0fda79591bbff0\"}","integrity":"sha512-A6/itvMOLMpC7JcG0gl8oIYEYiaA+NTyO4XUdU7nj0K6NPYvgkfS60K0awBf0GcLsAQ8cu+O0bMNTyZLy2HHPA==","time":1754204839208,"size":107453}