
68fabab925ec1f756e3e1ed7b3c145b5586f4e76	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.65d8357937cc254a7c13.hot-update.js\",\"contentHash\":\"e27c5f28827f6a90440c667dc47c6a0a\"}","integrity":"sha512-vEM6ar0oOo0+6kLjgz+FEaSUGTE9LGp9ePe486uFwVfnMGx+59AySpD+MOLfRT3XYjjlJxps0Hnnf2qnWb5jFQ==","time":1754202879709,"size":60726}