
e4c4e0a7db24d8cbcaa31bea534c9a512bd3c663	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"40ef43f4f0418b871e79e2364aa70f6d\"}","integrity":"sha512-R7+yKTmc8g9jtj9YJsd7EEtf6TZUnAitRqeBKSWfzBA0n1jHN6eahE4UIfYaviWXC8TjcsZ24vJkDh8mNicK4w==","time":1754200970670,"size":275020}