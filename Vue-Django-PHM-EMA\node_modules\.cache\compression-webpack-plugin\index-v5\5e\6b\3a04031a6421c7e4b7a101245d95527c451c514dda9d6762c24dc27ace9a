
ff46d159f8b7d40cab60348ea56fc49f7f104852	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"54320d001500745eda5f4be2e16d8d87\"}","integrity":"sha512-zfdHbclxkMSWKDaVbLTMsCgCQL/ARBV2awnKMFEUXH5Mx7/SX4utKVaDebrbd+3LlM5P7Mmdh97E+Xnuatq3cg==","time":1754204299203,"size":23565}