
d85e639ea210b5f3eae1b3971cac0af70d9711b6	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"70d231b35f1ba7dbda02791b26c5526a\"}","integrity":"sha512-0pXCBmC3gz0gQLZX9c0/zWoyEdhixHd3YmThvkjgTj26apVtaeBud5lQKWzYpqIhbP1hU1PaoJoihDz9JNkeFQ==","time":1754203186094,"size":26720}