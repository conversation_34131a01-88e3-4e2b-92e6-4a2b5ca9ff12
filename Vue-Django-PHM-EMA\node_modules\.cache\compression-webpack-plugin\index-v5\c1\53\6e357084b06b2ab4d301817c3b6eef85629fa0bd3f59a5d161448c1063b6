
0fbefbbf762ec8df7381e854d670bfda78250ffd	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"3.2f7a05f5e0fd5d17e4b4.hot-update.js\",\"contentHash\":\"95b31f06684e266ccbec9eeae5ad7d8b\"}","integrity":"sha512-IK/O5Puidnwu6oR8fLnciE888nRg3uTA0Ho1KVwc18Z648UyNMqfqf+9lMxFLDZtDztDXu9DMQQNbU7gVjBjnw==","time":1754201768294,"size":61116}