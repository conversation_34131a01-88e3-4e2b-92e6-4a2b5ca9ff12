
0485a5df2c2cf7ab1b891095bc950ada9845585b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"b0e1d3d858f9f49d432eeeb0dadd708c\"}","integrity":"sha512-jvbK2k8CxdVqZJRtKUqPdYMRU+Wuw5ZibsPgUqvkGLVgun+d2cta+kYhTJI4yqUkrPNQXqJ7/an7PwXh6KeDyw==","time":1754204194488,"size":154469}