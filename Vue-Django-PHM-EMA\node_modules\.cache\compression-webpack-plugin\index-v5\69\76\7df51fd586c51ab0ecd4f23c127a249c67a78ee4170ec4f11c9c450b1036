
b94e19aeca4d67acf733925d1297747fe5ea337f	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"86262e37ed0a36200d5f4b41e02b75fd\"}","integrity":"sha512-21/lWL3xhPw8LH3RZ6qShRZYongaqqahiF3xziQPgSn/jQhHIO3+eMuaMv6CqPDUwEUzoDb00mu+hzJb2Xg6LQ==","time":1754205154714,"size":87046}