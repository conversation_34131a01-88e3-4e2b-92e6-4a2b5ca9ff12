
2b578702e361d53766241059af8b154f611e7673	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"5.a56cf2fcb7f3f366770e.hot-update.js\",\"contentHash\":\"5e37107d67e6bcadd4ca83d067dad212\"}","integrity":"sha512-qCdT4ddDkGXGK4s0L9jhktxzQP3lF8Hr3MfNh4P7wnC96hxjfFSoR9lNXrLjqNeD9rdspxKIiK36MxWs5aUoRQ==","time":1754206071754,"size":13301}