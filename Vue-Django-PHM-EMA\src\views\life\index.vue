<template>
  <div class="life-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-data-analysis"></i>
        寿命预测与健康评估
        <span class="page-subtitle">Life Prediction & Health Assessment</span>
      </h2>
    </div>

    <!-- 健康度展示区域 -->
    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="16">
        <el-card class="health-trend-card">
          <div slot="header">
            <span>系统健康度趋势</span>
          </div>
          <div v-loading="loading" class="chart-container">
            <div id="healthTrendChart" class="chart" />
            <div v-if="!healthData.trend || healthData.trend.length === 0" class="empty-content">
              <i class="el-icon-data-line"></i>
              <p>暂无健康度趋势数据</p>
              <p class="tip">请先进行故障诊断</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="gauge-card">
          <div slot="header">
            <span>当前健康度</span>
          </div>
          <div v-loading="loading" class="chart-container">
            <div id="healthGaugeChart" class="chart" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="12">
        <!-- RUL预测展示 -->
        <el-card class="rul-card">
          <div slot="header">
            <span>剩余使用寿命预测</span>
          </div>
          <div v-loading="loading" class="rul-content">
            <h2 class="rul-value">{{ rulValue }} 小时</h2>
            <el-progress :percentage="rulPercentage" :format="format" :color="rulColor"></el-progress>
            <p class="rul-description">预计剩余使用寿命，基于当前系统运行状态</p>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <!-- 部件健康状态 -->
        <el-card class="components-card">
          <div slot="header">
            <span>关键部件健康状态</span>
          </div>
          <div v-loading="loading" class="chart-container">
            <div id="componentsHealthChart" class="chart" />
            <div v-if="!healthData.components || healthData.components.length === 0" class="empty-content">
              <i class="el-icon-data-analysis"></i>
              <p>暂无部件健康状态数据</p>
              <p class="tip">请先进行故障诊断</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 报告生成按钮 -->
    <div class="actions-container">
      <el-button type="primary" icon="el-icon-document" :loading="reportGenerating" @click="generateReport">
        生成健康评估报告
      </el-button>
      <el-button type="primary" icon="el-icon-refresh" @click="fetchData(true)">
        刷新数据
      </el-button>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getHealthPrediction, generateHealthReport } from '@/api/health'
import { saveAs } from 'file-saver'

export default {
  data() {
    return {
      healthTrendChart: null,
      healthGaugeChart: null,
      componentsHealthChart: null,
      rulValue: '0',
      rulPercentage: 0,
      rulColor: '#409EFF',
      loading: false,
      reportGenerating: false,
      healthData: {
        trend: [],
        current: 0,
        components: []
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts()

      // 检查是否有从故障诊断页面传来的数据
      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {
        this.handleDiagnosisResult()
      } else {
        // 如果没有从故障诊断页面传来数据，则显示初始状态
        this.resetData()
      }
    })
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.healthTrendChart && this.healthTrendChart.dispose()
    this.healthGaugeChart && this.healthGaugeChart.dispose()
    this.componentsHealthChart && this.componentsHealthChart.dispose()
  },
  methods: {
    initCharts() {
      console.log('初始化图表...')
      this.healthTrendChart = echarts.init(document.getElementById('healthTrendChart'))
      this.healthGaugeChart = echarts.init(document.getElementById('healthGaugeChart'))
      this.componentsHealthChart = echarts.init(document.getElementById('componentsHealthChart'))

      // 设置仪表盘容器样式，确保仪表盘居中显示
      document.getElementById('healthGaugeChart').parentNode.style.display = 'flex'
      document.getElementById('healthGaugeChart').parentNode.style.justifyContent = 'center'
      document.getElementById('healthGaugeChart').parentNode.style.alignItems = 'center'

      this.updateCharts()
      window.addEventListener('resize', this.resizeCharts)
    },
    fetchData(isReset = false) {
      this.loading = true
      console.log('开始获取健康评估数据... reset:', isReset)

      // 构造请求参数
      const params = {}

      // 如果是重置请求
      if (isReset) {
        params.reset = 'true'
      } else if (this.$store.getters['diagnosis/hasUnprocessedResult']) {
        const diagnosisResult = this.$store.getters['diagnosis/currentDiagnosisResult']
        if (diagnosisResult && diagnosisResult.success) {
          const fault_mode = diagnosisResult.diagnosis_details.conclusion.predicted_fault_mode
          params.fault_mode = fault_mode
        }
      }

      // 调用API获取健康评估和寿命预测数据
      getHealthPrediction(params)
        .then(response => {
          console.log('API返回数据:', response)
          if (response.code === 200) {
            const data = response.data

            // 更新健康度趋势数据
            this.healthData.trend = data.health_trend || []

            // 更新当前健康度
            this.healthData.current = data.current_health || 0

            // 更新部件健康状态数据
            this.healthData.components = data.component_health || []

            // 更新RUL预测结果，将小数舍入为整数
            this.rulValue = data.rul_hours ? Math.round(parseFloat(data.rul_hours)) : '0'
            this.rulPercentage = data.rul_percentage ? Math.round(parseFloat(data.rul_percentage)) : 0

            // 如果是从故障诊断页面跳转来的，标记诊断结果已处理
            if (this.$store.getters['diagnosis/hasUnprocessedResult']) {
              this.$store.dispatch('diagnosis/processDiagnosisResult')
            }

            this.updateCharts()
          } else {
            console.error('API返回错误:', response)
            this.$message.error(response.message || '获取数据失败')
            // 不再调用mockData，改为显示初始状态
            this.resetData()
          }
        })
        .catch(error => {
          console.error('获取健康评估数据失败:', error)
          this.$message.error('获取数据失败，请稍后重试')
          // 显示详细错误信息
          console.log('错误详情:', {
            message: error.message,
            stack: error.stack,
            response: error.response && {
              status: error.response.status,
              statusText: error.response.statusText,
              data: error.response.data
            }
          })
          // 不再调用mockData，改为显示初始状态
          this.resetData()
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 处理从故障诊断页面传来的诊断结果
    handleDiagnosisResult() {
      const diagnosisResult = this.$store.getters['diagnosis/currentDiagnosisResult']
      console.log('处理故障诊断结果:', diagnosisResult)

      if (diagnosisResult && diagnosisResult.success) {
        // 获取故障模式
        const fault_mode = diagnosisResult.diagnosis_details.conclusion.predicted_fault_mode

        // 获取健康评估结果
        this.fetchData()

        // 显示通知
        this.$notify({
          title: '诊断结果应用',
          message: `已根据诊断结果(${this.getFaultModeName(fault_mode)})更新健康评估`,
          type: 'info',
          duration: 5000
        })
      }
    },
    // 重置数据为初始状态
    resetData() {
      this.healthData.trend = []
      this.healthData.current = 0
      this.healthData.components = [
        { name: '电机', value: 0 },
        { name: '控制器', value: 0 },
        { name: '减速器', value: 0 },
        { name: '传感器', value: 0 }
      ]
      this.rulValue = '0' // 已经是整数
      this.rulPercentage = 0
      this.updateCharts()
    },
    // 获取故障模式名称
    getFaultModeName(fault_mode) {
      // 从故障诊断的faultModeMap获取故障名称
      const faultModeMap = {
        '0_normal': '正常状态',
        '1_degradation_magnet': '永磁体退磁退化',
        '2_degradation_brush_wear': '电刷磨损退化',
        '3_degradation_commutator_oxidation': '换向器氧化退化',
        '4_fault_stator_short': '定子绕组短路故障',
        '5_fault_rotor_open': '转子绕组开路故障',
        '6_degradation_bearing_wear': '轴承磨损退化',
        '7_fault_bearing_stuck': '轴承卡死故障',
        '8_degradation_gear_wear': '齿轮磨损退化',
        '9_degradation_sensor_drift': '传感器漂移退化',
        '10_fault_sensor_loss': '传感器失效故障',
        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',
        '12_degradation_drive_distortion': '驱动信号失真退化',
        '13_fault_mcu_crash': 'MCU崩溃故障'
      }

      return faultModeMap[fault_mode] || '未知故障'
    },
    updateCharts() {
      // 更新健康度趋势图表
      this.healthTrendChart.setOption({
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.healthData.trend.map(item => item[0]),
          name: '检测时间点',
          nameLocation: 'middle',
          nameGap: 30,
          axisLabel: {
            interval: 'auto',
            rotate: 45,
            formatter: (value) => {
              // 只显示月份和日期，不显示年份
              const date = new Date(value)
              if (!isNaN(date)) {
                return `${date.getMonth() + 1}月${date.getDate()}日`
              }
              return value
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '健康度',
          min: 0,
          max: 100
        },
        grid: {
          bottom: '15%' // 为旋转后的横轴标签留出更多空间
        },
        series: [{
          name: '系统健康度',
          type: 'line',
          data: this.healthData.trend.map(item => item[1]),
          markLine: {
            data: [{
              yAxis: 60,
              lineStyle: { color: '#FF9800' },
              label: { formatter: '警戒线' }
            }]
          },
          lineStyle: {
            width: 3,
            color: '#409EFF'
          },
          smooth: true
        }]
      })

      // 更新健康度仪表盘
      this.healthGaugeChart.setOption({
        series: [{
          type: 'gauge',
          data: [{ value: this.healthData.current, name: '健康度' }],
          min: 0,
          max: 100,
          axisLine: {
            lineStyle: {
              width: 30,
              color: [
                [0.3, '#FF5722'],
                [0.7, '#FF9800'],
                [1, '#4CAF50']
              ]
            }
          },
          radius: '85%', // 缩小仪表盘半径，为刻度标签留出更多空间

          detail: {
            formatter: '{value}%',
            fontSize: 28,
            fontWeight: 'bold',
            offsetCenter: [0, '70%'], // 将数值放在下方
            color: '#333', // 使用更深的颜色提高对比度
            textShadow: '0 0 3px rgba(255,255,255,0.5)' // 添加文字阴影提高可读性
          },
          title: {
            fontSize: 18,
            fontWeight: 'normal',
            offsetCenter: [0, '50%'], // 将标题放在数值上方
            color: '#333'
          },
          pointer: {
            width: 6, // 增加指针宽度提高可见性
            length: '75%' // 稍微缩短指针，避免与刻度重叠
          },
          axisTick: {
            length: 6, // 减小刻度线长度，避免与指针重叠
            lineStyle: {
              width: 2, // 保持刻度线宽度
              color: '#666' // 加深刻度线颜色
            },
            distance: -8 // 向内偏移刻度线，确保不与刻度数字重叠
          },
          splitLine: {
            length: 18, // 增加分割线长度，让刻度标签与分割线有更明显的分隔
            lineStyle: {
              width: 3, // 增加分割线宽度
              color: '#666' // 加深分割线颜色
            },
            distance: -3 // 向内偏移分割线，远离刻度数字
          },
          axisLabel: {
            distance: 30, // 进一步增加标签与刻度线的距离
            fontSize: 12, // 稍微减小字体大小以减少重叠
            color: '#333', // 加深文字颜色提高对比度
            formatter: function(value) {
              // 减少刻度显示，只显示0、20、40、60、80、100的刻度值
              if (value % 20 === 0) {
                return value.toFixed(0) + ''
              } else {
                return '' // 其他刻度不显示数字
              }
            },
            backgroundColor: 'rgba(255, 255, 255, 0.8)', // 添加半透明背景色
            padding: [2, 4], // 添加内边距
            borderRadius: 3, // 圆角边框
            textShadow: '0 0 2px #fff' // 添加文字阴影提高可读性
          }
        }]
      })

      // 更新部件健康状态柱状图
      this.componentsHealthChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: this.healthData.components.map(item => item.name)
        },
        yAxis: {
          type: 'value',
          name: '健康度',
          min: 0,
          max: 100
        },
        series: [{
          type: 'bar',
          data: this.healthData.components.map(item => ({
            value: item.value,
            itemStyle: {
              color: this.getHealthColor(item.value)
            }
          })),
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%'
          }
        }]
      })
    },
    getHealthColor(value) {
      if (value >= 80) {
        return '#4CAF50'
      } else if (value >= 60) {
        return '#FF9800'
      } else {
        return '#FF5722'
      }
    },
    resizeCharts() {
      this.healthTrendChart && this.healthTrendChart.resize()
      this.healthGaugeChart && this.healthGaugeChart.resize()
      this.componentsHealthChart && this.componentsHealthChart.resize()
    },
    format(percentage) {
      return percentage + '%'
    },
    generateReport() {
      this.reportGenerating = true
      console.log('开始生成健康评估报告...')

      // 调用API生成健康评估报告
      generateHealthReport({
        health_data: {
          current_health: this.healthData.current,
          component_health: this.healthData.components,
          rul_hours: this.rulValue,
          rul_percentage: this.rulPercentage
        }
      })
        .then(response => {
          console.log('报告生成成功, 响应类型:', response.headers['content-type'])
          // 使用file-saver库将二进制数据保存为Excel文件
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })

          // 生成文件名：健康评估报告_年月日时分秒.xlsx
          const now = new Date()
          const fileName = `健康评估报告_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}.xlsx`

          saveAs(blob, fileName)

          this.$message.success('健康评估报告生成成功')
        })
        .catch(error => {
          console.error('生成健康评估报告失败:', error)
          // 显示详细错误信息
          console.log('错误详情:', {
            message: error.message,
            stack: error.stack,
            response: error.response && {
              status: error.response.status,
              statusText: error.response.statusText,
              data: error.response.data
            }
          })
          this.$message.error('生成报告失败，请稍后重试')
        })
        .finally(() => {
          this.reportGenerating = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.life-container {
  margin: 30px;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);
  padding: 24px;

  /* 页面标题样式 */
  .page-header {
    margin-bottom: 32px;
    text-align: center;

    .page-title {
      font-size: 2em;
      font-weight: 700;
      margin: 0;
      background: linear-gradient(135deg, #00d4ff, #33ddff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;

      i {
        font-size: 1.2em;
        color: #00d4ff;
      }

      .page-subtitle {
        font-size: 0.4em;
        color: #b8c5d1;
        font-weight: 400;
        margin-top: 8px;
        letter-spacing: 1px;
        display: block;
      }
    }
  }
}

.dashboard-row {
  margin-bottom: 20px;
}

.health-trend-card,
.gauge-card,
.rul-card,
.components-card {
  height: 350px;

  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .chart {
      width: 100%;
      height: 100%;
    }

    .empty-content {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 1;
      text-align: center;
      color: #909399;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #c0c4cc;
      }

      p {
        margin: 5px 0;
        font-size: 16px;
      }

      .tip {
        font-size: 14px;
        color: #a0a4a9;
      }
    }
  }
}

.rul-content {
  padding: 20px;
  text-align: center;

  .rul-value {
    font-size: 32px;
    margin-bottom: 20px;
    color: #409EFF;
  }

  .rul-description {
    margin-top: 20px;
    color: #606266;
    font-size: 14px;
  }
}

.actions-container {
  text-align: center;
  margin-top: 20px;

  .el-button {
    margin: 0 10px;
  }
}
</style>
