
120b66c689cc2ca7fa785f3b9c4b96b144f6d6b2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"0.b294548ef659d88d5289.hot-update.js\",\"contentHash\":\"fa12527c1444323a02fc904bc4a8043d\"}","integrity":"sha512-Sb40eO3oPOtULWgjnQvua5AXff8BUd/PmA/fNSWc6dtvIvJfkse/DHvxH106cOHIRRNrJOe+g6vG6h6QVFED+A==","time":1754201072936,"size":28998}