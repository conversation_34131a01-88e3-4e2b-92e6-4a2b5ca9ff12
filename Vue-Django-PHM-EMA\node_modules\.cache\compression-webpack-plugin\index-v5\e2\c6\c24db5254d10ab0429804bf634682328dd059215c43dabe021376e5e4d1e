
24def7a9ea19c8cacb4952501f8a74079ec1d8b6	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"5f67efe00e80e9d37b88e609b19a36ca\"}","integrity":"sha512-4xzG8dH9Ut0U66jmtIusdPetAH06Q1VFAJklxNwFsOU6EdD0/fluUq6Q5vttQDjGmaRS0oACWPI4ITYeH4RE3A==","time":1754204281144,"size":26710}