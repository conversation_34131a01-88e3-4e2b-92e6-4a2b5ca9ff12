
b5a65716ef12cecd7d3b1bc8583b642abb3127be	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"94e1ff144eb840680b33950da2f610b3\"}","integrity":"sha512-2M6NXe90qrz/yGlSjrKceqWHSNEO4yLZ7lM83vArHPlRLfe6i8Y1r4zSaf5nEX5TbnDBfxLSllnGqyPtpFL6gA==","time":1754201073387,"size":277056}