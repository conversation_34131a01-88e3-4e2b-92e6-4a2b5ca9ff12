
be99d14512705a9df05dfd4fcc488eeae04f657e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"db74acfcb0b58ddbe97548e0c350ad2a\"}","integrity":"sha512-JJPLzTlV4sjCy0skqjj9qJS3rPioWO2nd0YwsubexNLXu+s3bzab9xfUUOKd7Fs08scLUMEfjIAgGz4MVPdQ6g==","time":1754203844726,"size":148856}