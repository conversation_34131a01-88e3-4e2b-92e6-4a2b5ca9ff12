
0027bc53487badeb7a97cecfcb22af4de05761a9	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F4.js\",\"contentHash\":\"2e3125c6dccdffc8a4ffe05b075a98e1\"}","integrity":"sha512-5GfAbNDpYsX2Ji7HSv1fp1T4ADhUE0dCJFe9svp314DN5UvrRqUBNOCA6gx67U87S9+s7TcuBJkAyzJ+EEIjuw==","time":1754205174013,"size":103697}