
047dd18cbd1f81b1098702fa547d352ea763629b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.6fad956d7762a987d74f.hot-update.js\",\"contentHash\":\"90c20e6b8e62951064fca941336d3aa7\"}","integrity":"sha512-OwXx4tC04+l0wU8xnW422TgVnadKsEPgoUlcffI5MJnft7ojkTDi/jaKS5yXy6IW5/G+oh2DG3guUisLuIYXtg==","time":1754204154006,"size":24374}