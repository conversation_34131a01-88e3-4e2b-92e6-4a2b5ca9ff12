
7183f75d6ddd091f906b3aa08817ac623e81c94a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"f6066f8a40b0c6b94b3ed8a5380d92b5\"}","integrity":"sha512-4bhjylN1IdbvdKeNX1B+aInL2bP4oVXSt2450QRfzIeeTvAZa5J+h8sjmCp15qdJAlBz1O6r31MUkQEB7EUZPA==","time":1754202922678,"size":23570}