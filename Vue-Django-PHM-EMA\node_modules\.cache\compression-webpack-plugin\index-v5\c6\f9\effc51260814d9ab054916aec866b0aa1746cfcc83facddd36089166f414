
0e79e3bbd5af052aaf6e878b24f0f136a1a48209	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"9.511ee9144741c1ccfa1a.hot-update.js\",\"contentHash\":\"930c72236f0817c4f0469cb87db64d12\"}","integrity":"sha512-27pNHwfxE57AOcEY8J/p/jW7Pid7P0w4A0v1zctTFLVTYgajT/1PL07qxMDd91c5monko3YfJAmp0YMoqS+0Kg==","time":1754202922678,"size":30381}