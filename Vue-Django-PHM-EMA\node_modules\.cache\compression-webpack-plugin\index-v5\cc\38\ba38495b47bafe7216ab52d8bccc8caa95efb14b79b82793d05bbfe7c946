
2f40a73184f0a4ccae79e69f0d55be556996e49b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"cf4379985ca695c9e2d75e74a9cfb1db\"}","integrity":"sha512-zd7EVUofybvdcoog62G6U2seX7Yy/W9hwbpWwkgIzrkAMDrzaJIeK8xclJlpSlxGXeJKlwOCVGd9csZfXIXtZg==","time":1754201014419,"size":26723}