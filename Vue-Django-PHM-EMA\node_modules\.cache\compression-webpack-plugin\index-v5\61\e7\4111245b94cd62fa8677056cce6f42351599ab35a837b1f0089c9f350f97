
05341cfc2cca915a8e23f2f7f7c162793d4fe2d5	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"0.a0205138a9d2907055e3.hot-update.js\",\"contentHash\":\"04c59fee3192f54fae76e45ccad31644\"}","integrity":"sha512-rCMV7r1CYAdBgZzo+J6bA1oHScgZg8vlyz4QekVo/PBD56QzlRknAvbTTitgi/Dcd8rdEp73Dmt7iTIfK4G0FA==","time":1754201059745,"size":28638}