
ea34b3d6b6c5234eaac2a9c68d89808dc7bc5d18	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"b0e1d3d858f9f49d432eeeb0dadd708c\"}","integrity":"sha512-jYgk9J/9WDMSvI9w/8rLvnG4oD2QfyZGUHZWMYf7slq6eOFZqRQtjS9kAYvcIW1+udmzgAA5rcH15aoPzhOxNw==","time":1754204194917,"size":117263}