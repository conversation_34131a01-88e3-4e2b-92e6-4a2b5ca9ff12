
354f9a07ebe704ce0923e49ae4616cbe01647af4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"4.f8903a9b3919588f49e3.hot-update.js\",\"contentHash\":\"0420ae18b2f11686f366732ac6dbd79c\"}","integrity":"sha512-N2QEO/oXOpKcNXvl2Jxe+xoX85CJ/HUyw1Dnoze24obPysdSM+A1/Z1kRREkjHAW3WPpRM066g6m39IBXvuaVw==","time":1754205498260,"size":16859}