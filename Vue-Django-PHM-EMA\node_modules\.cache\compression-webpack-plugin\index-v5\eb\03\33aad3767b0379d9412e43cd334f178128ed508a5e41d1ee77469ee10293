
229b50dbf8be77998ac2e5670e8a7722502aaeda	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"app.4c8fb6cdea279c7cee3b.hot-update.js\",\"contentHash\":\"bb562b9807300d21afc8c0192d1975e4\"}","integrity":"sha512-viefc7GMJB0tJfD/5uHpY+0OB8stLEO8VeyF+Xc4voMO68LUyqAIU5Zwlslmhrrj2wwCEk2Rk1bdpKVVZ7zG/A==","time":1754204790615,"size":12494}