
e7a9d81ebbf9060c7f44f3c8d97757a5141f247e	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"6381e782109f7a53c6ff911b40c40beb\"}","integrity":"sha512-maLiwLbuRMfXlAWZw1GFSj/zFeKGY6drKSsFbtj9Ah70x0+9zTXYsL3dKgm44iA/aqh+NA5RhlsAZRI3qQL3Ew==","time":1754202942690,"size":108869}