
0938870ebf2e59ea1f7fd24ec55f4f3346c1dcf8	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"2.ea3a0d5b384a67a5d06f.hot-update.js\",\"contentHash\":\"7959e4fb819b17c2f7aef3521769807a\"}","integrity":"sha512-yHUX1E2IilMRFZ/OlZt5GupjP/uoNkePVzKxzv1kHziwHzmz9kp8hojrV6DxFsdO11o97MXTMV3RoS0I+UDkRg==","time":1754206041786,"size":20883}