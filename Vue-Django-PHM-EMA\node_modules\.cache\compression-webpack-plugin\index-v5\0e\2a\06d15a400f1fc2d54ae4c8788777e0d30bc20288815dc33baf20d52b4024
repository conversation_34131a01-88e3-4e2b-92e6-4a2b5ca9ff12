
85c94072b66ae84db4aa2e1dfd51e1350163835a	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"9e9bef438a349c3f21f3d1b167dd7e38\"}","integrity":"sha512-EtlRyIJm7rWQTRpQQjQAl65ChdNzip47mZ3JEAPEQ1QEsISsteX6CQSN0LK6M5LqHt4AlXPn0DqM1sgXSPzxdw==","time":1754204006599,"size":152210}