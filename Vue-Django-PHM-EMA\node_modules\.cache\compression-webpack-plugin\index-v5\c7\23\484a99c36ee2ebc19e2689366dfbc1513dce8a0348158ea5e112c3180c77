
04c0777d088a6e4f4acaac12935c5d2dd99cf6d1	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"479d4a9bba164a03b6073c5772a2bc02\"}","integrity":"sha512-wLaZ4kX3hEVCM8q9NISVuI1Da1TSCkoBJ20jfjQe6nP5Li+UBA2k4sSFM5lcD80ib0t7fWq5qpMsUTAJsZqMow==","time":1754201768293,"size":26761}