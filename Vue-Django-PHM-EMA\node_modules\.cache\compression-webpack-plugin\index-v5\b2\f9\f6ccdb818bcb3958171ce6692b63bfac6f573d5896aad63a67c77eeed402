
cc2467f5c7f95267b2345af9df5106b5b9ffa836	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"app.ddda577373b6380a0c2a.hot-update.js\",\"contentHash\":\"ad3b9b26b7717e50cb5763544d6ff8a2\"}","integrity":"sha512-CY2vsqwpA6ybJbV45FPPk1ws4ZwEFgtD/5lqnn79w4oChEINc3XCilASwmMcUeFB35hTE6Jnl2EOOWXhnq66RQ==","time":1754202854643,"size":12402}