
421ed93c2cc7ff80e3f827779482b682849f185b	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"64df4a6733c99a7ed833c970197a1fe4\"}","integrity":"sha512-aubOrm4m8JmsDA4q5Dln8yctglskTMmcQeoFWupAqh67RBlQHP2FPqTkLJu+b/P13XqkMggNrH+6CvRqfsg0rg==","time":1754201860992,"size":23535}