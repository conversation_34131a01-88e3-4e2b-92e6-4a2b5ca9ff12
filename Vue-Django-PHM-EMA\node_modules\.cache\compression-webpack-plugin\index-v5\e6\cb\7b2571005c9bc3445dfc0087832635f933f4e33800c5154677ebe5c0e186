
535240b03e459d532cca5cfb7bd879d9b84998b7	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"a31699552196e250c0daf5ebe8d19c02\"}","integrity":"sha512-js+tB/xSt9j7/w89w+Kp1afqL1P/T50QTF4lU6B/fiOUWg3wOu6MFyvs5fgfT72q1EK3xjcWDcDevkUmH1SiaA==","time":1754204860113,"size":26708}