
ef06984075a8e3e10430aa35edb88b5d1e6cc123	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"0.652386ff57efb9b7268c.hot-update.js\",\"contentHash\":\"6f04792e8f16ac04aaea451652c8096c\"}","integrity":"sha512-s1NKT27ZbP14u6EAIg8SDmQIeIHl6PyugoW77WiboBSJPkFZ246JmrsBvFCj9yfSH07vFUMPtcPzmKXvPr4z7w==","time":1754201014976,"size":104086}