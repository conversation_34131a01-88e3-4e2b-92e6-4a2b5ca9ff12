
64ee7d42921154133f3c24abe3c624fdd3d47e49	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"2564fd6d5b24fb6e4e1f28b3be7ce9ce\"}","integrity":"sha512-U3I0YLAlqx1hOMW+Y6N27shWUFInH3yxzjkwEd9Krp0beInqwBbWkr+Q9qzs67XA72scTIYapUJ9nf9K8qqJvA==","time":1754203212032,"size":112271}