
5090943f3e5df9f9e8d5e36fc4d731d6f8885fb2	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F9.js\",\"contentHash\":\"efbe147b4e3738b091b292d05e2cbc00\"}","integrity":"sha512-603BE9zG/A+5bgpx0FDKdQVjjyf608cZqh1Ow49zY1Jw+XauhJ8Um7+KwJriwBiV2rvemhCspCRfWQiadficcA==","time":1754203663877,"size":147922}