
56d88eb19058b70ccf5d04530c6dee870f3f2141	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"4ff6f1e52d7470551c0bcdedf4f95120\"}","integrity":"sha512-yhMC887V5ooskD2zABD16Fbwi076YdQMDxEuECYU0yuQBMzhvXY5otdUMg7WEH1ivXPlhOwcpArVJ2lOiCKeAw==","time":1754204790336,"size":1850157}