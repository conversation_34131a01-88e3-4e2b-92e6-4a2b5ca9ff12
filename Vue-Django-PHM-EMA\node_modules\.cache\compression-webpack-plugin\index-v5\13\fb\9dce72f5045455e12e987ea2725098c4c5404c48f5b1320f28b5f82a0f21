
f20fd3d5c1e7836ac31b3141783f7c8eb7b96e53	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"cd92c67424a658372156f588b6012a8f\"}","integrity":"sha512-ntiUaLjPECQa35iVNh5fuqa3Sox/ghapyqyAr1lhraUBp6q42v/tYa3GTeNNbAKjxkcUr8zZCwvckTrdzLbLlQ==","time":1754201060198,"size":276077}