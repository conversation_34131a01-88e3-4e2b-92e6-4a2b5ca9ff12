{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\life\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\life\\index.vue", "mtime": 1754206054570}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,OAAA,KAAA,OAAA,MAAA,SAAA;AACA,SAAA,mBAAA,EAAA,oBAAA,QAAA,cAAA;AACA,SAAA,MAAA,QAAA,YAAA;AAEA,eAAA;AACA,EAAA,IADA,kBACA;AACA,WAAA;AACA,MAAA,gBAAA,EAAA,IADA;AAEA,MAAA,gBAAA,EAAA,IAFA;AAGA,MAAA,qBAAA,EAAA,IAHA;AAIA,MAAA,QAAA,EAAA,GAJA;AAKA,MAAA,aAAA,EAAA,CALA;AAMA,MAAA,QAAA,EAAA,SANA;AAOA,MAAA,OAAA,EAAA,KAPA;AAQA,MAAA,gBAAA,EAAA,KARA;AASA,MAAA,UAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,UAAA,EAAA;AAHA;AATA,KAAA;AAeA,GAjBA;AAkBA,EAAA,OAlBA,qBAkBA;AAAA;;AACA,SAAA,SAAA,CAAA,YAAA;AACA,MAAA,KAAA,CAAA,UAAA,GADA,CAGA;;;AACA,UAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,gCAAA,CAAA,EAAA;AACA,QAAA,KAAA,CAAA,qBAAA;AACA,OAFA,MAEA;AACA;AACA,QAAA,KAAA,CAAA,SAAA;AACA;AACA,KAVA;AAWA,IAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,KAAA,YAAA;AACA,GA/BA;AAgCA,EAAA,aAhCA,2BAgCA;AACA,IAAA,MAAA,CAAA,mBAAA,CAAA,QAAA,EAAA,KAAA,YAAA;AACA,SAAA,gBAAA,IAAA,KAAA,gBAAA,CAAA,OAAA,EAAA;AACA,SAAA,gBAAA,IAAA,KAAA,gBAAA,CAAA,OAAA,EAAA;AACA,SAAA,qBAAA,IAAA,KAAA,qBAAA,CAAA,OAAA,EAAA;AACA,GArCA;AAsCA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,wBACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA;AACA,WAAA,gBAAA,GAAA,OAAA,CAAA,IAAA,CAAA,QAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,CAAA;AACA,WAAA,gBAAA,GAAA,OAAA,CAAA,IAAA,CAAA,QAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,CAAA;AACA,WAAA,qBAAA,GAAA,OAAA,CAAA,IAAA,CAAA,QAAA,CAAA,cAAA,CAAA,uBAAA,CAAA,CAAA,CAJA,CAMA;;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,kBAAA,EAAA,UAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,kBAAA,EAAA,UAAA,CAAA,KAAA,CAAA,cAAA,GAAA,QAAA;AACA,MAAA,QAAA,CAAA,cAAA,CAAA,kBAAA,EAAA,UAAA,CAAA,KAAA,CAAA,UAAA,GAAA,QAAA;AAEA,WAAA,YAAA;AACA,MAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,KAAA,YAAA;AACA,KAdA;AAeA,IAAA,SAfA,uBAeA;AAAA;;AAAA,UAAA,OAAA,uEAAA,KAAA;AACA,WAAA,OAAA,GAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,sBAAA,EAAA,OAAA,EAFA,CAIA;;AACA,UAAA,MAAA,GAAA,EAAA,CALA,CAOA;;AACA,UAAA,OAAA,EAAA;AACA,QAAA,MAAA,CAAA,KAAA,GAAA,MAAA;AACA,OAFA,MAEA,IAAA,KAAA,MAAA,CAAA,OAAA,CAAA,gCAAA,CAAA,EAAA;AACA,YAAA,eAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,kCAAA,CAAA;;AACA,YAAA,eAAA,IAAA,eAAA,CAAA,OAAA,EAAA;AACA,cAAA,UAAA,GAAA,eAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,oBAAA;AACA,UAAA,MAAA,CAAA,UAAA,GAAA,UAAA;AACA;AACA,OAhBA,CAkBA;;;AACA,MAAA,mBAAA,CAAA,MAAA,CAAA,CACA,IADA,CACA,UAAA,QAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;;AACA,YAAA,QAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,cAAA,IAAA,GAAA,QAAA,CAAA,IAAA,CADA,CAGA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,KAAA,GAAA,IAAA,CAAA,YAAA,IAAA,EAAA,CAJA,CAMA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,OAAA,GAAA,IAAA,CAAA,cAAA,IAAA,CAAA,CAPA,CASA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,UAAA,GAAA,IAAA,CAAA,gBAAA,IAAA,EAAA,CAVA,CAYA;;AACA,UAAA,MAAA,CAAA,QAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,GAAA,GAAA;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,GAAA,CAAA,CAdA,CAgBA;;AACA,cAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,gCAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,kCAAA;AACA;;AAEA,UAAA,MAAA,CAAA,YAAA;AACA,SAtBA,MAsBA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,UAAA,EAAA,QAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,QAAA,EAFA,CAGA;;;AACA,UAAA,MAAA,CAAA,SAAA;AACA;AACA,OA/BA,EAgCA,KAhCA,CAgCA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,aAAA,EAAA,KAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,cAAA,EAFA,CAGA;;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA;AACA,UAAA,OAAA,EAAA,KAAA,CAAA,OADA;AAEA,UAAA,KAAA,EAAA,KAAA,CAAA,KAFA;AAGA,UAAA,QAAA,EAAA,KAAA,CAAA,QAAA,IAAA;AACA,YAAA,MAAA,EAAA,KAAA,CAAA,QAAA,CAAA,MADA;AAEA,YAAA,UAAA,EAAA,KAAA,CAAA,QAAA,CAAA,UAFA;AAGA,YAAA,IAAA,EAAA,KAAA,CAAA,QAAA,CAAA;AAHA;AAHA,SAAA,EAJA,CAaA;;AACA,QAAA,MAAA,CAAA,SAAA;AACA,OA/CA,EAgDA,OAhDA,CAgDA,YAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAlDA;AAmDA,KArFA;AAsFA;AACA,IAAA,qBAvFA,mCAuFA;AACA,UAAA,eAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,kCAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,eAAA;;AAEA,UAAA,eAAA,IAAA,eAAA,CAAA,OAAA,EAAA;AACA;AACA,YAAA,UAAA,GAAA,eAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,oBAAA,CAFA,CAIA;;AACA,aAAA,SAAA,GALA,CAOA;;AACA,aAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,OAAA,uDAAA,KAAA,gBAAA,CAAA,UAAA,CAAA,0CAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA;AAJA,SAAA;AAMA;AACA,KA1GA;AA2GA;AACA,IAAA,SA5GA,uBA4GA;AACA,WAAA,UAAA,CAAA,KAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,UAAA,CAAA,UAAA,GAAA,CACA;AAAA,QAAA,IAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CAAA;AAMA,WAAA,QAAA,GAAA,GAAA,CATA,CASA;;AACA,WAAA,aAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KAxHA;AAyHA;AACA,IAAA,gBA1HA,4BA0HA,UA1HA,EA0HA;AACA;AACA,UAAA,YAAA,GAAA;AACA,oBAAA,MADA;AAEA,gCAAA,SAFA;AAGA,oCAAA,QAHA;AAIA,8CAAA,SAJA;AAKA,gCAAA,UALA;AAMA,8BAAA,UANA;AAOA,sCAAA,QAPA;AAQA,iCAAA,QARA;AASA,mCAAA,QATA;AAUA,sCAAA,SAVA;AAWA,gCAAA,SAXA;AAYA,qCAAA,YAZA;AAaA,2CAAA,UAbA;AAcA,8BAAA;AAdA,OAAA;AAiBA,aAAA,YAAA,CAAA,UAAA,CAAA,IAAA,MAAA;AACA,KA9IA;AA+IA,IAAA,YA/IA,0BA+IA;AAAA;;AACA;AACA,WAAA,gBAAA,CAAA,SAAA,CAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAIA,QAAA,OAAA,EAAA;AACA,UAAA,OAAA,EAAA;AADA,SAJA;AAOA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,UADA;AAEA,UAAA,IAAA,EAAA,KAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA,CAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,YAAA,EAAA,QAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,SAAA,EAAA;AACA,YAAA,QAAA,EAAA,MADA;AAEA,YAAA,MAAA,EAAA,EAFA;AAGA,YAAA,SAAA,EAAA,mBAAA,KAAA,EAAA;AACA;AACA,kBAAA,IAAA,GAAA,IAAA,IAAA,CAAA,KAAA,CAAA;;AACA,kBAAA,CAAA,KAAA,CAAA,IAAA,CAAA,EAAA;AACA,iCAAA,IAAA,CAAA,QAAA,KAAA,CAAA,mBAAA,IAAA,CAAA,OAAA,EAAA;AACA;;AACA,qBAAA,KAAA;AACA;AAVA;AANA,SAPA;AA0BA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,KAFA;AAGA,UAAA,GAAA,EAAA,CAHA;AAIA,UAAA,GAAA,EAAA;AAJA,SA1BA;AAgCA,QAAA,IAAA,EAAA;AACA,UAAA,MAAA,EAAA,KADA,CACA;;AADA,SAhCA;AAmCA,QAAA,MAAA,EAAA,CAAA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,KAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA,CAHA;AAIA,UAAA,QAAA,EAAA;AACA,YAAA,IAAA,EAAA,CAAA;AACA,cAAA,KAAA,EAAA,EADA;AAEA,cAAA,SAAA,EAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eAFA;AAGA,cAAA,KAAA,EAAA;AAAA,gBAAA,SAAA,EAAA;AAAA;AAHA,aAAA;AADA,WAJA;AAWA,UAAA,SAAA,EAAA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAXA;AAeA,UAAA,MAAA,EAAA;AAfA,SAAA;AAnCA,OAAA,EAFA,CAwDA;;AACA,WAAA,gBAAA,CAAA,SAAA,CAAA;AACA,QAAA,MAAA,EAAA,CAAA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,CAAA;AAAA,YAAA,KAAA,EAAA,KAAA,UAAA,CAAA,OAAA;AAAA,YAAA,IAAA,EAAA;AAAA,WAAA,CAFA;AAGA,UAAA,GAAA,EAAA,CAHA;AAIA,UAAA,GAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA;AACA,YAAA,SAAA,EAAA;AACA,cAAA,KAAA,EAAA,EADA;AAEA,cAAA,KAAA,EAAA,CACA,CAAA,GAAA,EAAA,SAAA,CADA,EAEA,CAAA,GAAA,EAAA,SAAA,CAFA,EAGA,CAAA,CAAA,EAAA,SAAA,CAHA;AAFA;AADA,WALA;AAeA,UAAA,MAAA,EAAA,KAfA;AAeA;AAEA,UAAA,MAAA,EAAA;AACA,YAAA,SAAA,EAAA,UADA;AAEA,YAAA,QAAA,EAAA,EAFA;AAGA,YAAA,UAAA,EAAA,MAHA;AAIA,YAAA,YAAA,EAAA,CAAA,CAAA,EAAA,KAAA,CAJA;AAIA;AACA,YAAA,KAAA,EAAA,MALA;AAKA;AACA,YAAA,UAAA,EAAA,+BANA,CAMA;;AANA,WAjBA;AAyBA,UAAA,KAAA,EAAA;AACA,YAAA,QAAA,EAAA,EADA;AAEA,YAAA,UAAA,EAAA,QAFA;AAGA,YAAA,YAAA,EAAA,CAAA,CAAA,EAAA,KAAA,CAHA;AAGA;AACA,YAAA,KAAA,EAAA;AAJA,WAzBA;AA+BA,UAAA,OAAA,EAAA;AACA,YAAA,KAAA,EAAA,CADA;AACA;AACA,YAAA,MAAA,EAAA,KAFA,CAEA;;AAFA,WA/BA;AAmCA,UAAA,QAAA,EAAA;AACA,YAAA,MAAA,EAAA,CADA;AACA;AACA,YAAA,SAAA,EAAA;AACA,cAAA,KAAA,EAAA,CADA;AACA;AACA,cAAA,KAAA,EAAA,MAFA,CAEA;;AAFA,aAFA;AAMA,YAAA,QAAA,EAAA,CAAA,CANA,CAMA;;AANA,WAnCA;AA2CA,UAAA,SAAA,EAAA;AACA,YAAA,MAAA,EAAA,EADA;AACA;AACA,YAAA,SAAA,EAAA;AACA,cAAA,KAAA,EAAA,CADA;AACA;AACA,cAAA,KAAA,EAAA,MAFA,CAEA;;AAFA,aAFA;AAMA,YAAA,QAAA,EAAA,CAAA,CANA,CAMA;;AANA,WA3CA;AAmDA,UAAA,SAAA,EAAA;AACA,YAAA,QAAA,EAAA,EADA;AACA;AACA,YAAA,QAAA,EAAA,EAFA;AAEA;AACA,YAAA,KAAA,EAAA,MAHA;AAGA;AACA,YAAA,SAAA,EAAA,mBAAA,KAAA,EAAA;AACA;AACA,kBAAA,KAAA,GAAA,EAAA,KAAA,CAAA,EAAA;AACA,uBAAA,KAAA,CAAA,OAAA,CAAA,CAAA,IAAA,EAAA;AACA,eAFA,MAEA;AACA,uBAAA,EAAA,CADA,CACA;AACA;AACA,aAXA;AAYA,YAAA,eAAA,EAAA,0BAZA;AAYA;AACA,YAAA,OAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAbA;AAaA;AACA,YAAA,YAAA,EAAA,CAdA;AAcA;AACA,YAAA,UAAA,EAAA,cAfA,CAeA;;AAfA;AAnDA,SAAA;AADA,OAAA,EAzDA,CAiIA;;AACA,WAAA,qBAAA,CAAA,SAAA,CAAA;AACA,QAAA,OAAA,EAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA;AACA,YAAA,IAAA,EAAA;AADA;AAFA,SADA;AAOA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,UADA;AAEA,UAAA,IAAA,EAAA,KAAA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,IAAA;AAAA,WAAA;AAFA,SAPA;AAWA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,KAFA;AAGA,UAAA,GAAA,EAAA,CAHA;AAIA,UAAA,GAAA,EAAA;AAJA,SAXA;AAiBA,QAAA,MAAA,EAAA,CAAA;AACA,UAAA,IAAA,EAAA,KADA;AAEA,UAAA,IAAA,EAAA,KAAA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,mBAAA;AACA,cAAA,KAAA,EAAA,IAAA,CAAA,KADA;AAEA,cAAA,SAAA,EAAA;AACA,gBAAA,KAAA,EAAA,MAAA,CAAA,cAAA,CAAA,IAAA,CAAA,KAAA;AADA;AAFA,aAAA;AAAA,WAAA,CAFA;AAQA,UAAA,KAAA,EAAA;AACA,YAAA,IAAA,EAAA,IADA;AAEA,YAAA,QAAA,EAAA,KAFA;AAGA,YAAA,SAAA,EAAA;AAHA;AARA,SAAA;AAjBA,OAAA;AAgCA,KAjTA;AAkTA,IAAA,cAlTA,0BAkTA,KAlTA,EAkTA;AACA,UAAA,KAAA,IAAA,EAAA,EAAA;AACA,eAAA,SAAA;AACA,OAFA,MAEA,IAAA,KAAA,IAAA,EAAA,EAAA;AACA,eAAA,SAAA;AACA,OAFA,MAEA;AACA,eAAA,SAAA;AACA;AACA,KA1TA;AA2TA,IAAA,YA3TA,0BA2TA;AACA,WAAA,gBAAA,IAAA,KAAA,gBAAA,CAAA,MAAA,EAAA;AACA,WAAA,gBAAA,IAAA,KAAA,gBAAA,CAAA,MAAA,EAAA;AACA,WAAA,qBAAA,IAAA,KAAA,qBAAA,CAAA,MAAA,EAAA;AACA,KA/TA;AAgUA,IAAA,MAhUA,kBAgUA,UAhUA,EAgUA;AACA,aAAA,UAAA,GAAA,GAAA;AACA,KAlUA;AAmUA,IAAA,cAnUA,4BAmUA;AAAA;;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAFA,CAIA;;AACA,MAAA,oBAAA,CAAA;AACA,QAAA,WAAA,EAAA;AACA,UAAA,cAAA,EAAA,KAAA,UAAA,CAAA,OADA;AAEA,UAAA,gBAAA,EAAA,KAAA,UAAA,CAAA,UAFA;AAGA,UAAA,SAAA,EAAA,KAAA,QAHA;AAIA,UAAA,cAAA,EAAA,KAAA;AAJA;AADA,OAAA,CAAA,CAQA,IARA,CAQA,UAAA,QAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,QAAA,CAAA,OAAA,CAAA,cAAA,CAAA,EADA,CAEA;;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA,SAAA,CAAA,CAHA,CAOA;;AACA,YAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,QAAA,kDAAA,GAAA,CAAA,WAAA,EAAA,SAAA,MAAA,CAAA,GAAA,CAAA,QAAA,KAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,SAAA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,SAAA,MAAA,CAAA,GAAA,CAAA,QAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,SAAA,MAAA,CAAA,GAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,SAAA,MAAA,CAAA,GAAA,CAAA,UAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,UAAA;AAEA,QAAA,MAAA,CAAA,IAAA,EAAA,QAAA,CAAA;;AAEA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,YAAA;AACA,OAtBA,EAuBA,KAvBA,CAuBA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,aAAA,EAAA,KAAA,EADA,CAEA;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA;AACA,UAAA,OAAA,EAAA,KAAA,CAAA,OADA;AAEA,UAAA,KAAA,EAAA,KAAA,CAAA,KAFA;AAGA,UAAA,QAAA,EAAA,KAAA,CAAA,QAAA,IAAA;AACA,YAAA,MAAA,EAAA,KAAA,CAAA,QAAA,CAAA,MADA;AAEA,YAAA,UAAA,EAAA,KAAA,CAAA,QAAA,CAAA,UAFA;AAGA,YAAA,IAAA,EAAA,KAAA,CAAA,QAAA,CAAA;AAHA;AAHA,SAAA;;AASA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,cAAA;AACA,OApCA,EAqCA,OArCA,CAqCA,YAAA;AACA,QAAA,MAAA,CAAA,gBAAA,GAAA,KAAA;AACA,OAvCA;AAwCA;AAhXA;AAtCA,CAAA", "sourcesContent": ["<template>\n  <div class=\"life-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-data-analysis\"></i>\n        寿命预测与健康评估\n        <span class=\"page-subtitle\">Life Prediction & Health Assessment</span>\n      </h2>\n    </div>\n\n    <!-- 健康度展示区域 -->\n    <el-row :gutter=\"20\" class=\"dashboard-row\">\n      <el-col :span=\"16\">\n        <el-card class=\"health-trend-card\">\n          <div slot=\"header\">\n            <span>系统健康度趋势</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"healthTrendChart\" class=\"chart\" />\n            <div v-if=\"!healthData.trend || healthData.trend.length === 0\" class=\"empty-content\">\n              <i class=\"el-icon-data-line\"></i>\n              <p>暂无健康度趋势数据</p>\n              <p class=\"tip\">请先进行故障诊断</p>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"8\">\n        <el-card class=\"gauge-card\">\n          <div slot=\"header\">\n            <span>当前健康度</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"healthGaugeChart\" class=\"chart\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" class=\"dashboard-row\">\n      <el-col :span=\"12\">\n        <!-- RUL预测展示 -->\n        <el-card class=\"rul-card\">\n          <div slot=\"header\">\n            <span>剩余使用寿命预测</span>\n          </div>\n          <div v-loading=\"loading\" class=\"rul-content\">\n            <h2 class=\"rul-value\">{{ rulValue }} 小时</h2>\n            <el-progress :percentage=\"rulPercentage\" :format=\"format\" :color=\"rulColor\"></el-progress>\n            <p class=\"rul-description\">预计剩余使用寿命，基于当前系统运行状态</p>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"12\">\n        <!-- 部件健康状态 -->\n        <el-card class=\"components-card\">\n          <div slot=\"header\">\n            <span>关键部件健康状态</span>\n          </div>\n          <div v-loading=\"loading\" class=\"chart-container\">\n            <div id=\"componentsHealthChart\" class=\"chart\" />\n            <div v-if=\"!healthData.components || healthData.components.length === 0\" class=\"empty-content\">\n              <i class=\"el-icon-data-analysis\"></i>\n              <p>暂无部件健康状态数据</p>\n              <p class=\"tip\">请先进行故障诊断</p>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 报告生成按钮 -->\n    <div class=\"actions-container\">\n      <el-button type=\"primary\" icon=\"el-icon-document\" :loading=\"reportGenerating\" @click=\"generateReport\">\n        生成健康评估报告\n      </el-button>\n      <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"fetchData(true)\">\n        刷新数据\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport { getHealthPrediction, generateHealthReport } from '@/api/health'\nimport { saveAs } from 'file-saver'\n\nexport default {\n  data() {\n    return {\n      healthTrendChart: null,\n      healthGaugeChart: null,\n      componentsHealthChart: null,\n      rulValue: '0',\n      rulPercentage: 0,\n      rulColor: '#409EFF',\n      loading: false,\n      reportGenerating: false,\n      healthData: {\n        trend: [],\n        current: 0,\n        components: []\n      }\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initCharts()\n\n      // 检查是否有从故障诊断页面传来的数据\n      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        this.handleDiagnosisResult()\n      } else {\n        // 如果没有从故障诊断页面传来数据，则显示初始状态\n        this.resetData()\n      }\n    })\n    window.addEventListener('resize', this.resizeCharts)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.resizeCharts)\n    this.healthTrendChart && this.healthTrendChart.dispose()\n    this.healthGaugeChart && this.healthGaugeChart.dispose()\n    this.componentsHealthChart && this.componentsHealthChart.dispose()\n  },\n  methods: {\n    initCharts() {\n      console.log('初始化图表...')\n      this.healthTrendChart = echarts.init(document.getElementById('healthTrendChart'))\n      this.healthGaugeChart = echarts.init(document.getElementById('healthGaugeChart'))\n      this.componentsHealthChart = echarts.init(document.getElementById('componentsHealthChart'))\n\n      // 设置仪表盘容器样式，确保仪表盘居中显示\n      document.getElementById('healthGaugeChart').parentNode.style.display = 'flex'\n      document.getElementById('healthGaugeChart').parentNode.style.justifyContent = 'center'\n      document.getElementById('healthGaugeChart').parentNode.style.alignItems = 'center'\n\n      this.updateCharts()\n      window.addEventListener('resize', this.resizeCharts)\n    },\n    fetchData(isReset = false) {\n      this.loading = true\n      console.log('开始获取健康评估数据... reset:', isReset)\n\n      // 构造请求参数\n      const params = {}\n\n      // 如果是重置请求\n      if (isReset) {\n        params.reset = 'true'\n      } else if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        const diagnosisResult = this.$store.getters['diagnosis/currentDiagnosisResult']\n        if (diagnosisResult && diagnosisResult.success) {\n          const fault_mode = diagnosisResult.diagnosis_details.conclusion.predicted_fault_mode\n          params.fault_mode = fault_mode\n        }\n      }\n\n      // 调用API获取健康评估和寿命预测数据\n      getHealthPrediction(params)\n        .then(response => {\n          console.log('API返回数据:', response)\n          if (response.code === 200) {\n            const data = response.data\n\n            // 更新健康度趋势数据\n            this.healthData.trend = data.health_trend || []\n\n            // 更新当前健康度\n            this.healthData.current = data.current_health || 0\n\n            // 更新部件健康状态数据\n            this.healthData.components = data.component_health || []\n\n            // 更新RUL预测结果，将小数舍入为整数\n            this.rulValue = data.rul_hours ? Math.round(parseFloat(data.rul_hours)) : '0'\n            this.rulPercentage = data.rul_percentage ? Math.round(parseFloat(data.rul_percentage)) : 0\n\n            // 如果是从故障诊断页面跳转来的，标记诊断结果已处理\n            if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n              this.$store.dispatch('diagnosis/processDiagnosisResult')\n            }\n\n            this.updateCharts()\n          } else {\n            console.error('API返回错误:', response)\n            this.$message.error(response.message || '获取数据失败')\n            // 不再调用mockData，改为显示初始状态\n            this.resetData()\n          }\n        })\n        .catch(error => {\n          console.error('获取健康评估数据失败:', error)\n          this.$message.error('获取数据失败，请稍后重试')\n          // 显示详细错误信息\n          console.log('错误详情:', {\n            message: error.message,\n            stack: error.stack,\n            response: error.response && {\n              status: error.response.status,\n              statusText: error.response.statusText,\n              data: error.response.data\n            }\n          })\n          // 不再调用mockData，改为显示初始状态\n          this.resetData()\n        })\n        .finally(() => {\n          this.loading = false\n        })\n    },\n    // 处理从故障诊断页面传来的诊断结果\n    handleDiagnosisResult() {\n      const diagnosisResult = this.$store.getters['diagnosis/currentDiagnosisResult']\n      console.log('处理故障诊断结果:', diagnosisResult)\n\n      if (diagnosisResult && diagnosisResult.success) {\n        // 获取故障模式\n        const fault_mode = diagnosisResult.diagnosis_details.conclusion.predicted_fault_mode\n\n        // 获取健康评估结果\n        this.fetchData()\n\n        // 显示通知\n        this.$notify({\n          title: '诊断结果应用',\n          message: `已根据诊断结果(${this.getFaultModeName(fault_mode)})更新健康评估`,\n          type: 'info',\n          duration: 5000\n        })\n      }\n    },\n    // 重置数据为初始状态\n    resetData() {\n      this.healthData.trend = []\n      this.healthData.current = 0\n      this.healthData.components = [\n        { name: '电机', value: 0 },\n        { name: '控制器', value: 0 },\n        { name: '减速器', value: 0 },\n        { name: '传感器', value: 0 }\n      ]\n      this.rulValue = '0' // 已经是整数\n      this.rulPercentage = 0\n      this.updateCharts()\n    },\n    // 获取故障模式名称\n    getFaultModeName(fault_mode) {\n      // 从故障诊断的faultModeMap获取故障名称\n      const faultModeMap = {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n\n      return faultModeMap[fault_mode] || '未知故障'\n    },\n    updateCharts() {\n      // 更新健康度趋势图表\n      this.healthTrendChart.setOption({\n        title: {\n          text: ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: this.healthData.trend.map(item => item[0]),\n          name: '检测时间点',\n          nameLocation: 'middle',\n          nameGap: 30,\n          axisLabel: {\n            interval: 'auto',\n            rotate: 45,\n            formatter: (value) => {\n              // 只显示月份和日期，不显示年份\n              const date = new Date(value)\n              if (!isNaN(date)) {\n                return `${date.getMonth() + 1}月${date.getDate()}日`\n              }\n              return value\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '健康度',\n          min: 0,\n          max: 100\n        },\n        grid: {\n          bottom: '15%' // 为旋转后的横轴标签留出更多空间\n        },\n        series: [{\n          name: '系统健康度',\n          type: 'line',\n          data: this.healthData.trend.map(item => item[1]),\n          markLine: {\n            data: [{\n              yAxis: 60,\n              lineStyle: { color: '#FF9800' },\n              label: { formatter: '警戒线' }\n            }]\n          },\n          lineStyle: {\n            width: 3,\n            color: '#409EFF'\n          },\n          smooth: true\n        }]\n      })\n\n      // 更新健康度仪表盘\n      this.healthGaugeChart.setOption({\n        series: [{\n          type: 'gauge',\n          data: [{ value: this.healthData.current, name: '健康度' }],\n          min: 0,\n          max: 100,\n          axisLine: {\n            lineStyle: {\n              width: 30,\n              color: [\n                [0.3, '#FF5722'],\n                [0.7, '#FF9800'],\n                [1, '#4CAF50']\n              ]\n            }\n          },\n          radius: '85%', // 缩小仪表盘半径，为刻度标签留出更多空间\n\n          detail: {\n            formatter: '{value}%',\n            fontSize: 28,\n            fontWeight: 'bold',\n            offsetCenter: [0, '70%'], // 将数值放在下方\n            color: '#333', // 使用更深的颜色提高对比度\n            textShadow: '0 0 3px rgba(255,255,255,0.5)' // 添加文字阴影提高可读性\n          },\n          title: {\n            fontSize: 18,\n            fontWeight: 'normal',\n            offsetCenter: [0, '50%'], // 将标题放在数值上方\n            color: '#333'\n          },\n          pointer: {\n            width: 6, // 增加指针宽度提高可见性\n            length: '75%' // 稍微缩短指针，避免与刻度重叠\n          },\n          axisTick: {\n            length: 6, // 减小刻度线长度，避免与指针重叠\n            lineStyle: {\n              width: 2, // 保持刻度线宽度\n              color: '#666' // 加深刻度线颜色\n            },\n            distance: -8 // 向内偏移刻度线，确保不与刻度数字重叠\n          },\n          splitLine: {\n            length: 18, // 增加分割线长度，让刻度标签与分割线有更明显的分隔\n            lineStyle: {\n              width: 3, // 增加分割线宽度\n              color: '#666' // 加深分割线颜色\n            },\n            distance: -3 // 向内偏移分割线，远离刻度数字\n          },\n          axisLabel: {\n            distance: 30, // 进一步增加标签与刻度线的距离\n            fontSize: 12, // 稍微减小字体大小以减少重叠\n            color: '#333', // 加深文字颜色提高对比度\n            formatter: function(value) {\n              // 减少刻度显示，只显示0、20、40、60、80、100的刻度值\n              if (value % 20 === 0) {\n                return value.toFixed(0) + ''\n              } else {\n                return '' // 其他刻度不显示数字\n              }\n            },\n            backgroundColor: 'rgba(255, 255, 255, 0.8)', // 添加半透明背景色\n            padding: [2, 4], // 添加内边距\n            borderRadius: 3, // 圆角边框\n            textShadow: '0 0 2px #fff' // 添加文字阴影提高可读性\n          }\n        }]\n      })\n\n      // 更新部件健康状态柱状图\n      this.componentsHealthChart.setOption({\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        xAxis: {\n          type: 'category',\n          data: this.healthData.components.map(item => item.name)\n        },\n        yAxis: {\n          type: 'value',\n          name: '健康度',\n          min: 0,\n          max: 100\n        },\n        series: [{\n          type: 'bar',\n          data: this.healthData.components.map(item => ({\n            value: item.value,\n            itemStyle: {\n              color: this.getHealthColor(item.value)\n            }\n          })),\n          label: {\n            show: true,\n            position: 'top',\n            formatter: '{c}%'\n          }\n        }]\n      })\n    },\n    getHealthColor(value) {\n      if (value >= 80) {\n        return '#4CAF50'\n      } else if (value >= 60) {\n        return '#FF9800'\n      } else {\n        return '#FF5722'\n      }\n    },\n    resizeCharts() {\n      this.healthTrendChart && this.healthTrendChart.resize()\n      this.healthGaugeChart && this.healthGaugeChart.resize()\n      this.componentsHealthChart && this.componentsHealthChart.resize()\n    },\n    format(percentage) {\n      return percentage + '%'\n    },\n    generateReport() {\n      this.reportGenerating = true\n      console.log('开始生成健康评估报告...')\n\n      // 调用API生成健康评估报告\n      generateHealthReport({\n        health_data: {\n          current_health: this.healthData.current,\n          component_health: this.healthData.components,\n          rul_hours: this.rulValue,\n          rul_percentage: this.rulPercentage\n        }\n      })\n        .then(response => {\n          console.log('报告生成成功, 响应类型:', response.headers['content-type'])\n          // 使用file-saver库将二进制数据保存为Excel文件\n          const blob = new Blob([response.data], {\n            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n          })\n\n          // 生成文件名：健康评估报告_年月日时分秒.xlsx\n          const now = new Date()\n          const fileName = `健康评估报告_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}.xlsx`\n\n          saveAs(blob, fileName)\n\n          this.$message.success('健康评估报告生成成功')\n        })\n        .catch(error => {\n          console.error('生成健康评估报告失败:', error)\n          // 显示详细错误信息\n          console.log('错误详情:', {\n            message: error.message,\n            stack: error.stack,\n            response: error.response && {\n              status: error.response.status,\n              statusText: error.response.statusText,\n              data: error.response.data\n            }\n          })\n          this.$message.error('生成报告失败，请稍后重试')\n        })\n        .finally(() => {\n          this.reportGenerating = false\n        })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.life-container {\n  padding: 20px;\n}\n\n.dashboard-row {\n  margin-bottom: 20px;\n}\n\n.health-trend-card,\n.gauge-card,\n.rul-card,\n.components-card {\n  height: 350px;\n\n  .chart-container {\n    height: 300px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n\n    .chart {\n      width: 100%;\n      height: 100%;\n    }\n\n    .empty-content {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      background-color: rgba(255, 255, 255, 0.8);\n      z-index: 1;\n      text-align: center;\n      color: #909399;\n\n      i {\n        font-size: 48px;\n        margin-bottom: 16px;\n        color: #c0c4cc;\n      }\n\n      p {\n        margin: 5px 0;\n        font-size: 16px;\n      }\n\n      .tip {\n        font-size: 14px;\n        color: #a0a4a9;\n      }\n    }\n  }\n}\n\n.rul-content {\n  padding: 20px;\n  text-align: center;\n\n  .rul-value {\n    font-size: 32px;\n    margin-bottom: 20px;\n    color: #409EFF;\n  }\n\n  .rul-description {\n    margin-top: 20px;\n    color: #606266;\n    font-size: 14px;\n  }\n}\n\n.actions-container {\n  text-align: center;\n  margin-top: 20px;\n\n  .el-button {\n    margin: 0 10px;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/life"}]}